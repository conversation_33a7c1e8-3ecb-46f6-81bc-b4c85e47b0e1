<?php

function cone_filter_products_by_category()
{

	$cat_id = $_POST['cat_id'];

	$query_args = array('posts_per_page' => 12, 'no_found_rows' => 1, 'post_status' => 'publish', 'post_type' => 'product', 'tax_query' => array( 
	   array(
	     'taxonomy' => 'product_cat',
	     'field' => 'id',
	     'terms' => array( $cat_id )
	   )));

	$loop = new WP_Query($query_args);

	if ( $loop->have_posts() ) :
		while ( $loop->have_posts() ) : $loop->the_post();
		global $product;
		?>
		<div class="product-card swiper-slide" <?php post_class(); ?>>
			<div class="product-card-top">
				<a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
				<div class="product-card-icons">
					<i class="material-icons">favorite_border</i>
				</div>
				<?php  echo $product->get_image('full') ; ?>
<!-- 				<div class="product-card-excerpt">
					<p><?php //echo $product->post->post_excerpt ; ?></p>
				</div> -->
			</div>
			<div class="product-card-content">
				<a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
				<p><?php the_title(); ?></p>
				<span class="cd-product-span">exkl.moms <?php echo $product->get_price_html() ; ?></span>
				<?php if ( $product->is_type( 'variable' ) ) : ?>
					<h6 class="cd-product-span">inkl. moms <?php echo $product->get_price_including_tax(1, $product->get_variation_price('min') ) . 'kr-' . $product->get_price_including_tax(1, $product->get_variation_price('max') ); ?> kr</h6>
				<?php else : ?>
					<h6 class="cd-product-span">inkl. moms <?php echo $product->get_price_including_tax( 1, cd_discount_price($product) ); ?>  kr</h6>
				<?php endif; ?>
			</div>
			<div class="product-card-add">
				<p class="<?php if ( $product->is_type( 'variable' ) ) echo 'cd-variable-product'; ?>" data-link="<?php echo get_permalink( $loop->post->ID ); ?>">Lägg till i varukorg</p>
				<?php if ( ! $product->is_type( 'variable' ) ) : ?>
					<div class="add-remove-card" style="display: none;">
					    <i class="material-icons">remove</i>
					    <?php woocommerce_quantity_input(array('input_value' => isset( $quantity ) ? $quantity : 1)) ; ?>
					    <i class="material-icons">add</i>
					</div>
					<a rel="nofollow" href="<?php echo esc_url( $product->add_to_cart_url() ); ?>" data-quantity="<?php echo esc_attr( isset( $quantity ) ? $quantity : 1 ); ?>" data-product_id="<?php echo esc_attr( $product->get_id() ); ?>" data-product_sku="<?php echo esc_attr( $product->get_sku() ); ?>" class="<?php echo esc_attr( isset( $class ) ? $class : 'bitt' ); ?> product_type_simple add_to_cart_button ajax_add_to_cart" style="display: none;">Lägg till</a>
				<?php endif; ?>
			</div>
		</div>
		<?php
		endwhile;
	else :
		?>
		<p>Inga Produkter</p>
	<?php
	endif;
	wp_reset_query();
	//wp_send_json( $products );

	die();
}

add_action('wp_ajax_cone_filter_products_by_category', 'cone_filter_products_by_category');
add_action('wp_ajax_nopriv_cone_filter_products_by_category', 'cone_filter_products_by_category');