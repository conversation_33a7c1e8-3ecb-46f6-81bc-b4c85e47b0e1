<?php 
$hide = false;
if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' && get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ) $hide = true;
?>
<div class="special-fields-container" <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' && get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ) echo 'style="display: none;"'; ?>>
	<div class="two-inputs">
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_venue_type">Typ av lokal*</label>
			<input type="text" id="special_venue_type" class="edit-required" name="special_venue_type" value="<?php echo get_post_meta($order->get_id(), 'special_venue_type', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_county">Stadsdel*</label>
			<input type="text" id="special_county" class="edit-required" name="special_county" value="<?php echo get_post_meta($order->get_id(), 'special_county', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_floor">Våningsplan*</label>
			<input type="text" id="special_floor" class="edit-required" name="special_floor" value="<?php echo get_post_meta($order->get_id(), 'special_floor', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_doorcode">Portkod*</label>
			<input type="text" id="special_doorcode" class="edit-required" name="special_doorcode" value="<?php echo get_post_meta($order->get_id(), 'special_doorcode', true); ?>" disabled />
		</div>
	</div>
	<div class="two-inputs">
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_steps_to_elevator">ANTAL TRAPPSTEG FÖRE ENTRÉ/HISS*</label>
			<input type="text" id="special_steps_to_elevator" class="edit-required" name="special_steps_to_elevator" value="<?php echo get_post_meta($order->get_id(), 'special_steps_to_elevator', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_elevator_size">Storlek på ev.hiss (PERSONER)*</label>
			<input type="text" id="special_elevator_size" class="edit-required" name="special_elevator_size" value="<?php echo get_post_meta($order->get_id(), 'special_elevator_size', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_carry_distance">Bärsträcka FRÅN LASTBIL(CA ANTAL METER)*</label>
			<input type="text" id="special_carry_distance" class="edit-required" name="special_carry_distance" value="<?php echo get_post_meta($order->get_id(), 'special_carry_distance', true); ?>" disabled />
		</div>
	</div>
	<div class="two-inputs">
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_other_contact">Ev. annan kontaktperson på leveransplats</label>
			<input type="text" id="special_other_contact" class="" name="special_other_contact" value="<?php echo get_post_meta($order->get_id(), 'special_other_contact', true); ?>" disabled />
		</div>
		<div class="checkout-input checkout-content checkout-content-first" >
			<label for="special_other_contact_phone">Kontaktpersonens mobilnr</label>
			<input type="text" id="special_other_contact_phone" class="" name="special_other_contact_phone" value="<?php echo get_post_meta($order->get_id(), 'special_other_contact_phone', true); ?>" disabled />
		</div>
	</div>
</div>