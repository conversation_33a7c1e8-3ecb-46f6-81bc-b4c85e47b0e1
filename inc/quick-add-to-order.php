<?php
//Quick add product to order
//function cone_quick_add_item_to_order() {

    $orderId = absint($_POST['order_id']);

    $order = wc_get_order( $orderId );

    if ( get_current_user_id() != $order->get_user_id() ) {
        die();
    }

    //Init array of added products
    $productArray = [];
    
    //If product_id is numeric it means product was quick-added from order page.
    if ( is_numeric( $_POST['product_id'] ) ) {
        //Add product to array with id as key and qty as value
        $productArray[$_POST['product_id']] = 1;
    }
    //Otherwise it was one or more products were added from the cart
    else {
        //Set sent array of products to temp array
        $arrayOfAddedProducts = ($_POST['product_id']);
        //Add products to initiatde productArray vith id as key and qty as value
        foreach ( $arrayOfAddedProducts as $p_id => $qty ) {
            $productArray[$p_id] = $qty;
        }
    }

    //Loop through productArray
    foreach ($productArray as $product_id => $quantity) {
        //Get product object
        $product = wc_get_product( $product_id );
        //If product was found
        if ( $product ) {
            //Init bundle vars
            $bundled_items = [];
            $bundle_qty = '';

            //If product is of type bundle
            if (  $product->is_type( 'yith_bundle' ) ) {
                //Create data-attr with json for HTML
                $bundle_qty = "data-bundleqty='[";
                foreach ($product->bundle_data as $key => $bp) {
                    $bundle_qty .= '{"id":'. intval($bp["product_id"]) .',"qty":'. intval($bp["bp_min_qty"]) .'},';
                    //array_push($bundled_items, $bp["product_id"]);
                    $bundled_items[$bp["product_id"]] = $bp["bp_min_qty"];
                }
                $bundle_qty = rtrim($bundle_qty,", ");
                $bundle_qty .= "]'";
            }

            //Get items in order.
            $items = $order->get_items();

            $exists = 0;
            //Loop through items in the order
            foreach ( $items as $key => $item ) {
                //If the item equals the product we are trying to add
                if ( $item['product_id']  == $product->get_id() ){
                    //Set exist to  current qty of item.
                    $exists = $item['qty'];
                }
            }

            //Check if item already exists in order otherwise send back product html
            if ( $exists === 0 ) {

            ?>    
                <div class="account-table-grid <?php if ( $product->is_type( 'yith_bundle' ) ) echo 'cone-is-bundle'; ?>" data-price="<?php echo ( $product->is_type( 'yith_bundle' ) ) ? $product->price_per_item_tot : $product->get_price(); ?>" <?php echo $bundle_qty; ?>>
                    <div class="account-table-item account-item-img">
                        <?php  echo $product->get_image() ; ?>
                    </div>
                    <div class="account-table-item account-item-product">
                        <a href="<?php echo get_permalink($product->get_id()); ?>"><?php echo $product->get_name(); ?></a>
                    </div>
                    <div class="account-table-item account-item-price">
                        <?php if ( $product->is_type( 'yith_bundle' ) ) : ?>
                            <span><?php echo wc_price($product->price_per_item_tot); ?></span>
                        <?php else : ?>
                            <span><?php echo wc_price($product->get_price()); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="account-table-item account-item-number" <?php if ( $product->is_type( 'yith_bundle' ) ) echo ' data-bpid="'. $product->get_id() .'"'; ?>>
                        <div class="quantity cone-quantity">
                            <input type="number" class="input-text qty text" step="1" min="1" max="" name="cone_quantity" value="<?php echo $productArray[$product->get_id()]; ?>" title="Qty" size="4" pattern="[0-9]*" inputmode="numeric">
                        </div>
                    </div>
                    <div class="account-table-item account-item-total">
                        <?php if ( $product->is_type( 'yith_bundle' ) ) : ?>
                            <h6><?php echo wc_price($product->price_per_item_tot); ?></h6>
                        <?php else : ?>
                            <h6><?php echo wc_price($product->get_price() * $productArray[$product->get_id()]); ?></h6>
                        <?php endif; ?>
                    </div>
                    <div class="account-table-item account-item-remove">
                        <i class="cone-remove-product material-icons" data-id="<?php echo $product->get_id(); ?>">remove_circle_outline</i>
                    </div>
                </div>
            <?php
                if ( count($bundled_items) > 0 ) {
                    $ending = date('His');
                    foreach ($bundled_items as $bp_id => $bp_qty) {
                        $pro = wc_get_product( $bp_id );
                    ?>
                        <div class="account-table-grid cone-bundle-id-<?php echo $product->get_id().'-'.$bp_id; ?> cone-is-bundle-child-<?php echo $ending; ?>" data-belongsto="<?php echo $product->get_id(); ?>" data-price="<?php echo $pro->get_price(); ?>" <?php echo $bundle_qty; ?>>
                            <div class="account-table-item account-item-img">
                                <?php  echo $pro->get_image(); ?>
                            </div>
                            <div class="account-table-item account-item-product">
                                <?php echo sprintf( '<a href="%s">&nbsp;&nbsp;&nbsp;-&nbsp;%s</a>', get_permalink($pro->get_id()), $pro->get_name() ); ?>
                            </div>
                            <div class="account-table-item account-item-price">
                                <span></span>
                            </div>
                            <div class="account-table-item account-item-number">
                                <span><?php echo $bp_qty; ?></span>
                            </div>
                            <div class="account-table-item account-item-total">
                                <h6></h6>
                            </div>
                            <div class="account-table-item account-item-remove">
                                <!-- <i class="cone-remove-product material-icons cone-display-none" data-id="<?php //echo $pro->id; ?>">remove_circle_outline</i> -->
                            </div>
                        </div>
                    <?php
                    }
                }

            }else{

                ?>
                <p class="existing-products-list" style="display: none;"><?php echo $product->get_name(); ?></p>
                <?php
                //json_encode(var_dump($exists));
                //wp_send_json( ['Exists' => true]);

            }
        }
    }
    //return false;

    die();
//}

//add_action('wp_ajax_cone_quick_add_item_to_order', 'cone_quick_add_item_to_order');
//add_action('wp_ajax_nopriv_cone_quick_add_item_to_order', 'cone_quick_add_item_to_order');