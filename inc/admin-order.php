<?php
/**
 * Display field value on the order edit page
 */
add_action( 'woocommerce_admin_order_data_after_billing_address', 'my_custom_checkout_field_display_admin_order_meta', 10, 1 );

function my_custom_checkout_field_display_admin_order_meta($order){

    echo '<h3>Special details</h3>';

    echo '<p><strong>'.__('Kundtyp').':</strong> ' . get_post_meta( $order->id, 'billing_customer_type', true ) . '</p>';

    if ( get_post_meta( $order->id, '_billing_org_nr', true ) ) echo '<p><strong>'.__('Org.nr').':</strong> ' . get_post_meta( $order->id, '_billing_org_nr', true ) . '</p>';

    echo '<p><strong>'.__('Order datum').':</strong> ' . date('Y-m-d', strtotime( get_post_meta( $order->id, 'cone_billing_order_date', true ) ) ) . ' till ' . date('Y-m-d', strtotime( get_post_meta( $order->id, 'cone_billing_return_date', true ) ) ) . '</p>';

    echo '<p><strong>'.__('Hämtar varor').':</strong> ' . get_post_meta( $order->id, 'cone_billing_get_products', true ) . ', ' . get_post_meta( $order->id, 'cone_billing_get_products_time', true ) . '</p>';

    echo '<p><strong>'.__('Lämnar varor').':</strong> ' . get_post_meta( $order->id, 'cone_billing_return_products', true ) . ', ' . get_post_meta( $order->id, 'cone_billing_return_products_time', true ) . '</p>';

    echo '<p><strong>'.__('Typ av lokal').':</strong> ' . get_post_meta( $order->id, 'special_venue_type', true ) . '</p>';

    echo '<p><strong>'.__('Stadsdel').':</strong> ' . get_post_meta( $order->id, 'special_county', true ) . '</p>';

    echo '<p><strong>'.__('Våningsplan').':</strong> ' . get_post_meta( $order->id, 'special_floor', true ) . '</p>';

    echo '<p><strong>'.__('Portkod').':</strong> ' . get_post_meta( $order->id, 'special_doorcode', true ) . '</p>';

    echo '<p><strong>'.__('Ca antal trappsteg före entre/hiss').':</strong> ' . get_post_meta( $order->id, 'special_steps_to_elevator', true ) . '</p>';

    echo '<p><strong>'.__('Storlek på ev. hiss').':</strong> ' . get_post_meta( $order->id, 'special_elevator_size', true ) . '</p>';

    echo '<p><strong>'.__('Bärsträcka, ca').':</strong> ' . get_post_meta( $order->id, 'special_carry_distance', true ) . '</p>';

    if ( get_post_meta( $order->id, 'special_other_contact', true ) ) {
        echo '<p><strong>'.__('Ev. annan kontaktperson på leveransplats').':</strong> ' . get_post_meta( $order->id, 'special_other_contact', true ) . '</p>';
    }
    
    if ( get_post_meta( $order->id, 'special_other_contact_phone', true ) ) {
        echo '<p><strong>'.__('Kontaktpersonens mobilnr').':</strong> ' . get_post_meta( $order->id, 'special_other_contact_phone', true ) . '</p>';
    }

    if ( get_post_meta( $order->id, 'invoice_email', true ) ) echo '<p><strong>'.__('Faktura e-post').':</strong> ' . get_post_meta( $order->id, 'invoice_email', true ) . '</p>';

    if ( get_post_meta( $order->id, 'invoice_email_address', true ) ) echo '<p><strong>'.__('Faktura e-postadress').':</strong> ' . get_post_meta( $order->id, 'invoice_email_address', true ) . '</p>';
}