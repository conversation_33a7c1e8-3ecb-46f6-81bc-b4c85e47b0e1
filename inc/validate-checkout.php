<?php
/**
 * Process the checkout - FILE NOT USED SHOULD BE DELETED, USE checkout-process.php
 */
add_action('woocommerce_checkout_process', 'my_custom_checkout_field_process');

function my_custom_checkout_field_process() {
    //If customertype = företag make company_name and orgnr required
    if ( $_POST['billing_customer_type'] == 'Företag' ) {
        if ( !$_POST['billing_company'] || !$_POST['billing_org_nr'] ){
            wc_add_notice( __( 'Company name och Org nr är obligatoriskt.' ), 'error' );
        }
    }


    //If date is a blocked date.
    // if ( date( 'dmY',strtotime("-2 days") ) < date( 'dmY' ) ) {
        
    // }



    //If get or return product is set to aladdin, make special fields required
    if ( $_POST['cone_billing_get_products'] == 'aladdin' || $_POST['cone_billing_return_products'] == 'aladdin' ) {

        /*
        OBS!!
        Tydligen räknas siffran 0 som att fältet inte är ifyllt.
        */

        if ( $_POST['special_venue_type'] == '' ){
            wc_add_notice( __( 'Typ av lokal är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_county']  == '' ){
            wc_add_notice( __( 'Stadsdel är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_venue_type']  == '' ){
            wc_add_notice( __( 'Typ av lokal är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_floor']  == '' ){
            wc_add_notice( __( 'Våningsplan är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_doorcode']  == '' ){
            wc_add_notice( __( 'Portkod är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_steps_to_elevator']  == '' ){
            wc_add_notice( __( 'Antal trappsteg till hiss är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_elevator_size'] == '' ){
            wc_add_notice( __( 'Storlek på hiss är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['special_carry_distance'] == '' ){
            wc_add_notice( __( 'Bärsträcka från lastbil är obligatoriskt.' ), 'error' );
        }
        if ( $_POST['invoice_email'] == true && $_POST['invoice_email_address'] == '' ){
            wc_add_notice( __( 'E-postadress är obligatoriskt.' ), 'error' );
        }
    }
}