<h1>Blockera Datum</h1>
<div id="mdp-demo"></div>
<input type="hidden" id="altField" value=""/>
<form method="POST" id="cone-blocked-form">


<?php 
	
    //$blocked_dates = get_option('cone_blocked_dates');

    //die( var_dump($blocked_dates) );

	$date_string = '';
	echo '<div class="closed-results">';
	if ( $blocked_dates ) { 
		foreach (json_decode($blocked_dates) as $date => $bothDates) {
		 	$date_string .= "'".$date."',";
		 	//echo '<div><label for="'.$key.'">'.$key.'</label><input id="'.$key.'" name="blocked_date['.$key.']" value="'.$value.'"/></div>';
		 	?>
		 	<div class="closed-date-block">
		 	    <p style="font-weight: 700;"><?php echo $date; ?></p>
		 	    <?php foreach ($bothDates as $label => $text) { ?>
		 	    	    <div>
		 	    	    	<label for="<?php echo $date; ?>"><?php echo $label ?>:</label>
		 	    			<input id="<?php echo $date; ?>" name="blocked_date[<?php echo $date; ?>][<?php echo $label; ?>]" value="<?php echo $text; ?>" style="margin-left: <?php echo $label === 'Hyrdatum' ? '20px' : '8px'; ?>">
		 	    		</div>
		 	    <?php } ?>
		 	</div>
		 	<?php
		}
		$date_string = rtrim($date_string,",");

		$date_string = str_replace(' ', '', $date_string);
	}else{
		echo '<input type="hidden" id="no-dates-input" name="no_dates" value="null" />';
	}

	echo '</div>';


?>



    <?php echo wp_nonce_field( 'wpshout_option_page_example_action' ); ?>
    <input type="submit" value="Save" class="button button-primary button-large">
<!--     <input type="hidden" id="cone_blocked_dates" name="cone_blocked_dates" value="" /> -->
</form>
 <script>
 jQuery(function() {

 	var today = new Date();
 	var y = today.getFullYear();
 	jQuery('#mdp-demo').multiDatesPicker({
 		dateFormat: "dd-mm-yy",
 		<?php if ($date_string) : ?> addDates: [<?php echo $date_string; ?>], <?php endif; ?>
 		numberOfMonths: [3,4],
 		defaultDate: '01-01-'+y,
 		altField: '#altField',
 		onSelect: function(date){
 			test(date);
 		}
 	});


 	jQuery('#cone-blocked-form').on('submit', function(){
 		jQuery('#cone_blocked_dates').val();
 	});

 	function test(date){
 		var exists = false;
 		jQuery('.closed-results input').each(function(){
 			if ( this.id == date ){
 				if( jQuery('.closed-results input').length == 1 ){
 					jQuery('.closed-results').prepend('<input type="hidden" id="no-dates-input" name="no_dates" value="null" />');
 				}
 				jQuery(this).closest('.closed-date-block').remove();//('border', '1px red solid');
 				exists = true;
 			}
 		});

 		if ( !exists ){
 			if ( document.getElementById('no-dates-input') ) document.getElementById('no-dates-input').remove();
 			var html = '<div class="closed-date-block"><p style="font-weight: 700;">'+date+'</p><div><label for="'+date+'-hyr">Hyrdatum: </label><input id="'+date+'" name="blocked_date['+date+'][Hyrdatum]" style="margin-left: 20px;" /></div><label for="'+date+'-retur">Returdatum: </label><input id="'+date+'-retur" name="blocked_date['+date+'][Returdatum]" style="margin-left: 8px;" /><div></div></div>';
 			jQuery('.closed-results').prepend(html);
 		}
 	}

 });
 </script>