<?php
/**
 * Update the order meta with field value
 */
add_action( 'woocommerce_checkout_update_order_meta', 'my_custom_checkout_field_update_order_meta' );

function my_custom_checkout_field_update_order_meta( $order_id ) {
    //Count for emails
    update_post_meta( $order_id, 'cone_order_email_count', 1 );

    if ( isset( $_POST['cone_billing_order_date'] ) ) {
        update_post_meta( $order_id, 'cone_billing_order_date', sanitize_text_field( $_POST['cone_billing_order_date'] ) );
    }

    if ( isset( $_POST['cone_billing_return_date'] ) ) {
        update_post_meta( $order_id, 'cone_billing_return_date', sanitize_text_field( $_POST['cone_billing_return_date'] ) );
    }

    if ( isset( $_POST['cone_billing_get_products'] ) ) {
        update_post_meta( $order_id, 'cone_billing_get_products', sanitize_text_field( $_POST['cone_billing_get_products'] ) );
    }

    if ( isset( $_POST['cone_billing_call_before_delivery'] ) ) {
        update_post_meta( $order_id, 'cone_billing_call_before_delivery', sanitize_text_field( $_POST['cone_billing_call_before_delivery'] ) );
    }

    if ( isset( $_POST['cone_billing_get_products_time'] ) ) {
        update_post_meta( $order_id, 'cone_billing_get_products_time', sanitize_text_field( $_POST['cone_billing_get_products_time'] ) );
    }

    if ( isset( $_POST['cone_billing_return_products'] ) ) {
        update_post_meta( $order_id, 'cone_billing_return_products', sanitize_text_field( $_POST['cone_billing_return_products'] ) );
    }

    if ( isset( $_POST['cone_billing_call_before_return'] ) ) {
        update_post_meta( $order_id, 'cone_billing_call_before_return', sanitize_text_field( $_POST['cone_billing_call_before_return'] ) );
    }

    if ( isset( $_POST['cone_billing_return_products_time'] ) ) {
        update_post_meta( $order_id, 'cone_billing_return_products_time', sanitize_text_field( $_POST['cone_billing_return_products_time'] ) );
    }

    if ( isset( $_POST['billing_customer_type'] ) ) {
        update_post_meta( $order_id, 'billing_customer_type', sanitize_text_field( $_POST['billing_customer_type'] ) );
    }

    if ( isset( $_POST['cone_is_offer'] ) ) {
        update_post_meta( $order_id, 'cone_is_offer', sanitize_text_field( $_POST['cone_is_offer'] ) );
    }

    if ( isset( $_POST['billing_company'] ) ) {
        update_post_meta( $order_id, 'billing_company', sanitize_text_field( $_POST['billing_company'] ) );
    }

    if ( isset( $_POST['billing_org_nr'] ) ) {
        update_post_meta( $order_id, 'billing_org_nr', sanitize_text_field( $_POST['billing_org_nr'] ) );
    }

    if ( isset( $_POST['billing_company_other_name'] ) ) {
        update_post_meta( $order_id, 'billing_company_other_name', sanitize_text_field( $_POST['billing_company_other_name'] ) );
    }

    if ( isset( $_POST['billing_order_code'] ) ) {
        update_post_meta( $order_id, 'billing_order_code', sanitize_text_field( $_POST['billing_order_code'] ) );
    }

    if ( isset( $_POST['special_venue_type'] ) ) {
        update_post_meta( $order_id, 'special_venue_type', sanitize_text_field( $_POST['special_venue_type'] ) );
    }

    if ( isset( $_POST['special_county'] ) ) {
        update_post_meta( $order_id, 'special_county', sanitize_text_field( $_POST['special_county'] ) );
    }
    if ( isset( $_POST['special_floor'] ) ) {
        update_post_meta( $order_id, 'special_floor', sanitize_text_field( $_POST['special_floor'] ) );
    }
    if ( isset( $_POST['special_doorcode'] ) ) {
        update_post_meta( $order_id, 'special_doorcode', sanitize_text_field( $_POST['special_doorcode'] ) );
    }
    if ( isset( $_POST['special_steps_to_elevator'] ) ) {
        update_post_meta( $order_id, 'special_steps_to_elevator', sanitize_text_field( $_POST['special_steps_to_elevator'] ) );
    }
    if ( isset( $_POST['special_elevator_size'] ) ) {
        update_post_meta( $order_id, 'special_elevator_size', sanitize_text_field( $_POST['special_elevator_size'] ) );
    }

    if ( isset( $_POST['special_carry_distance'] ) ) {
        update_post_meta( $order_id, 'special_carry_distance', sanitize_text_field( $_POST['special_carry_distance'] ) );
    }
    if ( isset( $_POST['special_other_contact'] ) ) {
        update_post_meta( $order_id, 'special_other_contact', sanitize_text_field( $_POST['special_other_contact'] ) );
    }
    if ( isset( $_POST['special_other_contact_phone'] ) ) {
        update_post_meta( $order_id, 'special_other_contact_phone', sanitize_text_field( $_POST['special_other_contact_phone'] ) );
    }

    if ( isset( $_POST['invoice_email'] ) ) {
        // Debug: Log what we're receiving
        error_log('Invoice email POST value: ' . print_r($_POST['invoice_email'], true));
        update_post_meta( $order_id, 'invoice_email', sanitize_text_field( $_POST['invoice_email'] ) );
    } else {
        // Debug: Log when checkbox is not set
        error_log('Invoice email checkbox not set in POST data');
    }
    if ( isset( $_POST['invoice_email_address'] ) ) {
        update_post_meta( $order_id, 'invoice_email_address', sanitize_text_field( $_POST['invoice_email_address'] ) );
    }
}
