<?php
add_action('admin_menu', 'cone_blocked_dates_page');

function cone_blocked_dates_page() {
    $page_title = 'Cone Blocked Dates';
    $menu_title = 'Blocked Dates';
    $capability = 'edit_posts';
    $menu_slug = 'cone_block_dates';
    $function = 'cone_block_dates';
    $icon_url = 'dashicons-calendar-alt';
    $position = 24;

    add_menu_page( $page_title, $menu_title, $capability, $menu_slug, $function, $icon_url, $position );
}

function cone_block_dates(){
	if (!current_user_can('manage_options')) {
	    wp_die('Unauthorized user');
	}

    // if (!wp_verify_nonce( '_wp_nonce', 'wpshout_option_page_example_action' )) {
    //     wp_die('Nonce verification failed');
    // }

    if (isset($_POST['blocked_date'])) {
        // var_dump($_POST['blocked_date']);
        // die();
        update_option('cone_blocked_dates', json_encode($_POST['blocked_date']));
    }

    if (isset($_POST['no_dates'])) {
        update_option('cone_blocked_dates', null);
    }

    $blocked_dates = get_option('cone_blocked_dates', null);

	include 'template-blocked-dates.php';
}