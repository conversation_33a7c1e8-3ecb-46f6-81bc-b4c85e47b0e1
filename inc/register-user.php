<?php
function cone_register_user(){
    session_start();

    $first_name = $_POST['register_name'];
    $last_name = $_POST['register_lastname'];
    $user_email = $_POST['register_email'];
    $company = $_POST['register_company'];
    $username = $_POST['register_email'];
    $user_password = $_POST['register_password'];
    $accept_terms = $_POST['accept_terms'];
    $role = 'customer';
    $isCheckoutLogin = $_POST['is_checkout_login'];

    if ( $accept_terms !== '1' ) {
        $_SESSION['accept_error'] = 'Du måste acceptera de allmänna vilkoren.';
        if ( $isCheckoutLogin == 'checkout_login'  ){
            wp_redirect( home_url('/checkout-login') );
        }else{
            wp_redirect( home_url() );
        }
        exit();
    } 

    if (email_exists($user_email)) {
        $_SESSION['email_exists_error'] = 'Email-adressen finns redan i systemet, prova att logga in.';
        if ( $isCheckoutLogin == 'checkout_login'  ){
            wp_redirect( home_url('/checkout-login') );
        }else{
            wp_redirect( home_url() );
        }
        exit();
        
    } elseif (username_exists($username)) {
        $_SESSION['username_exists_error'] = 'Email-adressen finns redan i systemet, prova att logga in.';
        if ( $isCheckoutLogin == 'checkout_login'  ){
            wp_redirect( home_url('/checkout-login') );
        }else{
            wp_redirect( home_url() );
        }
        exit();
    } else {

        $user_id = wc_create_new_customer( $user_email, $username, $user_password );


        update_user_meta( $user_id, "billing_first_name", $first_name );
        update_user_meta( $user_id, "billing_last_name", $last_name );

        if ( $company != '' ) {
            update_user_meta( $user_id, "billing_company", $company );
        }
                
        // $user_id = wp_insert_user(
        //     array(
        //         'user_email' => $user_email,
        //         'user_login' => $username,
        //         'user_pass'  => $user_password,
        //         'user_url'   => '',
        //         'first_name' => $first_name,
        //         'last_name'  => $last_name,
        //         'billing_first_name' => $first_name,
        //         'billing_last_name' => $last_name,
        //         'role'       => $role,
        //     )
        // );

        $_SESSION['fine'] = 'Verkar funka';

        //echo 'done';
    }

    $user = get_user_by( 'id', $user_id );

    if ( $user ) {
        wp_set_current_user( $user_id, $user->user_login );
        wp_set_auth_cookie( $user_id );
        do_action( 'wp_login', $user->user_login, $user);
    }

    if ( $isCheckoutLogin == 'checkout_login'  ) {
        $url = wc_get_checkout_url();
    }
    else{
        $url = esc_url( home_url( '/' ) );
    }

    wp_redirect( $url );

    exit;

}
add_action( 'admin_post_nopriv', 'cone_register_user' );
add_action( 'admin_post_nopriv_cone_register_user', 'cone_register_user' );