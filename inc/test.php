<?php
/**
 * @internal never define functions inside callbacks.
 * these functions could be run multiple times; this would result in a fatal error.
 */
 
/**
 * custom option and settings
 */
function wporg_settings_init() {
 // register a new setting for "wporg" page
 register_setting( 'wporg', 'wporg_options' );
 
 // register a new section in the "wporg" page
 add_settings_section(
	 'wporg_section_developers',
	 __( 'Enter Dates', 'wporg' ),
	 'wporg_section_developers_cb',
	 'wporg'
 );
 
 // register a new field in the "wporg_section_developers" section, inside the "wporg" page
 add_settings_field(
	'wporg_field_pill', // as of WP 4.6 this value is used only internally
	// use $args' label_for to populate the id inside the callback
	__( 'Pill', 'wporg' ),
	'wporg_field_pill_cb',
	'wporg',
	'wporg_section_developers',
	[
	'label_for' => 'wporg_field_pill',
	'class' => 'wporg_row',
	'wporg_custom_data' => 'custom',
	]
	);
}
 
/**
 * register our wporg_settings_init to the admin_init action hook
 */
add_action( 'admin_init', 'wporg_settings_init' );
 
/**
 * custom option and settings:
 * callback functions
 */
 
// developers section cb
 
// section callbacks can accept an $args parameter, which is an array.
// $args have the following keys defined: title, id, callback.
// the values are defined at the add_settings_section() function.
function wporg_section_developers_cb( $args ) {
 ?>
 <p id="<?php echo esc_attr( $args['id'] ); ?>"><?php esc_html_e( 'Click on dates to block them.', 'wporg' ); ?></p>
 <?php
}
 
// pill field cb
 
// field callbacks can accept an $args parameter, which is an array.
// $args is defined at the add_settings_field() function.
// wordpress has magic interaction with the following keys: label_for, class.
// the "label_for" key value is used for the "for" attribute of the <label>.
// the "class" key value is used for the "class" attribute of the <tr> containing the field.
// you can add custom key value pairs to be used inside your callbacks.
function wporg_field_pill_cb( $args ) {
 // get the value of the setting we've registered with register_setting()
 $options = get_option( 'wporg_options' );
 // output the field
 ?>

 <div id="mdp-demo"></div>
 <input type="hidden" id="altField" data-custom="<?php echo esc_attr( $args['wporg_custom_data'] ); ?>"
 name="wporg_options[<?php echo esc_attr( $args['label_for'] ); ?>]" value=""/>

 <?php
}
 
/**
 * top level menu
 */
function wporg_options_page() {
 // add top level menu page
 add_menu_page(
 'Closed Dates',
 'Closed Dates',
 'manage_options',
 'closed-dates',
 'wporg_options_page_html',
 'dashicons-calendar-alt'
 );
}
 
/**
 * register our wporg_options_page to the admin_menu action hook
 */
add_action( 'admin_menu', 'wporg_options_page' );
 
/**
 * top level menu:
 * callback functions
 */
function wporg_options_page_html() {
 // check user capabilities
 if ( ! current_user_can( 'manage_options' ) ) {
 return;
 }
 
 // add error/update messages
 
 // check if the user have submitted the settings
 // wordpress will add the "settings-updated" $_GET parameter to the url
 if ( isset( $_GET['settings-updated'] ) ) {
 // add settings saved message with the class of "updated"
 add_settings_error( 'wporg_messages', 'wporg_message', __( 'Settings Saved', 'wporg' ), 'updated' );
 }
 
 // show error/update messages
 settings_errors( 'wporg_messages' );
 ?>

 <div class="wrap">
 <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
 
 <form action="options.php" method="post">
 <?php
 // output security fields for the registered setting "wporg"
 settings_fields( 'wporg' );
 // output setting sections and their fields
 // (sections are registered for "wporg", each field is registered to a specific section)
 do_settings_sections( 'wporg' );
 // output save settings button
 submit_button( 'Save Settings' );
 $date_string = '';
 $blocked_dates = explode(',', get_option( 'wporg_options' )['wporg_field_pill']);
 foreach ($blocked_dates as $key => $value) {
 	$date_string .= "'".$value."',";
 }
 $date_string = rtrim($date_string,",");

 $date_string = str_replace(' ', '', $date_string);

 ?>
 </form>
 <script>
 jQuery(function() {
 	var today = new Date();
 	var y = today.getFullYear();
 	jQuery('#mdp-demo').multiDatesPicker({
 		dateFormat: "dd-mm-yy",
 		addDates: [<?php echo $date_string; ?>],
 		numberOfMonths: [3,4],
 		defaultDate: '01-01-'+y,
 		altField: '#altField',
 	});
 });
 </script>
 </div>
 <?php
}