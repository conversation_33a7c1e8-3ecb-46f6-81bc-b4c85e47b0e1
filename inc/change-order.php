<?php
/**
*-----------------------------------------------------------------------
*Function to update existing order quantity.
*-----------------------------------------------------------------------
*/

//function prefix_cone_update_order() { ---- comment out function for now
    
    $order_products = $_POST['cone_product'];

    // var_dump($order_products);
    // die();

    $qtys = $_POST['cone_qty'];

    $orderId = absint($_POST['cone_orderID']);

    $order = wc_get_order( $orderId );

    // var_dump($orderId);
    // die();

    if ( get_current_user_id() != $order->get_user_id() ) {
    //if ( true ) {
    	wp_redirect( '/' );
    	exit;
    }

    if ( get_post_meta( $orderId, 'billing_customer_type', true) == 'Privatperson' ) {
        //Array of order info  fields
        $cone_order_fields = [
            '_shipping_first_name',
            '_shipping_last_name',
            '_shipping_address_1',
            '_shipping_postcode',
            '_shipping_city',
            'cone_billing_order_date',
            'cone_billing_return_date',
            'cone_billing_get_products',
            'cone_billing_get_products_time',
            'cone_billing_return_products',
            'cone_billing_return_products_time',
            'special_venue_type',
            'special_county',
            'special_floor',
            'special_doorcode',
            'special_steps_to_elevator',
            'special_elevator_size',
            'special_carry_distance',
            'special_other_contact',
            'special_other_contact_phone'
        ];
    }else {
        //Array of order info  fields
        $cone_order_fields = [
            '_shipping_first_name',
            '_shipping_last_name',
            'billing_company',
            'billing_org_nr',
            'billing_company_other_name',
            'billing_order_code',
            '_shipping_address_1',
            '_shipping_address_2',
            '_shipping_postcode',
            '_shipping_city',
            'cone_billing_order_date',
            'cone_billing_return_date',
            'cone_billing_get_products',
            'cone_billing_get_products_time',
            'cone_billing_return_products',
            'cone_billing_return_products_time',
            'special_venue_type',
            'special_county',
            'special_floor',
            'special_doorcode',
            'special_steps_to_elevator',
            'special_elevator_size',
            'special_carry_distance',
            'special_other_contact',
            'special_other_contact_phone'
        ];
    }

    //Array of order info that has been updated
    $cone_changed_order_info =[];

    //include validation file
    include 'validate-edit-order.php';

    foreach ($cone_order_fields as $field) {
        if ( $_POST[$field] != get_post_meta( $orderId, $field, true ) ){
           update_post_meta( $orderId, $field, sanitize_text_field( $_POST[$field] ) );
           array_push($cone_changed_order_info, $field);
        }
    }

    $sent_order = [];

    //Array of order products that has been updated
    $cone_order_meta = [];


    if ( !empty( $cone_changed_order_info ) ) {
        add_post_meta( $orderId, 'cone_changed_order_info', json_encode($cone_changed_order_info) );
    }


    if ($order_products) {
        foreach ($order_products as $key => $value) {
            $sent_order[$value] = intval($qtys[$key]);
        }
        
        $items = $order->get_items();

        $existing_items = array();

        foreach ( $items as $key => $item ) {
            $existing_items[$key] = $item['qty'];
        }

        $new_products = array_diff_key($sent_order, $existing_items);

        $sent_order_clone = $sent_order;

        if ( count( $new_products ) > 0 ) {
            foreach ($new_products as $key => $value) {
                unset($sent_order_clone[$key]);
            }
        }

        $removed_products = array_diff_key($existing_items, $sent_order);

        $changed_qty_products = array_diff_assoc($sent_order_clone, $existing_items);

        $items = [];

        if ( count($changed_qty_products) > 0 ) {
            foreach ($changed_qty_products as $key => $item) {
                $id = intval(wc_get_order_item_meta($key, '_product_id'));
                $product = wc_get_product( $id );
                if ( $product->is_type( 'variable' ) ) {
                    $variation_id = wc_get_order_item_meta($key, '_variation_id');
                    $v_product = new WC_Product_Variation($variation_id);
                    $product_price = $v_product->get_price();
                }else {
                    $product_price = $product->get_price();
                }
                $line_tax = wc_get_order_item_meta($key, '_line_tax');
                $line_subtotal_tax = $line_tax * $sent_order_clone[$key]; 
                $line_subtotal = $product_price * $sent_order_clone[$key];
                wc_update_order_item_meta( $key, '_qty', $sent_order_clone[$key] );
                if ( !$product->is_type( 'yith_bundle' ) ) {
                   wc_update_order_item_meta( $key, '_line_subtotal_tax', $line_subtotal_tax );
                   wc_update_order_item_meta( $key, '_line_tax', $line_subtotal_tax );
                   wc_update_order_item_meta( $key, '_line_subtotal', $line_subtotal );
                   wc_update_order_item_meta( $key, '_line_total', $line_subtotal );
                }

                //If product is bundle
                if ( $product->is_type( 'yith_bundle' ) ) {
                    //Get bundle tax data and total
                    $bundleParent = wc_get_order_item_meta($key, '_yith_bundle_cart_key');
                    $bundle_totals = wc_get_order_item_meta($key, '_yith_bundle_totals');
                    
                    //Init tax and totals vars
                    $line_tax = 0.00;
                    $line_total = 0.00;
                    $line_tax_data_12 = 0.00;
                    $line_tax_data_25 = 0.00;
                    $line_tax_data = [];

                    //Loop through items in order
                    foreach ( $order->get_items() as $item_id => $item ) {
                        //If current item is a bundle child and is in current bundle
                        if ( wc_get_order_item_meta($item_id, '_bundled_by') && wc_get_order_item_meta($item_id, '_bundled_by') == $bundleParent ) {
                            //Get product from item
                            $b_prod = $item->get_product();
                            //Loop through bundle data
                            foreach ($product->bundle_data as $bd) {
                                //if bundled product matches with order item product
                                if ( $b_prod->get_id() == $bd['product_id'] ) {
                                    //$bundle_totals['']
                                    $bdQty = $bd['bp_min_qty'] * $sent_order_clone[$key];
                                    $current_qty = wc_get_order_item_meta($item_id, '_qty');
                                    $current_total = wc_get_order_item_meta($item_id, '_line_total');
                                    $l_tax = wc_get_order_item_meta($item_id, '_line_tax');
                                    $new_total = ($current_total / $current_qty) * $bdQty;
                                    $new_tax = ($l_tax / $current_qty) * $bdQty;

                                    $line_total = $line_total + ($b_prod->get_price() * $bdQty);

                                    if ($b_prod->get_tax_class() === 'reduced-rate'){
                                        $line_tax_data_12 = $line_tax + ($b_prod->get_price() * 0.12 * $bdQty);
                                    }else{
                                        $line_tax_data_25 = $line_tax + ($b_prod->get_price() * 0.25 * $bdQty);
                                    }

                                    $line_tax_data['total'][1] = $line_tax_data_25; 

                                    if ( $line_tax_data_12 != 0 ) {
                                        $line_tax_data['total'][2] = $line_tax_data_12;
                                    }

            
                                    // wc_update_order_item_meta( $item_id, '_line_tax', $new_tax );
                                    // wc_update_order_item_meta( $item_id, '_line_subtotal_tax', $new_tax );
                                    // wc_update_order_item_meta( $item_id, '_line_total', $new_total );
                                    // wc_update_order_item_meta( $item_id, '_line_subtotal', $new_total );
                                    wc_update_order_item_meta( $item_id, '_qty', $bdQty );
                                    wc_add_order_item_meta($item_id, '_cone_changed_item', 'qty,'. $current_qty .',' . $bdQty);
                                    array_push($cone_order_meta, $item_id);
                                }
                            }
                        }
                    }
                    $yith_bundle_data = [
                        'line_tax' => ($line_tax_data_12 + $line_tax_data_25),
                        'line_subtotal_tax' => ($line_tax_data_12 + $line_tax_data_25),
                        'line_tax_data' => $line_tax_data,
                        'line_total' => $line_total,
                        'line_subtotal' => $line_total
                    ];
                    wc_update_order_item_meta( $key, '_yith_bundle_totals', $yith_bundle_data );
                }

                //Add cone order meta            
                wc_add_order_item_meta($key, '_cone_changed_item', 'qty,'. $existing_items[$key] .',' . $sent_order_clone[$key]);
                array_push($cone_order_meta, $key);

                array_push($items, $key); 
            }
        }

        //Dont remove product just mark it for mail
        if ( count($removed_products) > 0 ) {
            foreach ($removed_products as $meta_key => $remove_item) {
                //$id = intval(wc_get_order_item_meta($meta_key, '_product_id'));
                //$pr = wc_get_product( $id );

                // $bundle_key = wc_get_order_item_meta($meta_key, '_yith_bundle_cart_key');

                // var_dump( $meta_key );                
                wc_update_order_item_meta($meta_key, '_cone_changed_item', 'removed');
                array_push($cone_order_meta, $meta_key);
            }
        }

        $bundled_items_ids = [];

        if ( count($new_products) > 0 ) {
            foreach ($new_products as $key => $new_product) {
                //$id = intval(wc_get_order_item_meta($key, '_product_id'));
                $product = wc_get_product( $key );
                //var_dump($product);
                $item_id = $order->add_product($product, $sent_order[$key]);
                if ( $product->is_type( 'yith_bundle' ) ) {

                    $cartstamp = [];
                    $b_rand = bin2hex(random_bytes(16));
                    $bundle = [];
                    $bundle_totals = [
                        'line_tax' => 0,
                        'line_subtotal_tax' => 0,
                        'line_tax_data' => [
                            'total' => []
                        ],
                        'line_total' => 0,
                        'line_subtotal' => 0
                    ];

                    $t12 = [];
                    $t25 = [];
                    $ex_price = [];

                    foreach ($product->bundle_data as $b_key => $bp) {
                        $bundle_rand = bin2hex(random_bytes(16));
                        $bundle[$b_key] = $bundle_rand;
                        $real_qty = $bp['bp_min_qty'] * $sent_order[$key];
                        $p = [
                            "product_id" => $bp['product_id'],
                            "type" => "simple",
                            "quantity" => $bp['bp_min_qty'],
                            "hide_thumbnail" => false,
                            "title" => $bp['bp_title'],
                            "discount" => "0"
                        ];
                        $pro = wc_get_product( $bp['product_id'] );
                        $tax = get_post_meta($bp['product_id'], '_tax_class', true) ? '12' : '25';
                        $price_excl_tax = wc_get_price_excluding_tax($pro);
                        $price_incl_tax = wc_get_price_including_tax($pro);
                        $tax_amount = $price_incl_tax - $price_excl_tax;
                        $ep = $price_excl_tax * $real_qty;
                        array_push($ex_price, $ep);

                        $bundle_totals['line_tax'] = $bundle_totals['line_tax'] + ($tax_amount * $real_qty);
                        $bundle_totals['line_subtotal_tax'] = $bundle_totals['line_tax'];
                        ( $tax === '12') ? array_push($t12, $tax_amount * $real_qty) : array_push($t25, $tax_amount * $real_qty);

                        $b_item_id = $order->add_product($pro, $real_qty);
                        array_push($bundled_items_ids, $b_item_id);

                        wc_add_order_item_meta( $b_item_id, '_bundled_by', $b_rand );
                        wc_add_order_item_meta( $b_item_id, '_yith_wcpb_hidden', '' );

                        array_push($cartstamp, $p);
                    }

                    $bundle_totals['line_tax_data']['total'][1] = array_sum($t25);
                    if ( count($t12) > 0 ) $bundle_totals['line_tax_data']['total'][2] = array_sum($t12);
                    $bundle_totals['line_total'] = array_sum($ex_price);
                    $bundle_totals['line_subtotal'] = $bundle_totals['line_total'];
                    $bundle_totals_total = $bundle_totals['line_total'] + $bundle_totals['line_tax'];
                    //$cartstamp = maybe_serialize($cartstamp);
                    //$bundle = maybe_serialize($bundle);
                    wc_update_order_item_meta( $item_id, '_line_subtotal_tax', 0 );
                    wc_update_order_item_meta( $item_id, '_line_tax', 0 );
                    wc_update_order_item_meta( $item_id, '_line_subtotal', 0 );
                    wc_update_order_item_meta( $item_id, '_line_total', 0 );
                    wc_add_order_item_meta($item_id, '_cartstamp', $cartstamp);
                    wc_add_order_item_meta($item_id, '_yith_bundle_cart_key', $b_rand);
                    wc_add_order_item_meta($item_id, '_bundled_items', $bundle);
                    wc_add_order_item_meta($item_id, '_per_items_pricing', 'yes');
                    wc_add_order_item_meta($item_id, '_non_bundled_shipping', 'no');
                    wc_add_order_item_meta($item_id, '_yith_bundle_totals', $bundle_totals);
                    wc_add_order_item_meta($item_id, '_yith_bundle_totals_total', $bundle_totals_total);
                } 
                wc_add_order_item_meta($item_id, '_cone_changed_item', 'added');

                array_push($cone_order_meta, $item_id);

                array_push($items, $item_id);
            }
        }

        $updated_order = wc_get_order( $orderId );
        $updated_order->calculate_totals();

    }

    /*
    ** Apply coupon discounts - start
    ** ------------------------------
    ** only tested with percentage discounts
    ** @todo - test with fixed discounts
    */

    // Retrieve applied coupons
    if (method_exists($updated_order, 'get_coupon_codes')) {
        // For newer WC versions
        $applied_coupons = $updated_order->get_coupon_codes();
    } else {
        // For WC 3.2.6
        $applied_coupons = [];
        foreach ($updated_order->get_items('coupon') as $coupon_item) {
            $applied_coupons[] = $coupon_item['name']; // Coupon codes are stored under 'name'
        }
    }

    if (!empty($applied_coupons)) {
        foreach ($applied_coupons as $coupon_code) {

            // Check if the coupon exists
            $coupon_id = wc_get_coupon_id_by_code($coupon_code);
            if (!$coupon_id) {
                continue; // Skip if the coupon does not exist
            }

            $coupon = new WC_Coupon($coupon_code);

            // Backward-compatible check for coupon expiration
            $expiration_date = null;
            if (method_exists($coupon, 'get_date_expires')) {
                // For newer WC versions
                $expiration_date = $coupon->get_date_expires();
                $is_expired = $expiration_date && $expiration_date->getTimestamp() < time();
            } else {
                // For WC 3.2.6
                $expiration_date_meta = get_post_meta($coupon_id, '_date_expires', true);
                $is_expired = $expiration_date_meta && strtotime($expiration_date_meta) < time();
            }
            if ($is_expired) {
                continue; // Skip if the coupon is expired
            }

            // Retrieve the discount type and amount
            $discount_type = $coupon->get_discount_type();
            $discount_amount = $coupon->get_amount();

            // Check if the coupon excludes sale items
            $exclude_sale_items = $coupon->get_exclude_sale_items();

            // Loop through order items to apply discounts
            foreach ($updated_order->get_items() as $item_id => $item) {
                $product_id = $item['product_id'];
                $product = wc_get_product($product_id); // Get product object

                if ($product && $product->get_price() > 0) {

                    // Check if product has a sale price and the coupon excludes sale items
                    if ($exclude_sale_items && $product->is_on_sale()) {
                        continue; // Skip discounting this product if it's on sale and the coupon excludes sale items
                    }

                    $line_total = isset($item['line_subtotal']) ? $item['line_subtotal'] : 0; // Total before discount
                    $current_discount = $line_total - (isset($item['line_total']) ? $item['line_total'] : $line_total); // Already applied discount

                    foreach ($applied_coupons as $coupon_code) {
                        $coupon = new WC_Coupon($coupon_code);

                        // Retrieve the discount type and amount
                        $discount_type = $coupon->get_discount_type();
                        $discount_amount = $coupon->get_amount();

                        // Calculate the expected discount
                        if ($discount_type === 'percent') {
                            $expected_discount = ($line_total * $discount_amount) / 100;
                        } elseif ($discount_type === 'fixed_cart') {
                            $expected_discount = ($discount_amount / $updated_order->get_item_count());
                        } elseif ($discount_type === 'fixed_product') {
                            $expected_discount = $discount_amount * $item['qty']; // Fixed per item
                        } else {
                            $expected_discount = 0;
                        }

                        // Ensure no stacking by subtracting the already applied discount
                        $new_discount = max(0, $expected_discount - $current_discount);

                        // Handle floating point precision errors (if the new discount is too small, set it to 0)
                        if (abs($new_discount) < 0.01) {
                            $new_discount = 0;
                        }

                        // Apply the new discount
                        if ($new_discount > 0) {
                            $new_total = $line_total - $new_discount;

                            // Get the tax rate (using get_post_meta to fetch tax class)
                            $tax_class = get_post_meta($product_id, '_tax_class', true); // Get the tax class

                            // Return 12% or 25% based on the tax class
                            $tax_rate = $tax_class ? 12 : 25; // If tax class exists, 12% else 25%

                            // Update line total and tax
                            $item['line_total'] = $new_total;
                            $item['line_tax'] = ($new_total * $tax_rate) / 100;

                            // Update the order item
                            wc_update_order_item_meta($item_id, '_line_total', $new_total);
                            wc_update_order_item_meta($item_id, '_line_tax', ($new_total * $tax_rate) / 100);
                        }
                    }
                }
            }

        }

        // Save updated order items
        $updated_order->save();

        // Recalculate totals and taxes
        $updated_order->calculate_totals();
        $updated_order->update_taxes();
    }

    /*
    ** Apply coupon discounts - end
    ** ------------------------------
    */

    $url = esc_url( $order->get_view_order_url() );

    //Increment email count
    $currentCount = get_post_meta($orderId, 'cone_order_email_count', true);
    $count = ( is_numeric( $currentCount ) ) ? get_post_meta($orderId, 'cone_order_email_count', true) : 1;
    $new_count = $count;
    if ( $count == 0 ) {
        $new_count = 2;
    }else{
        $new_count = $count + 1;
    }
    update_post_meta( $orderId, 'cone_order_email_count', $new_count );

    //New mail function
    $mailer = WC()->mailer();
    $mails = $mailer->get_emails();
    if ( ! empty( $mails ) ) {
        foreach ( $mails as $mail ) {
            if ( $mail->id == 'customer_completed_order' ) {
                $mail->trigger( $orderId );
            }
        }
    }

    foreach ($cone_order_meta as $item_id) {
        wc_delete_order_item_meta( $item_id, '_cone_changed_item' );
    }

    // //Now remove product
    if ( count($removed_products) > 0 ) {
        foreach ($removed_products as $meta_key => $remove_item) {
            wc_delete_order_item( $meta_key );
        }
    }

    delete_post_meta( $orderId, 'cone_changed_order_info' );

    $final_updated_order = wc_get_order( $orderId );

    $final_updated_order->calculate_totals();
    $final_updated_order->update_taxes();

    wp_redirect( $url );
    exit;
//} -- function end


// add_action( 'wp_ajax', 'prefix_cone_update_order' );
// add_action( 'wp_ajax_cone_update_order', 'prefix_cone_update_order' );
// add_action( 'admin_post', 'prefix_cone_update_order' );
// add_action( 'admin_post_cone_update_order', 'prefix_cone_update_order' );