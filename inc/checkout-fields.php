<?php
add_filter( 'woocommerce_checkout_fields', 'cone_add_checkout_fields' );

function cone_add_checkout_fields( $fields ) {
    
    //----------------Billing--------------------------------------------- 

    // Change all attributes on a field
    $fields['billing']['billing_customer_type'] = array(
        'label'     => __('Customer Type', 'woocommerce'),
        'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array('cone-customer-type'),
        'clear'     => true,
    );

    $fields['billing']['cone_is_offer'] = array(
        'label'     => __('Endast offert', 'woocommerce'),
        'placeholder'   => _x('Endast offert', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-is-offer'),
        'clear'     => true,
        'default'   => 'false'
    );

    $fields['billing']['billing_org_nr'] = array(
        'label'     => __('Org.nr (Måste anges om faktura begärs)', 'woocommerce'),
        'placeholder'   => _x('Org.nr', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-org-nr'),
        'clear'     => true,
    );

    $fields['billing']['billing_company'] = array(
        'label'     => __('Inregistrerat företagsnamn*', 'woocommerce'),
        'placeholder'   => _x('Inregistrerat företagsnamn', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-company-name'),
        'clear'     => true,
    );

    $fields['billing']['billing_company_other_name'] = array(
        'label'     => __('Om annat arbetsnamn på företaget används', 'woocommerce'),
        'placeholder'   => _x('Om annat arbetsnamn på företaget används', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-company-other-name'),
        'clear'     => true,
    );

    $fields['billing']['billing_order_code'] = array(
        'label'     => __('Eventuell märkning av beställningen', 'woocommerce'),
        'placeholder'   => _x('Eventuell märkning av beställningen', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-company-order-code'),
        'clear'     => true,
    );

    $fields['billing']['billing_address_2'] = array(
        'label'     => __('Box adress (viktigt om det finns)', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    //Hidden new billing-fields
    $fields['billing']['cone_billing_order_date'] = array(
        'label'     => __('Order date', 'woocommerce'),
        'placeholder'   => _x('Order date', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array('cone-customer-type'),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_return_date'] = array(
        'label'     => __('Return date', 'woocommerce'),
        'placeholder'   => _x('Return date', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array('cone-customer-type'),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_get_products'] = array(
        'label'     => __('Get products', 'woocommerce'),
        'placeholder'   => _x('Get products', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_get_products_time'] = array(
        'label'     => __('Get products time', 'woocommerce'),
        'placeholder'   => _x('Get products time', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_call_before_delivery'] = array(
        'type'      => 'hidden',
        'label'     => __('Call 30 min before delivery?', 'woocommerce'),
        'placeholder'   => _x('Call 30 min before delivery?', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
        'default'   => 'Nej',
    );

    $fields['billing']['cone_billing_return_products'] = array(
        'label'     => __('Return Product', 'woocommerce'),
        'placeholder'   => _x('Return Product', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_return_products_time'] = array(
        'label'     => __('Return Product time', 'woocommerce'),
        'placeholder'   => _x('Return Product time', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['billing']['cone_billing_call_before_return'] = array(
        'type'      => 'hidden',
        'label'     => __('Call 30 min before return?', 'woocommerce'),
        'placeholder'   => _x('Call 30 min before return?', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
        'default'   => 'Nej',
    );

    $fields['billing']['billing_other_phone'] = array(
        'label'     => __('Annan telefon', 'woocommerce'),
        'placeholder'   => _x('Annan telefon', 'placeholder', 'woocommerce'),
        'required'  => true,
        'class'     => array(),
        'clear'     => true,
    );

    // Move these around as necessary.
    $billing_order = array(
        "cone_billing_order_date",
        "cone_billing_return_date",
        "cone_billing_get_products",
        "cone_billing_get_products_time",
        "cone_billing_call_before_delivery",
        "cone_billing_return_products",
        "cone_billing_return_products_time",
        "cone_billing_call_before_return",
        "billing_customer_type",
        "cone_is_offer",
        "billing_first_name", 
        "billing_last_name", 
        "billing_company",
        "billing_org_nr",
        "billing_company_other_name",
        "billing_order_code",
        "billing_address_1", 
        "billing_address_2", 
        "billing_postcode", 
        "billing_city",
        "billing_phone",
        "billing_other_phone",
        "billing_email"
    );

    // This sets the billing fields in the order above
    foreach($billing_order as $billing_field) {
        $billing_fields[$billing_field] = $fields["billing"][$billing_field];
    }

    $fields["billing"] = $billing_fields;


    //------------Shipping-----------------------------------------------------

    $fields['shipping']['shipping_address_2'] = array(
        'label'     => __('Box adress(viktigt om det finns)', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['shipping']['shipping_org_nr'] = array(
        'label'     => __('Organisationsnummer*', 'woocommerce'),
        'placeholder'   => _x('Org.nr', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array('cone-org-nr'),
        'clear'     => true,
    );

    // Move these around as necessary. You'll see we added email first.
    $shipping_order = array(
        "shipping_first_name", 
        "shipping_last_name", 
        "shipping_address_1", 
        //"shipping_address_2", 
        "shipping_postcode", 
        "shipping_city",
    );

    // This sets the shipping fields in the order above
    foreach($shipping_order as $shipping_field) {
        $shipping_fields[$shipping_field] = $fields["shipping"][$shipping_field];
    }

    $fields["shipping"] = $shipping_fields;

    //----------------------Other-----------------------------------------------
    $fields['order']['special_venue_type'] = array(
        'label'     => __('Typ av lokal', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_venue_other'] = array(
        'label'     => __('Specifiera annan*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_county'] = array(
        'label'     => __('Stadsdel*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_floor'] = array(
        'label'     => __('Våningsplan*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_doorcode'] = array(
        'label'     => __('Portkod*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_steps_to_elevator'] = array(
        'label'     => __('Antal trappsteg före entré / hiss*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_elevator_size'] = array(
        'label'     => __('Storlek på ev. hiss (personer)*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_carry_distance'] = array(
        'label'     => __('Bärsträcka från lastbil (ca antal meter)*', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_other_contact'] = array(
        'label'     => __('Ev. annan kontaktperson på leveransplats', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['special_other_contact_phone'] = array(
        'label'     => __('Kontaktpersonens mobilnr', 'woocommerce'),
        //'placeholder'   => _x('C type', 'placeholder', 'woocommerce'),
        'required'  => false,
        'class'     => array(),
        'clear'     => true,
    );

    $fields['order']['order_comments'] = array(
        'label'     => __('Ditt meddelande till Aladdin', 'woocommerce'),
    );

    // Move these around as necessary. You'll see we added email first.
    $special_order = array(
        "special_venue_type", 
        "special_venue_other", 
        "special_county",
        "special_floor",
        "special_doorcode", 
        "special_steps_to_elevator", 
        "special_elevator_size", 
        "special_carry_distance", 
        "special_other_contact", 
        "special_other_contact_phone", 
        "order_comments",
    );

    // This sets the shipping fields in the order above
    foreach($special_order as $special_field) {
        $order_fields[$special_field] = $fields["order"][$special_field];
    }

    $fields["order"] = $order_fields;


   return $fields;
}