<?php
//Ajax add product to cart
function cone_wc_add_cart_ajax() {

    $product_id = intval($_POST['product_id']);
    $variation_id = intval($_POST['variation_id']);
    $quantity = intval($_POST['quantity']);

    // var_dump($quantity);

    // die();

    if ($variation_id) {
    WC()->cart->add_to_cart( $product_id, $quantity, $variation_id );
    } else {
    WC()->cart->add_to_cart( $product_id, $quantity);
    }

    woocommerce_mini_cart();

    exit();
}

add_action('wp_ajax_cone_wc_add_cart', 'cone_wc_add_cart_ajax');
add_action('wp_ajax_nopriv_cone_wc_add_cart', 'cone_wc_add_cart_ajax');


//Remove from cart
function cone_remove_item_from_cart() {

    $cart = WC()->cart;

    foreach ($cart->get_cart() as $cart_item_key => $cart_item){
        if($cart_item['product_id'] == 7159 ){
            // Remove product in the cart using  cart_item_key.
            $cart->remove_cart_item($cart_item_key);
            $cart_count = $cart->get_cart_contents_count();
            $cart_total = $cart->get_cart_total();
            $response = ['0' => $cart_count, '1' => $cart_total]; 
        }
    }

    if ( ! isset($response) ) $response = 'error';

    wp_send_json( $response );

    //return false;

    die();
}

//add_action('wp_ajax_cone_remove_item_from_cart', 'cone_remove_item_from_cart');
//add_action('wp_ajax_nopriv_cone_remove_item_from_cart', 'cone_remove_item_from_cart');