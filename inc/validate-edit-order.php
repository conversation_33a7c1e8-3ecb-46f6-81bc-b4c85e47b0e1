<?php

/**
	Validate that required fields are correctly filled on changed order
*/

$validation_fails = false;

if ( get_post_meta($order->get_id(), 'billing_customer_type', true) == 'Privatperson' ) {
	$required = [
		'_shipping_first_name' => 'Förnamn',
		'_shipping_last_name' => 'Efternamn',
		'_shipping_address_1' => 'Adress',
		'_shipping_postcode' => 'Postkod',
		'_shipping_city' => 'Stad',
		'cone_billing_order_date' => 'Hyrdatum',
		'cone_billing_return_date' => 'Returdatum',
		'cone_billing_get_products' => 'Hämtar varor',
		'cone_billing_get_products_time' => 'Hämtar varor tid',
		'cone_billing_return_products' => 'Lämnar varor',
		'cone_billing_return_products_time' => 'Lämnar varor tid',
	];
}else {
	$required = [
		'_shipping_first_name' => 'Förnamn',
		'_shipping_last_name' => 'Efternamn',
		'_shipping_address_1' => 'Adress',
		'_shipping_postcode' => 'Postkod',
		'_shipping_city' => 'Stad',
		'billing_company' => 'Företagsnamn',
		'billing_org_nr' => 'Org nr',
		'cone_billing_return_date' => 'Returdatum',
		'cone_billing_get_products' => 'Hämtar varor',
		'cone_billing_get_products_time' => 'Hämtar varor tid',
		'cone_billing_return_products' => 'Lämnar varor',
		'cone_billing_return_products_time' => 'Lämnar varor tid',
	];
}

if ( $_POST['cone_billing_get_products'] === 'aladdin' || $_POST['cone_billing_return_products'] === 'aladdin' ) {
	$required['special_venue_type'] = 'Type av lokal';
	$required['special_county'] = 'Stadsdel';
	$required['special_floor'] = 'Våningsplan';
	$required['special_doorcode'] = 'Portkod';
	$required['special_steps_to_elevator'] = 'Antal trappsteg före hiss';
	$required['special_elevator_size'] = 'Storlek på ev.hiss';
	$required['special_carry_distance'] = 'Bärsträcka';
	$required['special_other_contact'] = 'Ev annan kontaktperson på leveransplats';
	$required['special_other_contact_phone'] = 'Kontaktpersonens mobilnummer';
}

if ( $_POST['cone_billing_order_date'] > $_POST['cone_billing_return_date'] ) {
	$validation_fails = true;
}


foreach ($required as $field => $name) {
	if ( $_POST[$field] == '' ) {
		$validation_fails = true;
		// global $cone_error_edit;
		// $cone_error_edit = new WP_Error();
		// $cone_error_edit->add('cone_edit_failed', $name .' är ifyllt fel.');
	}
}

if ( $validation_fails ){
	$url = esc_url( $order->get_view_order_url() );

	wp_redirect( $url );
	exit;
}

