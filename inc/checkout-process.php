<?php
/**
 * Checkout Process
 */

add_action('woocommerce_checkout_process', 'customise_checkout_field_process');

function customise_checkout_field_process()
{
	wc_clear_notices();
	//If required fields are missing
	$required = [
		'Hyrdatum' => 'cone_billing_order_date',
		'Returdatum' => 'cone_billing_return_date',
		'Leveranstyp' => 'cone_billing_get_products',
		'Leveranstid' => 'cone_billing_get_products_time',
		'Returtyp' => 'cone_billing_return_products',
		'Returtid' => 'cone_billing_return_products_time',
		'Kundtyp' => 'billing_customer_type',
		'Förnamn' => 'billing_first_name',
		'Efternamn' => 'billing_last_name',
		'Adress' => 'billing_address_1',
		'Postkod' => 'billing_postcode',
		'Stad' => 'billing_city',
	];
	foreach ($required as $key => $field) {
		if ( ! $_POST[$field] ) {
			wc_add_notice( __($key.' kan inte vara tomt.') , 'error' );
		}
	}

	//If org number/companyname is missing && customer_type = company
	if ( $_POST['billing_customer_type'] == 'Företag' ) {
		if ( ! $_POST['billing_company'] ) {
			wc_add_notice( __('Företagsnamn kan inte vara tomt.') , 'error' );
		}

		// if ( ! $_POST['billing_org_nr'] ) {
		// 	wc_add_notice( __('Org.nr kan inte vara tomt.') , 'error' );
		// }
	}

	//If dates dont work
	if ( date('Ymd', strtotime($_POST['cone_billing_order_date'])) > date('Ymd', strtotime($_POST['cone_billing_return_date'])) ) {
		wc_add_notice( __($_POST['cone_billing_order_date'] . ' Hyrdatum måste vara tidigare än returdatum.' . $_POST['cone_billing_return_date']) , 'error' );
	}

	//Check required special fields if aladdin delivers
	if ( $_POST['cone_billing_get_products'] == 'aladdin' || $_POST['cone_billing_return_products'] == 'aladdin' ) {
		$req = [
			'Typ av lokal' => 'special_venue_type',
			'Stadsdel' => 'special_county',
			'Våningsplan' => 'special_floor',
			'Portkod' => 'special_doorcode',
			'Trappsteg till entré/hiss' => 'special_steps_to_elevator',
			'Storlek på ev.hiss' => 'special_elevator_size',
			'Bärsträcka från lastbil' => 'special_carry_distance'
		];
		foreach ($req as $key => $special_field) {
			if ( $_POST[$special_field] == '' ) {
				wc_add_notice( __($key.' kan inte vara tomt.') , 'error' );
			}
		}
		if ( $_POST['special_venue_type'] == 'Annat' && ! $_POST['special_venue_other']) {
			wc_add_notice( __('Om typ av lokal är "Annat" måste det specifieras.') , 'error' );
		}
	}

	//If customer is privatperson can payment only be faktura? 
	if ( $_POST['billing_customer_type'] == 'Privatperson' && $_POST['payment_method'] != 'cod' ) {
		wc_add_notice( __('Ogiltig betalningsmetod.') , 'error' );
	}

	//Hyra inom 2 dagar
	$days = '-'.get_field('days-delay', 'options').' days';
	if ( date('Ymd', strtotime($days, strtotime($_POST['cone_billing_order_date']))) < date('Ymd') ) {
		wc_add_notice( __('Det är för kort tid till hyrestillfället, vi ber vi Dig att istället ringa oss på 664 2000. Vi beklagar besväret.') , 'error' );
		//wc_add_notice( __( date('Y-m-d', strtotime('-2 day', strtotime($_POST['cone_billing_order_date']))) . ' - ' .  date('Y-m-d') ) , 'error' );

	}
}