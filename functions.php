<?php
/**
 * cone functions and definitions
 *
 * Set up the theme and provides some helper functions, which are used in the
 * theme as custom template tags. Others are attached to action and filter
 * hooks in WordPress to change core functionality.
 *
 * When using a child theme you can override certain functions (those wrapped
 * in a function_exists() call) by defining them first in your child theme's
 * functions.php file. The child theme's functions.php file is included before
 * the parent theme's file, so the child theme functions would be used.
 *
 * @link https://codex.wordpress.org/Theme_Development
 * @link https://codex.wordpress.org/Child_Themes
 *
 * Functions that are not pluggable (not wrapped in function_exists()) are
 * instead attached to a filter or action hook.
 *
 * For more information on hooks, actions, and filters,
 * @link https://codex.wordpress.org/Plugin_API
 *
 * @package cone
 */

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function cone_theme_setup() {
 
    /*
     * Make theme available for translation.
     * Translations can be filed in the /languages/ directory.
     */
    load_theme_textdomain( 'cone', get_template_directory() . '/languages' );
 
    // Register nav menues to use wp_nav_menu()
    register_nav_menus( array(
        'primary' => __( 'Primary menu', 'cone' ),
        'secondary' => __( 'Secondary menu', 'cone' ),
        'footer' => __( 'Footer menu', 'cone' )
    ) );
 
    /*
     * Enable support for Post Thumbnails on posts and pages.
     *
     * See: https://codex.wordpress.org/Function_Reference/add_theme_support#Post_Thumbnails
     */
    add_theme_support( 'post-thumbnails' );
    // Add thumb sizes below
    add_image_size( 'rectangle-thumb', 375, 220, true );

    /*
     * Enable support for Post Formats.
     * See https://codex.wordpress.org/Post_Formats
     */
    add_theme_support( 'post-formats', array(
        'aside', 'image', 'video', 'audio', 'quote', 'link', 'gallery',
    ) );
 
    /*
     * Let WordPress manage the document title.
     * By adding theme support, we declare that this theme does not use a
     * hard-coded <title> tag in the document head, and expect WordPress to
     * provide it for us.
     */
    add_theme_support( 'title-tag' );
}
add_action( 'after_setup_theme', 'cone_theme_setup' );

remove_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5 );
remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20 );
remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );


add_action( 'after_setup_theme', 'woocommerce_support' );
function woocommerce_support() {
    add_theme_support( 'woocommerce' );
}



/**
 * Register custom post types
 * 
 * @link https://codex.wordpress.org/Function_Reference/register_post_type
 */
function cone_register_post_types() {
    // Custom post types should be registered here
}
add_action( 'init', 'cone_register_post_types' );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function cone_widgets_init() {
    register_sidebar( array(
        'name'          => __( 'Sidebar', 'cone' ),
        'id'            => 'sidebar-1',
        'description'   => '',
        'before_widget' => '<aside id="%1$s" class="widget %2$s">',
        'after_widget'  => '</aside>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );
}
add_action( 'widgets_init', 'cone_widgets_init' );

/**
 * Set the post excerpt length to 40 words.
 *
 * To override this length in a child theme, remove
 * the filter and add your own function tied to
 * the excerpt_length filter hook.
 *
 * @param int $length The number of excerpt characters.
 * @return int The filtered number of characters.
 */
function cone_set_excerpt_length( $length ) {
    return 40;
}
add_filter( 'excerpt_length', 'cone_set_excerpt_length' );

/**
 * Replace "[...]" in the Read More link with an ellipsis.
 *
 * The "[...]" is appended to automatically generated excerpts.
 *
 * To override this in a child theme, remove the filter and add your own
 * function tied to the excerpt_more filter hook.
 *
 * @param string $more The Read More text.
 * @return The filtered Read More text.
 */
function cone_excerpt_more( $more ) {
    if ( ! is_admin() ) {
        return ' &hellip;';
    }
    return $more;
}
add_filter( 'excerpt_more', 'cone_excerpt_more' );

/**
 * Add all the main scripts and styles here.
 */
function cone_enqueue_scripts() {

    // WordPress style.css
    wp_enqueue_style( 'default-style', get_stylesheet_uri() );

    //Swiper script and style
    wp_enqueue_style( 'swiper-style', get_template_directory_uri() . '/assets/css/lib/swiper.min.css' );

    wp_enqueue_script( 'swiper-scripts', get_template_directory_uri() . '/assets/js/lib/swiper.min.js', array('jquery'), 1.0, true );

    wp_enqueue_style( 'menu-aim-style', get_template_directory_uri() . '/assets/css/lib/menu-aim.css' );

    wp_enqueue_script( 'jq-menu-aim-scripts', get_template_directory_uri() . '/assets/js/lib/jquery.menu-aim.js', array('jquery'), 1.0, true );

    wp_enqueue_script( 'modernizr-scripts', get_template_directory_uri() . '/assets/js/lib/modernizr.js', array('jquery'), 1.0, true );

    wp_enqueue_script( 'menu-aim-scripts', get_template_directory_uri() . '/assets/js/lib/menu-aim.js', array('jquery'), 1.0, true );

    // vendor.css created with gulp
    wp_enqueue_style( 'main-min-style', get_template_directory_uri() . '/assets/css/src/main.min.css', array(), 2.7 );

    // vendor.js created with gulp
    wp_enqueue_script( 'main-min-scripts', get_template_directory_uri() . '/assets/js/src/main.min.js', array('jquery'), 2.5, false );

    wp_dequeue_script( 'wc-checkout' );

    wp_enqueue_script( 'wc-cart-fragments' );

    wp_localize_script(
      'main-min-scripts', // this needs to match the name of our enqueued script
      'ajaxApi',      // the name of the object
      array('ajaxurl' => admin_url('admin-ajax.php')) // the property/value
    );
}
add_action( 'wp_enqueue_scripts', 'cone_enqueue_scripts' );

// Custom template tags
require get_template_directory() . '/inc/template-tags.php';

/**
*-----------------------------------------------------------------------
*Function to add item to existing order with status "On hold".
*-----------------------------------------------------------------------
*/

function add_to_existing_order() {

    $orderId = absint($_POST['cone_orderID']);
    $order = wc_get_order( $orderId );
    
    if ( get_current_user_id() != $order->user_id ) {
        var_dump('No way José');
        die();
    } 

    $product = wc_get_product( $_POST['cone_product'] );
    $items = $order->get_items();
    $exists = 0; 
    $cone_order_meta = '';

    foreach ( $items as $key => $item ) {
        $product_id = wc_get_order_item_meta($key, '_product_id');
        if ( $product_id == $_POST['cone_product'] ) {
            $exists = 1;
            $current_q = wc_get_order_item_meta($key, '_qty');
            $q = $item['quantity'] + $_POST['cone_quantity'];
            $total = $product->price * $q;
            wc_update_order_item_meta($key, '_qty', $q);
            wc_update_order_item_meta($key, '_line_total', $total);
            wc_update_order_item_meta($key, '_line_subtotal', $total);

            //Add cone order item meta
            woocommerce_add_order_item_meta($key, '_cone_changed_item', 'qty,'. $current_q .',' . $q);
            $cone_order_meta = $key;
        }
    }

    if ( $exists == 0 ) {
        $item_id = $order->add_product($product, $_POST['cone_quantity']);
        woocommerce_add_order_item_meta($item_id, '_cone_changed_item', 'added');
        $cone_order_meta = $item_id;
    }

    $updated_order = wc_get_order( $orderId );
    $updated_order->calculate_totals();

    $url = esc_url( $updated_order->get_view_order_url() );

    $url = add_query_arg('order', 'updated', $url);

    $attachments = wcpdf_get_invoice( [$orderId], true )->get_pdf();

    $name = $orderId. '-' . date('ynjHis');

    // And a path where the file will be created
    $path = get_template_directory() .'/assets/orders/'. $name .'.pdf';

    // Then just save it like this
    file_put_contents( $path, $attachments );

    //die( var_dump( $attachments ) );
    $admin_email = get_option( 'admin_email' );
    $headers = 'From: Aladdin <<EMAIL>>' . "\r\n";
    wp_mail( $admin_email, 'Ändrad order', 'Order #'.$orderId.' har ändrats.', $headers, $path );
    //wp_mail( '<EMAIL>', 'Ändrad order', 'Order #'.$orderId.' har ändrats.', $headers, $path );


    //Delete Cone order meta
    wc_delete_order_item_meta( $cone_order_meta, '_cone_changed_item' );

    wp_redirect( $url );
    exit;
}
add_action( 'admin_post', 'add_to_existing_order' );
add_action( 'admin_post_cone_add_to_order', 'add_to_existing_order' );


 

/**
*-----------------------------------------------------------------------------------------
*Function to check if order status should be updated so customer cant add to order.
*-----------------------------------------------------------------------------------------
*/



//pagination for blog
function pagination_bar( $custom_query ) {

    $total_pages = $custom_query->max_num_pages;
    $big = 999999999; // need an unlikely integer

    if ($total_pages > 1){
        $current_page = max(1, get_query_var('paged'));

        echo paginate_links(array(
            'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
            'format' => '?paged=%#%',
            'current' => $current_page,
            'total' => $total_pages,
        ));
    }
}


//Add option page to ACF
if( function_exists('acf_add_options_page') ) {

    acf_add_options_page();
    acf_add_options_sub_page('Header');

}



// Edit myaccount menu
function wpb_woo_my_account_order() {
     $myorder = array(
        'orders' => __( 'Mina Ordrar', 'woocommerce' ),
        'edit-account' => __( 'Profil & Inställningar', 'woocommerce' ),
     );
     return $myorder;
}
add_filter ( 'woocommerce_account_menu_items', 'wpb_woo_my_account_order' );


//Redirect to login/register before checkout.
function cone_before_checkout() {
    if (
        ! is_user_logged_in()
        && ( is_checkout() )
    ) {

        wp_redirect(home_url('/checkout-login'));
        exit;
    }
}
add_action('template_redirect', 'cone_before_checkout');


//Register user
include 'inc/register-user.php';

//Product filter
include 'inc/filter-products.php';

//Add custom checkout fields
include 'inc/checkout-fields.php';

//Create order with custom fields
include 'inc/create-order.php';

//Quick add product to order
//include 'inc/quick-add-to-order.php';

//Ajax add single product to cart
include 'inc/cart.php';

//Custom block dates setting
include 'inc/blocked-dates.php';

//Custom checkout validation
include 'inc/checkout-process.php';

//Add to admin order
include 'inc/admin-order.php';

//Custom changed order email
include_once( 'inc/cone-custom-emails/cone-email-functions.php' );

//Sort categories from parent to child
function sort_terms_hierarchically( array &$terms, array &$into, $parent_id = 0 ) {
    foreach ( $terms as $i => $term ) {
        if ( $term->parent == $parent_id ) {
            $into[$term->term_id] = $term;
            unset( $terms[ $i ] );
        }
    }

    foreach ( $into as $top_term ) {
        $top_term->children = array();
        sort_terms_hierarchically( $terms, $top_term->children, $top_term->term_id );
    }

}

//Remove payment options and placeorder button from sidebar on checkout-page.
remove_action( 'woocommerce_checkout_order_review', 'woocommerce_checkout_payment', 20 );

//Remove coupon form
remove_action( 'woocommerce_before_checkout_form', 'woocommerce_checkout_coupon_form', 10 );

//Add payment options and place order button to form on checkout-page
add_action( 'woocommerce_checkout_after_customer_details', 'woocommerce_checkout_payment', 90 );

//Always show shipping form.
add_filter( 'woocommerce_cart_needs_shipping_address', '__return_true', 50 );



//Update cart on AJAX add_to_cart
add_filter( 'woocommerce_add_to_cart_fragments', 'cone_cart_content_update', 10, 2 );

function cone_cart_content_update( $cart ) {
    
    ob_start();
    woocommerce_mini_cart();
    $cart_panel = ob_get_clean();

    $cart['a.cart-customlocation'] = '<a class="cart-customlocation"><span>' . WC()->cart->get_cart_contents_count() . '</span><i class="material-icons">shopping_cart</i></a>';
    $cart['.open-cart-grid'] = $cart_panel;
    $cart['span.cart-header'] = '<span class="cart-header">(' . WC()->cart->get_cart_contents_count() . ')</span>';
    $cart['p.cart-excl-tax'] = '<p class="open-cart-sum">' . WC()->cart->get_cart_total() . ' exkl moms</p>';
    $cart['p.cart-incl-tax'] = '<p class="open-cart-sum">' . wc_price( WC()->cart->total ) . ' inkl moms</p>';
    return $cart;
}




//Enqueue admin style and scripts on block date page
function load_custom_wp_admin_style($hook) {
        // Load only on ?page=mypluginname
        if($hook != 'toplevel_page_cone_block_dates') {
                return;
        }

        wp_enqueue_script( 'jquery-ui-datepicker' );

        wp_enqueue_style('e2b-admin-ui-css','http://ajax.googleapis.com/ajax/libs/jqueryui/1.9.0/themes/base/jquery-ui.css',false,"1.9.0",false);

        wp_enqueue_script( 'multidate-script', get_template_directory_uri() . '/assets/js/lib/jquery-ui.multidatespicker.js', array('jquery'), 1.0, true );

        wp_enqueue_style( 'multidate-style', get_template_directory_uri() . '/assets/css/lib/jquery-ui.multidatespicker.css' );

        wp_enqueue_style('admin-styles', get_template_directory_uri().'/assets/css/src/main.min.css');
}
add_action( 'admin_enqueue_scripts', 'load_custom_wp_admin_style' );


/**
 * Redirect user after successful login.
 *
 * @param string $redirect_to URL to redirect to.
 * @param string $request URL the user is coming from.
 * @param object $user Logged user's data.
 * @return string
 */

function my_login_redirect( $redirect_to, $request, $user ) {

    $isCheckoutLogin = $_POST['checkout_login'];

    if ( $isCheckoutLogin == 1 ) {
        return wc_get_checkout_url();
    } else {
        return $redirect_to;
    }
}

add_filter( 'login_redirect', 'my_login_redirect', 10, 3 );

function cone_hide_admin_bar_if_non_admin( $show ) {
    if ( ! current_user_can( 'administrator' ) ) $show = false;
    return $show;
}
 
add_filter( 'show_admin_bar', 'cone_hide_admin_bar_if_non_admin', 20, 1 );


//Return to same page instead of wp-login page if login fails
add_action( 'wp_login_failed', 'custom_login_failed' );
function custom_login_failed( $username )
{
    $referrer = wp_get_referer();
    $login_page = visionmate_get_login_url();

    if ($referrer && !strstr($referrer, basename($login_page)) && !strstr($referrer,'wp-admin')) {
        wp_redirect(add_query_arg('login', 'failed', $referrer));
        exit;
    }
}

//If password or email are empty return to same page
add_filter( 'authenticate', 'custom_authenticate_username_password', 30, 3);
function custom_authenticate_username_password( $user, $username, $password )
{
    if ( is_a($user, 'WP_User') ) { return $user; }

    if ( empty($username) || empty($password) )
    {
        $error = new WP_Error();
        $user  = new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid username or incorrect password.'));

        return $error;
    }
}


add_action( 'woocommerce_after_checkout_validation', function() {
    global $woocommerce;
    
    $products_in_cart = array();
    $bundles = [];
    $items = [];
    foreach ( $woocommerce->cart->cart_contents as $key => $item ) {
        $terms = get_post_meta( $item['data']->get_id(), '_sku' );
        $items[$key] = $item;
        if ( array_key_exists('bundled_items', $item) ) {
            $bundles[ $key ] = $item['bundled_items'];
        }

        //$terms = wp_get_post_terms($item['data']->id, 'product_cat' );
        $products_in_cart[ $key ] = $terms[0];
    }

    file_put_contents('./items.json', json_encode($items));

    file_put_contents('./array.json', json_encode($products_in_cart));

    asort( $products_in_cart , SORT_STRING );

    $cart_contents = array();
    
    foreach ( $products_in_cart as $cart_key => $sku ) {
        $cart_contents[ $cart_key ] = $woocommerce->cart->cart_contents[ $cart_key ];
    }

    //If there are any bundles in order
    if ( $bundles ) {
        //Loop through if there are multiple bundles
        foreach ($bundles as $cartKey => $arrayOfBundledItems) {
            //Loop throug each product in bundle
            foreach ($arrayOfBundledItems as $item_cart_key) {
                //Get Item that should be moved
                $moveBundleItem = array( $item_cart_key => $cart_contents[ $item_cart_key ]);
                //Remove it from its place
                unset($cart_contents[ $item_cart_key ]);
                //Find index of the bundle
                $afterIndex = array_search($cartKey,array_keys($cart_contents));
                //PLace the item under its bundle
                $cart_contents = array_merge(array_slice($cart_contents,0,$afterIndex+1), $moveBundleItem, array_slice($cart_contents,$afterIndex+1));
            }
        }
    }


    file_put_contents('./cart_contents.json', json_encode($cart_contents));
    
    $woocommerce->cart->cart_contents = $cart_contents;
}, 10, 1 );


add_action('admin_init', 'disable_dashboard');
function disable_dashboard() {
    if (!is_user_logged_in()) {
        return null;
    }
    if (!current_user_can('administrator') && is_admin()) {
        wp_redirect(home_url());
        exit;
    }
}


function cd_algolia_set_record_index_to_menu_order($records, $post) {
    foreach ($records as &$record) {
        $record['record_index'] = $post->menu_order;
        //unset($record['content']);
    }

    return $records;
}
add_filter('algolia_post_product_records', 'cd_algolia_set_record_index_to_menu_order',10, 2);

function cd_discount_price($product) {
    if( $product->is_on_sale() ){
        $s = new FlycartWooDiscountRulesPricingRules;
        $price = $s->getDiscountPriceForTheProduct($product);
        if ( $price == 0 ) $price = $product->get_price();
    }else{
        $price = $product->get_price();
    }

    return $price;
}


add_filter( 'woocommerce_available_variation', 'change_variation_descriptions', 10, 3 );
function change_variation_descriptions( $data, $product, $variation ) {

    $data['price_inc'] = '<span class="price"><span class="cart_price">'.wc_price( wc_get_price_including_tax( $variation ) ).'</span></span>';    

    return $data;     
}


add_filter( 'algolia_post_shared_attributes', 'my_post_attributes', 10, 2 );
add_filter( 'algolia_searchable_post_shared_attributes', 'my_post_attributes', 10, 2 );

/**
 * @param array   $attributes
 * @param WP_Post $post
 *
 * @return array
 */
function my_post_attributes( array $attributes, WP_Post $post ) {

    if ( 'product' !== $post->post_type ) {
        // We only want to add an attribute for the 'speaker' post type.
        // Here the post isn't a 'speaker', so we return the attributes unaltered.
        return $attributes;
    }

    // Get the field value with the 'get_field' method and assign it to the attributes array.
    // @see https://www.advancedcustomfields.com/resources/get_field/
    $attributes['product_synonyms'] = get_field( 'synonymer', $post->ID );
    $attributes['product_sku'] = wc_get_product($post->ID)->get_sku();
    // Always return the value we are filtering.
    return $attributes;
}

//Round Totals
function custom_calculated_total( $total ) {
    $total = round( $total, 0);
    return ceil($total);
}
add_filter( 'woocommerce_calculated_total', 'custom_calculated_total' );


/*
 * goes in theme functions.php or a custom plugin
 *
 * Subject filters: 
 *   woocommerce_email_subject_new_order
 *   woocommerce_email_subject_customer_processing_order
 *   woocommerce_email_subject_customer_completed_order
 *   woocommerce_email_subject_customer_invoice
 *   woocommerce_email_subject_customer_note
 *   woocommerce_email_subject_low_stock
 *   woocommerce_email_subject_no_stock
 *   woocommerce_email_subject_backorder
 *   woocommerce_email_subject_customer_new_account
 *   woocommerce_email_subject_customer_invoice_paid
 **/
add_filter('woocommerce_email_subject_customer_completed_order', 'change_admin_email_subject', 1, 2);

function change_admin_email_subject( $subject, $order ) {
    global $woocommerce;

    $count = ( get_post_meta($order->get_id(), 'cone_order_email_count', true) ) ? ( get_post_meta($order->get_id(), 'cone_order_email_count', true) ) : 1;
    $count = ($count == 0) ? '' : $count;

    $firstName = get_post_meta($order->get_id(),'_billing_first_name', true);
    $lastName = get_post_meta($order->get_id(),'_billing_last_name', true);
    $name = $firstName . ' ' . $lastName;
    $subject = sprintf( 'Ändrad beställning / order nummer %s / %s till %s med hyrdatum %s', $order->get_order_number(), $count, $name, date('d.m.Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))) );

    return $subject;
}

add_filter('woocommerce_email_subject_new_order', 'new_order_email_subject', 1, 2);
function new_order_email_subject( $subject, $order ) {
    global $woocommerce;

    $title = ( get_post_meta($order->get_id(), 'cone_is_offer', true) === 'false' ) ? 'Ny kundorder' : 'Endast offert';
    $count = ( get_post_meta($order->get_id(), 'cone_order_email_count', true) ) ? ( get_post_meta($order->get_id(), 'cone_order_email_count', true) ) : 1;
    $count = ($count == 0) ? '' : $count; 
    $firstName = get_post_meta($order->get_id(),'_billing_first_name', true);
    $lastName = get_post_meta($order->get_id(),'_billing_last_name', true);
    $name = $firstName . ' ' . $lastName;
    $subject = sprintf( '%s #%s / %s till %s med hyrdatum %s', $title, $order->get_order_number(), $count, $name, date('d.m.Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))) );

    return $subject;
}

add_filter('woocommerce_email_subject_customer_invoice', 'new_order_customer_email_subject', 1, 2);
function new_order_customer_email_subject( $subject, $order ) {
    global $woocommerce;

    $firstName = get_post_meta($order->get_id(),'_billing_first_name', true);
    $lastName = get_post_meta($order->get_id(),'_billing_last_name', true);
    $name = $firstName . ' ' . $lastName;
    $subject = sprintf( 'Tack för din beställning / order nummer %s / till %s med hyrdatum %s', $order->get_order_number(), $name, date('d.m.Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))) );

    return $subject;
}

add_filter('woocommerce_email_subject_customer_processing_order', 'new_order_processing_email_subject', 1, 2);
function new_order_processing_email_subject( $subject, $order ) {
    global $woocommerce;

    $firstName = get_post_meta($order->get_id(),'_billing_first_name', true);
    $lastName = get_post_meta($order->get_id(),'_billing_last_name', true);
    $name = $firstName . ' ' . $lastName;
    $subject = sprintf( 'Tack för din beställning / order nummer %s / till %s med hyrdatum %s', $order->get_order_number(), $name, date('d.m.Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))) );

    return $subject;
}

add_filter('woocommerce_email_subject_customer_on_hold_order', 'new_order_onhold_email_subject', 1, 2);
function new_order_onhold_email_subject( $subject, $order ) {
    global $woocommerce;

    $firstName = get_post_meta($order->get_id(),'_billing_first_name', true);
    $lastName = get_post_meta($order->get_id(),'_billing_last_name', true);
    $name = $firstName . ' ' . $lastName;
    $subject = sprintf( 'Tack för din beställning / order nummer %s / till %s med hyrdatum %s', $order->get_order_number(), $name, date('d.m.Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))) );

    return $subject;
}

function coneEditOrderPOST()
{
    if ( isset( $_POST['action'] ) && $_POST['action'] == 'cone_update_order' ) {
        require_once( get_template_directory() . '/inc/change-order.php');
    }

    if ( isset( $_POST['action'] ) && $_POST['action'] == 'cone_quick_add_item_to_order' ) {
        require_once( get_template_directory() . '/inc/quick-add-to-order.php');
    }
}
add_action('wp_loaded', 'coneEditOrderPOST');


add_filter( 'woocommerce_email_headers', 'mycustom_headers_filter_function', 10, 2);
function mycustom_headers_filter_function( $headers, $object ) {
    if ($object == 'customer_completed_order') {
        $headers .= 'BCC: Aladdin <<EMAIL>>, ConeDigital <<EMAIL>>' . "\r\n";
        //$headers .= 'BCC: Aladdin <<EMAIL>>' . "\r\n";
    }else if ( $object == 'new_order' ) {
        $headers .= 'BCC: ConeDigital <<EMAIL>>' . "\r\n";
    }

    return $headers;
}

add_action( 'admin_menu', 'linked_url' );
function linked_url() {
    add_menu_page( 'linked_url', 'Lathund', 'read', 'my_slug', '', 'dashicons-text', 1 );
}

add_action( 'admin_menu' , 'linkedurl_function' );
function linkedurl_function() {
    global $menu;
    $menu[1][2] = "https://docs.google.com/document/d/1YqIGhYV_lQgJngRcBnFIemGa4SmUNVMWa-nPCNG9_8g/edit";
}

//Removes title tag from wp_head
remove_action( 'wp_head', '_wp_render_title_tag', 1 );

add_action ('woocommerce_email_customer_details', 'including_global_email_data', 2, 4 );
function including_global_email_data( $order, $sent_to_admin, $plain_text, $email ){
    // Set global email variable
    $GLOBALS['email_data'] = array( 'email' => $email, 'admin' => $sent_to_admin );
}

// add_filter ('woocommerce_email_footer_text', 'custom_email_footer_text', 20, 1 );
// function custom_email_footer_text( $footer_text ){
//     // Get global variables
//     $refNameGlobalsVar = $GLOBALS;
//     $sent_to_admin = $refNameGlobalsVar['email_data']['admin'];
//     $email         = $refNameGlobalsVar['email_data']['email'];

//     // Only for admin "New order" email notification
//     if( $email->id === 'new_order' ) // Or also:  if( $sent_to_admin )
//     {
//     $footer_text .= '<br><a href="http://maps.google.com/maps?q=nieuwstraat+15+brussel+1000+Belgium" class="m_8816063166882312894map-it-link" target="_blank" data-saferedirecturl="https://www.google.com/url?q=http://maps.google.com/maps?q%3Dnieuwstraat%2B15%2Bbrussel%2B1000%2BBelgium&amp">Map It</a>';
//     }
//     return $footer_text;
// }


add_filter( 'woocommerce_available_variation', 'cone_variation', 10, 3);
function cone_variation( $data, $product, $variation ) {

    $data['price_html'] = $variation->get_price_html();
    
    return $data;
}

add_filter( 'woocommerce_get_price_html', 'cone_price_html', 100, 2 );
function cone_price_html( $price, $product ){
    $price_excl = '<span class="cd-price">'. $price .' exkl. moms</span>';
    $price_inc = '<span class="cd-price">';

    //If is variable product
    if ( method_exists($product, 'get_variation_prices') ) {
        //Get variation prices
        $prices = $product->get_variation_prices( true );
        //If prices are empty    
        if ( empty( $prices['price'] ) ) {
            $price_inc = apply_filters( 'woocommerce_variable_empty_price_html', '', $product );
        }
        //If prices
        else {
            $min_price     = wc_get_price_including_tax( $product, array( 'price' => current( $prices['price'] ) ) ); //current( $prices['price'] );
            $max_price     = wc_get_price_including_tax( $product, array( 'price' => end( $prices['price'] ) ) ); //end( $prices['price'] );
            $min_reg_price = wc_get_price_including_tax( $product, array( 'price' => current( $prices['regular_price'] ) ) ); //current( $prices['regular_price'] );
            $max_reg_price = wc_get_price_including_tax( $product, array( 'price' => end( $prices['regular_price'] ) ) ); //end( $prices['regular_price'] );

            if ( $min_price !== $max_price ) {
                $price_inc .= wc_format_price_range( $min_price, $max_price );
            } elseif ( $product->is_on_sale() && $min_reg_price === $max_reg_price ) {
                $price_inc .= wc_format_sale_price( wc_price( $max_reg_price ), wc_price( $min_price ) );
            } else {
                $price_inc .= wc_price( $min_price );
            }

            //$price_inc = apply_filters( 'woocommerce_variable_price_html', $price_inc . $product->get_price_suffix(), $product );
        }
    }
    //If is bundled product
    else if ( $product->is_type( 'yith_bundle' ) ) {
        if ( get_post_meta( $product->get_id(), '_yith_wcpb_per_item_pricing', true) === 'no' ) {
            if ( $product->is_on_sale() ){
                $price_inc .= wc_format_sale_price( wc_get_price_including_tax( $product, array( 'price' => $product->get_regular_price() ) ), wc_get_price_including_tax( $product ) ) . $product->get_price_suffix();
            }else{
                $price_inc .= wc_price( wc_get_price_including_tax( $product ) ) . $product->get_price_suffix();
            }
        }else{
            $bundle_price = 0.00;
            foreach ($product->bundle_data as $key => $bp) {
                $pro = wc_get_product( $bp['product_id'] );
                $price_incl_tax = ($pro) ? wc_get_price_including_tax($pro) : 0.00;
                $bundle_price += $price_incl_tax * $bp['bp_min_qty'];
            }
            $price_inc .= wc_price( $bundle_price );
        }
    }
    else {
        if ( '' === $product->get_price() ) {
            $price_inc .= apply_filters( 'woocommerce_empty_price_html', '', $product );
        } elseif ( $product->is_on_sale() ) {
            $price_inc .= wc_format_sale_price( wc_get_price_including_tax( $product, array( 'price' => $product->get_regular_price() ) ), wc_get_price_including_tax( $product ) ) . $product->get_price_suffix();
        } else {
            $price_inc .= wc_price( wc_get_price_including_tax( $product ) ) . $product->get_price_suffix();
        }
    }

    $price_inc .= ' inkl. moms</span>';

    return $price_excl . $price_inc;
}

remove_action('woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30);

add_action( 'pre_get_posts', 'custom_pre_get_posts' );
function custom_pre_get_posts($query) {
    if ( is_woocommerce() ) {
        $query->set('posts_per_page', -1);
    }

    return $query;
}

// Hook in
add_filter( 'woocommerce_default_address_fields' , 'custom_override_default_address_fields' );
// Our hooked in function - $address_fields is passed via the filter!
function custom_override_default_address_fields( $address_fields ) {
    $address_fields['billing_postcode']['required'] = false;
    $address_fields['shipping_postcode']['required'] = false;
    
    return $address_fields;
}

// Remove password change email
remove_action( 'after_password_reset', 'wp_password_change_notification' );

// Get login url
function visionmate_get_login_url($action = '') {
    if (class_exists('WPS_Hide_Login')) {
        $wps_hide_login = new WPS_Hide_Login();
        $login_url = home_url($wps_hide_login->new_login_url());
        if ($action) {
            $login_url = add_query_arg('action', $action, $login_url);
        }
        return $login_url;
    }
    return site_url('wp-login.php' . ($action ? "?action=$action" : ''));
}

/**
 * Disable messages about the mobile apps in WooCommerce emails.
 * https://wordpress.org/support/topic/remove-process-your-orders-on-the-go-get-the-app/
 */
function visionmate_disable_mobile_messaging( $mailer ) {
    remove_action( 'woocommerce_email_footer', array( $mailer->emails['WC_Email_New_Order'], 'mobile_messaging' ), 9 );
}
add_action( 'woocommerce_email', 'visionmate_disable_mobile_messaging' );