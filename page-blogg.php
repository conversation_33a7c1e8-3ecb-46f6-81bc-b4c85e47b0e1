<?php
/**
 * The template for displaying the header
 *
 * @package cone
 */
?><!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title( ' - ', true, 'right' ); ?></title>
    <?php cone_og_meta_tags(); ?>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="icon" href="<?php echo esc_url(home_url( '/wp-content/themes/DirectConversion/assets/images/favicon.png' ) ); ?>">

    <?php wp_head(); ?>
</head>
<body>
    <div class="blog-header max-width">
        <h2><a class="absolute-link" href="<?php echo esc_url(home_url()); ?>"></a>Aladdins uthyrning</h2>
        <p>Praesent vestibulum dapibus nibh. Morbi vestibulum volutpat enim volutpat. <a href="<?php echo home_url('/blogg'); ?>">Bloggen</a>.</p>
    </div>
    <section class="blog-hero">
        <div class="blog-hero-content max-width medium-width">
            <?php the_content() ; ?>
        </div>
    </section>
    <section class="blog-section">
        <?php $paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1; ?>
        <?php $loop = new WP_Query( array( 'post_type' => 'post', 'posts_per_page' => 10, 'paged' => get_query_var('paged') ? get_query_var('paged') : 1 ) ); ?>
        <?php if ( $loop->have_posts() ) : ?>
            <?php while ( $loop->have_posts() ) : $loop->the_post(); ?>
                <div class="blog-grid">
                    <div class="blog-grid-content max-width medium-width">
                        <h5>Nyhet</h5>
                        <h3><?php the_title() ; ?> <a class="absolute-link" href="<?php the_permalink() ; ?>"></a></h3>
                        <div class="blog-grid-info">
                            <a href="#"><?php the_author() ; ?></a>
                            <p><?php echo get_the_date() ; ?></p>
                        </div>
                    </div>
                </div>
            <?php endwhile ;  wp_reset_query();?>
            <div class="max-width medium-width pagination-section">
                <?php pagination_bar( $loop ); ?>
            </div>
        <?php endif; ?>
        <div class="subscribe-form-section">
            <h6>prenumerera</h6>
            <div class="subscribe-form max-width">
                <h3>Prenumerera på vårt nyhetsbrev</h3>
                <p>Missa inget av vad som händer på Aladdins uthyrning. Lämna din email för att prenumerera på vårt nyhetsbrev.</p>
                <?php echo do_shortcode('[mc4wp_form id="291"]')?>
            </div>
        </div>
    </section>
</body>
</html>
