var gulp = require('gulp');
var sass = require('gulp-sass')(require('sass'));
var minify = require('gulp-clean-css');
var postcss  = require("gulp-postcss");
var autoprefixer = require('autoprefixer');
var uglify = require('gulp-uglify');
var concat = require('gulp-concat');
var rename = require('gulp-rename');

gulp.task('css', function(){
    return gulp.src('./assets/scss/app.scss')
        .pipe(sass())
        .pipe(postcss([autoprefixer('last 15 version')]))
        .pipe(gulp.dest('./assets/css/src'))
        .pipe(minify())
        .pipe(rename('main.min.css'))
        .pipe(gulp.dest('./assets/css/src'))
});

gulp.task('js', function(){
    return gulp.src('./assets/js/*.js')
        .pipe(concat('main.min.js'))
        .pipe(uglify())
        .pipe(gulp.dest('assets/js/src'))
});

gulp.task('blog', function(){
    return gulp.src('./assets/js/blog/*.js')
        .pipe(concat('blog.min.js'))
        .pipe(uglify())
        .pipe(gulp.dest('assets/js/src'))
});

gulp.task('watch', function(){
    gulp.watch('./assets/scss/**/*.scss', gulp.series('css'));
    gulp.watch('./assets/js/*.js', gulp.series('js'));
});

gulp.task('default', gulp.series('css', 'watch', 'js', 'blog'));
gulp.task('build', gulp.series('css', 'js', 'blog'));