<?php
    get_header() ;
    //session_start();
    global $product; 

if( isset( $_SESSION['accept_error'] ) ) {
    $text = $_SESSION['accept_error'];
    unset($_SESSION['accept_error']);
}

if( isset( $_SESSION['email_exists_error'] ) ) {
    $text = $_SESSION['email_exists_error'];
    unset($_SESSION['email_exists_error']);
}
if( isset( $_SESSION['username_exists_error'] ) ) {
    $text = $_SESSION['username_exists_error'];
    unset($_SESSION['username_exists_error']);
}


?>

<section class="home-heroes">
    <div class="top-category-grid">
        <div class="top-cat-item max-width absolute-top-cat">
            <h4><?php echo get_field('offer-one-text') ; ?></h4>
        </div>
        <div class="category-big">
            <div class="category-background background-img" style="background-image: url('<?php echo get_field('offer-one-img') ; ?>')"></div>
            <a href="<?php echo get_field('offer-one-link') ; ?>" class="absolute-link"></a>
            <div class="overlay"></div>
        </div>
        <div class="categories-small">
            <div class="cat-small-content" >
                <div class="category-background background-img" style="background-image: url('<?php echo get_field('offer-two-img') ; ?>')"></div>
                <a href="<?php  echo get_field('link-two-cat') ?>" class="absolute-link"></a>
                <div class="top-cat-item">
                    <h4><?php echo get_field('offer-two-cat') ; ?></h4>
                    <p><?php echo get_field('offer-two-text') ; ?></p>
                </div>
            </div>
            <div class="cat-small-content">
                <div class="category-background background-img" style="background-image: url('<?php echo get_field('offer-three-img') ; ?>')"></div>
                <a href="<?php  echo get_field('link-three-cat') ?>" class="absolute-link"></a>
                <div class="top-cat-item">
                    <h4><?php echo get_field('offer-three-cat') ; ?></h4>
                    <p><?php echo get_field('offer-three-text') ; ?></p>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="divider-section">
    <div class="max-width">
        <h3><?php echo get_field('home-welcome'); ?></h3>
    </div>
<!--    <a href="#">Vårt sortiment</a>-->
</section>
<section class="best-seller-section">
    <div class="max-width home-cat-section">
        <div class="home-text-mobile">
            <p>Välkommen till Aladdins Uthyrning. Skrolla igenom vårt <a href="<?php echo esc_url( get_permalink( wc_get_page_id( 'shop' ) ) ); ?>">sortiment</a> eller sök direkt efter det du är intresserad av i menyn</p>
        </div>
        <div class="best-seller-top">
            <div class="best-seller-top-headlines">
                <h3>Kategorier</h3>
            </div>
            <a href="<?php echo esc_url(get_permalink( wc_get_page_id( 'shop' ) )); ?>">Visa Alla</a>
        </div>
        <div class="swiper-container category-swiper">
            <div class="swiper-wrapper">
            <?php
            $taxonomy     = 'product_cat';
            $orderby      = 'name';
            $show_count   = 0;      // 1 for yes, 0 for no
            $pad_counts   = 0;      // 1 for yes, 0 for no
            $hierarchical = 1;      // 1 for yes, 0 for no
            $title        = '';
            $empty        = 0;

            $args = array(
                'taxonomy'     => $taxonomy,
                // 'orderby'      => $orderby,
                'show_count'   => $show_count,
                'pad_counts'   => $pad_counts,
                'hierarchical' => $hierarchical,
                'title_li'     => $title,
                'hide_empty'   => $empty
            );

            $all_categories = get_categories( $args );
//            var_dump($all_categories);
            foreach ($all_categories as $cat) {
                if ($cat->slug === 'uncategorized' || $cat->slug === 'okategoriserad') {
                    continue;
                }
                $thumbnail_id = get_term_meta( $cat->term_id, 'thumbnail_id', true );
                $image = wp_get_attachment_url( $thumbnail_id );
                if($cat->category_parent == 0) {
                ?>
                <div class="swiper-slide category-card">
                    <a href="<?php echo get_term_link( $cat->term_id); ?>" class="absolute-link"></a>
                    <div class="category-card-img background-img" style="background-image: url('<?php echo $image; ?>')"></div>
                    <div class="category-card-name">
                        <p><?php echo $cat->name; ?></p>
                    </div>
                </div> <?php
                }
            }

            ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <div class="swiper-button-next nav-right-cat">
            <i class="material-icons ">keyboard_arrow_right</i>
        </div>
        <div class="swiper-button-prev nav-left-cat">
            <i class="material-icons ">keyboard_arrow_left</i>
        </div>
    </div>
    <div class="cd-mobile-hide">
        <?php echo do_shortcode('[yith_similar_products]') ; ?>
    </div>
    <div class="max-width cd-mobile-hide">
        <div class="best-seller-top">
            <div class="best-seller-top-headlines">
                <h3>Populära produkter</h3>
            </div>
        </div>
        <div class="swiper-container best-seller-swiper">
            <div class="swiper-wrapper">
                <?php
                // Setup your custom query
                $args = array(
                    'post_type' => 'product',
                    'post_status' => 'publish',
                    'ignore_sticky_posts' => 1,
                    'meta_key'  => 'total_sales',
                    'orderby'   => 'meta_value_num',
                    'posts_per_page' => 12,);
                $loop = new WP_Query( $args );
                $image = wp_get_attachment_image_src( get_post_thumbnail_id( $loop->post->ID ), 'single-post-thumbnail' );

                while ( $loop->have_posts() ) : $loop->the_post(); ?>
                    <div class="swiper-slide product-card">
                        <div class="product-card-top">
                            <a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
                            <div class="product-card-icons">
                                <i class="material-icons">favorite_border</i>
                            </div>
                            <?php  echo $product->get_image('full') ; ?>
                            <div class="product-card-excerpt">
                                <p><?php echo get_the_excerpt( get_the_ID() ) ; ?></p>
                                <!-- <span class="cd-product-card-excerpt-link">Klicka för viktig info</span> -->
                            </div>
                        </div>
                        <div class="product-card-content">
                            <span style="color: #4A90E2; font-size: 18px; margin-bottom: 8px; font-weight: 600;">Klicka för viktig info</span>
                            <a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
                            <p><?php the_title() ; ?></p>
                            <?php if($product->get_sku()) : ?>
                                <?php if ( !$product->is_type( 'variable' ) ) : ?>
                                    <span class="product-card-sku">SKU: <?php echo $product->get_sku(); ?></span>
                                <?php endif; ?>
                            <?php endif ; ?>
                            <span class="cd-product-span"><?php echo $product->get_price_html(); ?></span>
                        </div>
                        <div class="product-card-add">
                            <p class="<?php if ( $product->is_type( 'variable' ) || get_field('prompt') ) echo 'cd-variable-product'; ?>" data-link="<?php echo get_permalink( $loop->post->ID ); ?>">Lägg till i varukorg</p>
                            <?php if ( ! $product->is_type( 'variable' ) ) : ?>
                                <div class="add-remove-card" style="display: none;">
                                    <i class="material-icons">remove</i>
                                    <?php woocommerce_quantity_input(array('input_value' => isset( $quantity ) ? $quantity : 1)) ; ?>
                                    <i class="material-icons">add</i>
                                </div>
                                <a rel="nofollow" href="<?php echo esc_url( $product->add_to_cart_url() ); ?>" data-quantity="<?php echo esc_attr( isset( $quantity ) ? $quantity : 1 ); ?>" data-product_id="<?php echo esc_attr( $product->get_id() ); ?>" data-product_sku="<?php echo esc_attr( $product->get_sku() ); ?>" class="<?php echo esc_attr( isset( $class ) ? $class : 'bitt' ); ?> product_type_simple add_to_cart_button ajax_add_to_cart" style="display: none;">Lägg till</a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endwhile; wp_reset_query(); ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <div class="swiper-button-next nav-right">
            <i class="material-icons">keyboard_arrow_right</i>
        </div>
        <div class="swiper-button-prev nav-left">
            <i class="material-icons">keyboard_arrow_left</i>
        </div>
    </div>
</section>
<section class="background-section background-img" style="background-image: url('<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/wedding.jpg' ) ); ?>')">
    <div class="overlay"></div>
    <div class="max-width background-section-content">
        <?php echo get_field('home-background-section') ; ?>
    </div>
</section>
<?php /*
<section class="package-section">
    <div class="max-width">
        <div class="best-seller-top">
            <div class="best-seller-top-headlines">
                <h3>Färdiga Paket</h3>
            </div>
            <a href="<?php echo esc_url(home_url( '/produkt-kategori/event-tillbehor/fardiga-paket' ) ); ?>">Visa Alla</a>
        </div>
        <div class="swiper-container packages-swiper">
            <div class="swiper-wrapper">
                <?php
                // Setup your custom query
                $args = array(
                    'post_type' => 'product',
                    'post_status' => 'publish',
                    'product_cat'    => 'fardiga-paket',
                    'posts_per_page' => 8,);
                $loop = new WP_Query( $args );
                $image = wp_get_attachment_image_src( get_post_thumbnail_id( $loop->post->ID ), 'single-post-thumbnail' );

                while ( $loop->have_posts() ) : $loop->the_post(); ?>
                    <div class="swiper-slide product-card">
                        <div class="product-card-top">
                            <a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
                            <div class="product-card-icons">
                                <i class="material-icons">favorite_border</i>
                            </div>
                            <?php  echo $product->get_image('full') ; ?>
                            <div class="product-card-excerpt">
                                <p><?php echo $product->post->post_excerpt ; ?></p>
                                <span>Klicka för viktig info</span>
                            </div>
                        </div>
                        <div class="product-card-content">
                            <a href="<?php echo get_permalink( $loop->post->ID ) ?>" class="absolute-link"></a>
                            <p><?php the_title(); ?></p>
                            <?php if($product->get_sku()) : ?>
                                <?php if ( !$product->is_type( 'variable' ) ) : ?>
                                    <span class="product-card-sku">SKU: <?php echo $product->get_sku(); ?></span>
                                <?php endif; ?>
                            <?php endif ; ?>
                            <span class="cd-product-span"><?php echo $product->get_price_html(); ?>&nbsp;exkl. moms</span>
                            <?php if ( $product->is_type( 'variable' ) ) : ?>
                                <h6><?php echo $product->get_price_including_tax(1, $product->get_variation_price('min') ) . ' kr - ' . $product->get_price_including_tax(1, $product->get_variation_price('max') ); ?> kr inkl. moms</h6>
                            <?php else : ?>
                                <h6><?php echo woocommerce_price( $product->get_price_including_tax( 1, cd_discount_price($product) ) ); ?>&nbsp;inkl. moms</h6>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endwhile; wp_reset_query(); ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <div class="swiper-button-next nav-pack-right">
            <i class="material-icons">keyboard_arrow_right</i>
        </div>
        <div class="swiper-button-prev nav-pack-left">
            <i class="material-icons">keyboard_arrow_left</i>
        </div>
    </div>
</section>
 */?>

<?php
if ( isset($text) ) {
    echo '<script>
            alert('.$text.');
        </script>';
} 
?>

<?php get_footer() ; ?>