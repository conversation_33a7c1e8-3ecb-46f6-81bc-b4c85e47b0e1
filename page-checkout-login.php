<?php
session_start();
get_header() ;
global $product;
define( 'WOOCOMMERCE_CART', true );
WC()->cart->calculate_totals();
?>
    <section class="checkout-wrapper">
        <div class="checkout-section max-width">
            <div class="checkout-left">
                <h2>Ditt konto</h2>
                <div class="checkout-inputs-half">
                    <div class="checkout-half-input">
                        <h3>Skapa ett konto</h3>
                        <form id="register-user-form" method="POST" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>">
                            <input type="hidden" name="action" value="cone_register_user" />
                            <input type="hidden" name="is_checkout_login" value="checkout_login" />
                            <div class="checkout-input">
                                <label for="register-name">Namn*</label>
                                <input type="text" name="register_name" id="register-name" class="checkout-login-input cone-required" required  autocomplete="off" />
                            </div>
                            <div class="checkout-input">
                                <label for="register-lastname">Efternamn*</label>
                                <input type="text" name="register_lastname" id="register-lastname" class="checkout-login-input cone-required" required  autocomplete="off" />
                            </div>
                            <div class="checkout-input">
                                <label for="login-mail">Email adress*</label>
                                <input type="email" name="register_email" id="user_login" class="checkout-login-input cone-required" required  autocomplete="off" />
                            </div>
                            <?php 
                            if ( isset($_SESSION['email_exists_error']) ) {
                                echo '<p>'. $_SESSION['email_exists_error'].'</p>';
                                unset($_SESSION['email_exists_error']);
                            }
                            if ( isset($_SESSION['username_exists_error']) ) {
                                echo '<p>'.  $_SESSION['username_exists_error'].'</p>';
                                unset($_SESSION['username_exists_error']);
                            }
                            ?>
                            <div class="checkout-input">
                                <label for="register-company">Företag (valfritt)</label>
                                <input type="text" name="register_company" class="checkout-login-input" id="register-company" readonly onfocus="this.removeAttribute('readonly');" />
                            </div>
                            <div class="checkout-input">
                                <label for="register-password">Välj Lösenord*</label>
                                <input type="password" name="register_password" id="register-password" class="checkout-login-input cone-required" required  autocomplete="off" />
                            </div>
                            <div class="checkout-input checkout-checkbox">
                                <label><input type="checkbox" name="accept_terms" value="" id="accept-terms">Jag accepterar de allmäna villkoren</label>
                            </div>
                            <?php 
                            if (isset($_SESSION['accept_error'])) {
                                echo '<p>'. $_SESSION['accept_error'].'</p>';
                                unset($_SESSION['accept_error']);
                            }
                            ?>
   <!--                          <div class="checkout-input checkout-checkbox">
                                <label><input type="checkbox" name="checkout_prem" value="prem">Prenumerera på Aladdins nyhetsbrev</label>
                            </div> -->
                        </form>
                    </div>
                    <h6>Eller</h6>
                    <div class="checkout-half-input">
                        <h3>Logga in</h3>
                        <form id="checkout-login-form" action="<?php echo esc_url(visionmate_get_login_url()); ?>" method="POST">
                            <div class="checkout-input">
                                <label for="c-l-name">Email adress</label>
                                <input type="text" name="log" id="c-l-name" class="checkout-login-input" />
                            </div>
                            <div class="checkout-input">
                                <label for="c-l-password">Lösenord</label>
                                <input type="password" name="pwd" id="c-l-password" class="checkout-login-input" readonly onfocus="this.removeAttribute('readonly');"   />
                            </div>
                            <a href="<?php echo esc_url(visionmate_get_login_url('lostpassword')); ?>">Glömt lösenordet?</a>
                            <?php 
                            if( $_SERVER['QUERY_STRING'] == 'login=failed' ) {
                                echo '<p>Fel email eller lösenord.</p>';
                            }
                            ?>
                       <!-- <div class="checkout-input checkout-checkbox">
                                <label><input type="checkbox" name="checkout_prem" value="prem">Prenumerera på Aladdins nyhetsbrev</label>
                            </div> -->
                            <input type="hidden" name="checkout_login" value="1">
                        </form>
                    </div>
                </div>
                <div class="checkout-continue checkout-register-login" data-form="">
                    <a href="#">Fortsätt</a>
                </div>
            </div>
            <div class="checkout-sidebar">
                <div class="checkout-sidebar-headline">
                    <i class="material-icons checkout-sidebar-toggle">close</i>
                    <h4>Ordersummering</h4>
                    <a href="<?php echo wc_get_cart_url(); ?>">Redigera</a>
                </div>
                <div class="checkout-sidebar-show">
                    <div class="checkout-sidebar-products">
                        <?php 
                        foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
                            $_product     = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );

                            if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_checkout_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
                                ?>
                        
                                <div class="checkout-sidebar-product">
                                    <img src="<?php echo wp_get_attachment_image_src( $_product->image_id )[0]; ?>">
                                    <div class="checkout-sidebar-content">
                                        <p><?php echo apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) . '&nbsp;'; ?></p>
                                        <div class="checkout-sidebar-product-info">
                                            <span>antal: <?php echo $cart_item['quantity']; ?></span>
                                            <h6><?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?></h6>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>
                    <div class="checkout-sidebar-info">
                    <div>
                        <strong>Subtotalt:</strong>
                        <strong><?php wc_cart_totals_subtotal_html(); ?></strong>
                    </div>
                    <?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
                        <div class="cart-discount coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
                            <p><?php wc_cart_totals_coupon_label( $coupon ); ?>:</p>
                            <p><?php wc_cart_totals_coupon_html( $coupon ); ?></p>
                        </div>
                    <?php endforeach; ?>
                    <?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
                        <div class="fee">
                            <p><?php echo esc_html( $fee->name ); ?>:</p>
                            <p><?php wc_cart_totals_fee_html( $fee ); ?></p>
                        </div>
                    <?php endforeach; ?>
                    <?php if ( wc_tax_enabled() && 'excl' === WC()->cart->get_tax_price_display_mode() ) : ?>
                        <?php if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) : ?>
                            <?php foreach ( WC()->cart->get_tax_totals() as $code => $tax ) : ?>
                                <div class="tax-rate tax-rate-<?php echo sanitize_title( $code ); ?>">
                                    <p><?php echo esc_html( $tax->label ); ?>:</p>
                                    <p><?php echo wp_kses_post( $tax->formatted_amount ); ?></p>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <div class="tax-total">
                                <p><?php echo esc_html( WC()->countries->tax_or_vat() ); ?>:</p>
                                <p><?php wc_cart_totals_taxes_total_html(); ?></p>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php do_action( 'woocommerce_review_order_before_order_total' ); ?>

                    <div class="order-total">
                        <strong><?php _e( 'Total', 'woocommerce' ); ?>:</strong>
                        <strong><?php wc_cart_totals_order_total_html(); ?></strong>
                    </div>

                    <?php do_action( 'woocommerce_review_order_after_order_total' ); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="big-icon-section max-width">
        <h2>Behöver du hjälp? Vi finns här för dig!</h2>
        <div class="big-icons">
            <div class="big-icons-content">
                <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Card.png' ) ); ?>">
                <span>Alladdins uthyrning till undsättning! Här är lite olika sätt att <a href="#">kontakta oss</a> på</span>
            </div>
            <div class="big-icons-content">
                <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Call.png' ) ); ?>">
                <span>Tala med oss direkt (Telefontid helgfri: mån-fre 09-12 samt 13-20.) <br/> på <?php the_field('aladdin-phone', 'option') ; ?></span>
            </div>
            <div class="big-icons-content">
                <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Contact.png' ) ); ?>">
                <span>Vi älskar emails! <a href="mailto:<?php the_field('aladdin-mail', 'option') ; ?>">Maila oss</a> vad du än har för frågor angående vad som helst</span>
            </div>
        </div>
    </section>


<?php get_footer() ; ?>
