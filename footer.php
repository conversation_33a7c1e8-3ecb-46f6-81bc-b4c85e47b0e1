<?php
/**
 * The template for displaying the footer
 *
 * @package cone
 */
?>

<footer>
<!--    <div class="contact-divider max-width">-->
<!--        <p>Vi väntar på din kontakt</p>-->
<!--        <a href="mailto:--><?php //echo get_field('aladdin-mail', 'option') ; ?><!--">Kontakta oss</a>-->
<!--    </div>-->
    <div class="footer ">
        <div class="max-width footer-content">
            <div class="footer-column">
                <h3>Aladdins Uthyrning</h3>
                <p>Enhagsvägen 10, <br/>
                    18740 Täby
                </p>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            <div class="footer-column">
                <h4>Villkor etc.</h4>
                <a href="<?php echo esc_url(home_url( '/hyresvillkor' ) ); ?>">Hyresvillkor</a>
<!--                <a href="#">Ändra order</a>-->
                <a href="<?php echo esc_url(home_url( '/mitt-konto/orders' ) ); ?>">Mina Ordrar</a>
                <a href="<?php echo esc_url(home_url( '/vanliga-fragor' ) ); ?>">Vanliga frågor</a>
            </div>
            <div class="footer-column">
                <h4>Sortiment</h4>
                <a href="<?php echo esc_url(home_url( '/shop' ) ); ?>">Hela Sortimentet</a>
                <?php
                $taxonomy     = 'product_cat';
                $orderby      = 'name';
                $show_count   = 0;      // 1 for yes, 0 for no
                $pad_counts   = 0;      // 1 for yes, 0 for no
                $hierarchical = 1;      // 1 for yes, 0 for no
                $title        = '';
                $empty        = 0;

                $args = array(
                    'taxonomy'     => $taxonomy,
                    // 'orderby'      => $orderby,
                    'show_count'   => $show_count,
                    'pad_counts'   => $pad_counts,
                    'hierarchical' => $hierarchical,
                    'title_li'     => $title,
                    'hide_empty'   => $empty,
                    //'parent'       => 0,
                );

                //$all_categories = get_categories( $args );

                $terms = get_categories( $args );
                $sorted_terms = array();
                $mjau = sort_terms_hierarchically( $terms, $sorted_terms );
                //error_log( print_r( $sorted_terms, true ) );
                //var_dump($sorted_terms[19]->children);
                foreach ($sorted_terms as $cat) {
                    if ($cat->slug === 'uncategorized' || $cat->slug === 'okategoriserad') {
                        continue;
                    }
                    $category_id = $cat->term_id;
                    ?>
                        <a href="<?php echo get_term_link($cat->slug, 'product_cat'); ?>" data-id="<?php echo $cat->term_id; ?>"><?php echo $cat->name; ?></a>
                    <?php
                }
                ?>
<!--                <a href="#">Bord och Stolar</a>-->
<!--                <a href="#">Dekor</a>-->
<!--                <a href="#">Glas och Porslin</a>-->
<!--                <a href="#">Färdiga Paket</a>-->
            </div>
            <div class="footer-column">
                <h4>Om Oss</h4>
                <a href="<?php echo esc_url(home_url( '/om-oss' ) ); ?>">Om Oss</a>
                <a href="<?php echo esc_url(home_url( '/kontakt' ) ); ?>">Kontakta Oss</a>
                <a href="<?php echo esc_url(home_url( '/blogg' ) ); ?>">Blogg</a>
            </div>
            <div class="footer-column">
                <h4>Kundservice</h4>
                <p class="cd-footer-p">Kundservice: 08- 664 20 00<br/>
                    Telefontid helgfri: <br/> mån-fre 09-12 samt 13-20.
                </p>
                <p>Maila: <a href="mailto:<EMAIL>"> <EMAIL></a></p>
            </div>
        </div>
    </div>
</footer>
    <?php wp_footer(); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-date-range-picker/0.16.0/jquery.daterangepicker.min.js"></script>

</body>
</html>