{"key": "group_59afe7aeb81b2", "title": "<PERSON><PERSON><PERSON><PERSON> uthyrning", "fields": [{"key": "field_59c3add48b9b2", "label": "Företagsinfo", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_59afe7c779cb3", "label": "Mail", "name": "aladdin-mail", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_59afe8b7ff4bb", "label": "Telefon", "name": "aladdin-phone", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_59c3aed58b9b4", "label": "Adress t.ex (Tegeluddsvägen 31)", "name": "aladdin-address", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_59c3aef78b9b5", "label": "<PERSON><PERSON> stad (t.ex Gärdet / Östermalm)", "name": "aladdin-city", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_59c3adf88b9b3", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_59c3b1019e9c8", "label": "Ö<PERSON>ttider butik vardagar", "name": "open-weekdays", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_59c3b2019e9ca", "label": "<PERSON><PERSON><PERSON><PERSON> helg butik", "name": "open-weekend", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_59c3b2279e9cb", "label": "Öppettider vardagar telefon", "name": "open-weekdays-phone", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_59c3b2fd9e9cd", "label": "Öppettider helg telefon", "name": "open-weekend-phone", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_59c3b8d8edc2d", "label": "<PERSON><PERSON> och leverans", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_59c3b8e4edc2e", "label": "<PERSON>nd hämtar i butik tider", "name": "customer-pick-up", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "sub_fields": [{"key": "field_59c3b8fbedc2f", "label": "Tid (t.ex 09:00 - 12:00)", "name": "customer-pick-up-time", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_59c3b8e4edc2e"}], "rows_per_page": 20}, {"key": "field_59c3ba398bf7a", "label": "<PERSON><PERSON><PERSON> levererar tider", "name": "aladdin-deliver", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "sub_fields": [{"key": "field_59c3ba5a8bf7b", "label": "Tid (t.ex 09:00 12:00)", "name": "aladdin-deliver-time", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_59c3ba398bf7a"}], "rows_per_page": 20}, {"key": "field_59c3baa775c40", "label": "Kund lämnar tillbaks tiider", "name": "customer-deliver", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "sub_fields": [{"key": "field_59c3bad175c41", "label": "Tid (t.ex 09:00 - 12:00)", "name": "customer-deliver-time", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_59c3baa775c40"}], "rows_per_page": 20}, {"key": "field_59c3bb0c75c42", "label": "<PERSON><PERSON><PERSON> hämtar upp tider", "name": "aladdin-pick-up", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "sub_fields": [{"key": "field_59c3bb3d75c43", "label": "Tid (t.ex 09:00 - 12:00)", "name": "aladdin-pick-up-time", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_59c3bb0c75c42"}], "rows_per_page": 20}, {"key": "field_5af58d3b087f5", "label": "Transportkostnad Text", "name": "transport-fee", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_5b7a876dca6ee", "label": "Välj tid info", "name": "choose-time-info", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_5ba8fa68fcf78", "label": "Endast offert info", "name": "is-offer-info", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "visual", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_5a61bda4308d1", "label": "Dagens meddelande", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_5a61bdbe308d2", "label": "Meddelande", "name": "daily-error", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_5a61bddc308d3", "label": "Info om leverans", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_5a61be03308d4", "label": "Info om leverans", "name": "info-about-delivery", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_6890ab3fa79e0", "label": "Betalningsalternativ", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_6890ab66a79e2", "label": "Kommentar om kortbetalningar", "name": "cod_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 15, "placeholder": "", "new_lines": ""}, {"key": "field_6890aba2a79e3", "label": "Kommentarer till fakturor", "name": "cheque_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 10, "placeholder": "", "new_lines": ""}, {"key": "field_6890aa48a3a28", "label": "<PERSON><PERSON><PERSON> och hantering", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_6890aad9a3a29", "label": "Text ovan<PERSON><PERSON><PERSON> kryss<PERSON>tan för villkor i kassan", "name": "terms_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 10, "placeholder": "", "new_lines": ""}, {"key": "field_5aedad57f167e", "label": "Tack för din order", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_5aedad6df167f", "label": "Meddelande på sidan <PERSON>", "name": "thank_you_text", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 1, "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 1}, {"key": "field_6890a6ed24e10", "label": "E-post text", "name": "email_thank_you_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 20, "placeholder": "", "new_lines": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "acf-options-header"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1754311808}