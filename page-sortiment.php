<?php get_header() ; ?>

    <section class="sortiment-section">
        <div class="max-width">
            <div class="sortiment-top">
                <?php the_content() ; ?>
            </div>
        </div>
        <div class="sortiment-grid">
            <?php
            $taxonomy     = 'product_cat';
            $orderby      = 'name';
            $show_count   = 0;      // 1 for yes, 0 for no
            $pad_counts   = 0;      // 1 for yes, 0 for no
            $hierarchical = 1;      // 1 for yes, 0 for no
            $title        = '';
            $empty        = 0;

            $args = array(
                'taxonomy'     => $taxonomy,
                // 'orderby'      => $orderby,
                'show_count'   => $show_count,
                'pad_counts'   => $pad_counts,
                'hierarchical' => $hierarchical,
                'title_li'     => $title,
                //'parent'       => 0,
                'hide_empty'   => $empty
            );

            $all_categories = get_categories( $args );
            $sorted_terms = array();
            $order = sort_terms_hierarchically( $all_categories, $sorted_terms );
            $cats = array_values($sorted_terms);
            //var_dump($sorted_terms);
            $count = 0;
            foreach ($cats as $key => $cat) {
                if ($cat->slug === 'uncategorized' || $cat->slug === 'okategoriserad') {
                    continue;
                }
                $thumbnail_id = get_term_meta( $cat->term_id, 'thumbnail_id', true );
                $image = wp_get_attachment_url( $thumbnail_id );

                    if($count == 0){
                        if( $key == 0 ){
                            $col1 = '<div class="category-column">';
                        }
                        $col1 .=     '<div class=" category-card">
                                            <div class="category-card-img background-img" style="background-image: url('. $image .')"></div>
                                            <div class="category-card-name">
                                                <div class="category-headline">
                                                    <h3>'. $cat->name .'</h3>
                                                    <a class="absolute-link" href="'. get_category_link($cat->term_id) . '"></a>
                                                </div>';
                                                foreach ($cat->children as $k => $child_cat) {
                                                    $col1 .= '<a href="'.get_term_link($child_cat->slug, "product_cat") . '">' .$child_cat->name. '</a>';
                                                }
                                   $col1 .= '</div>
                                      </div>';
                        $count++;
                        continue;
                    }
                    if($count == 1){
                        if( $key == 1 ){
                            $col2 = '<div class="category-column">';
                        }
                        $col2 .= '<div class=" category-card">
                                            <div class="category-card-img background-img" style="background-image: url('. $image .')"></div>
                                            <div class="category-card-name">
                                                <div class="category-headline">
                                                    <h3>'. $cat->name .'</h3>
                                                    <a class="absolute-link" href="'. get_category_link($cat->term_id) . '"></a>
                                                </div>';
                                             foreach ($cat->children as $k => $child_cat) {
                                                 $col2 .= '<a href="'.get_term_link($child_cat->slug, "product_cat") . '">' .$child_cat->name. '</a>';
                                             }
                                            $col2 .= '</div>
                                               </div>';
                        $count++;
                        continue;
                    }
                    if($count == 2){
                        if( $key == 2 ){
                            $col3 = '<div class="category-column">';
                        }
                        $col3 .= '<div class=" category-card">
                                            <div class="category-card-img background-img" style="background-image: url('. $image .')"></div>
                                            <div class="category-card-name">
                                                <div class="category-headline">
                                                    <h3>'. $cat->name .'</h3>
                                                    <a class="absolute-link" href="'. get_category_link($cat->term_id) . '"></a>
                                                                 </div>';
                                                     foreach ($cat->children as $k => $child_cat) {
                                                         $col3.= '<a href="'.get_term_link($child_cat->slug, "product_cat") . '">' .$child_cat->name. '</a>';
                                                     }
                                        $col3 .= '</div>
                                           </div>';
                        $count++;
                        continue;
                    }
                    if($count == 3){
                        if( $key == 3 ){
                            $col4 = '<div class="category-column">';
                        }
                        $col4 .= '<div class=" category-card">
                                            <div class="category-card-img background-img" style="background-image: url('. $image .')"></div>
                                            <div class="category-card-name">
                                                <div class="category-headline">
                                                    <h3>'. $cat->name .'</h3>
                                                    <a class="absolute-link" href="'. $cat->term_id . '"></a>
                                                </div>';
                                                foreach ($cat->children as $k => $child_cat) {
                                                    $col4 .= '<a href="'.get_term_link($child_cat->slug, "product_cat") . '">' .$child_cat->name. '</a>';
                                                }
                                               $col4 .= '</div>
                                                  </div>';
                        $count = 0;
                        continue;
                    }
                }

            $col1 .= '</div>';
            $col2 .= '</div>';
            $col3 .= '</div>';
            $col4 .= '</div>';

            $html = $col1 . $col2 . $col3 . $col4;
            echo $html;
            ?>
        </div>
    </section>

<?php get_footer() ; ?>