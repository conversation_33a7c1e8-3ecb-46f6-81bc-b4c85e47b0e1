<?php
/**
 * The template for displaying the header
 *
 * @package cone
 */
//session_start();
?><!DOCTYPE html>
<html <?php language_attributes(); ?> xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-date-range-picker/0.16.0/daterangepicker.min.css" rel="stylesheet">
    <link rel="icon" href="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/favi.png' ) ); ?>">
   
    <?php cone_og_meta_tags(); ?>

    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
    <?php if(get_field('daily-error', 'option')) : ?>
        <div class="daily-message">
            <div class="max-width">
                <?php echo get_field('daily-error', 'option') ; ?>
            </div>
        </div>
    <?php endif ; ?>
    <header <?php if ( is_page( array('checkout-page', 'checkout', 'confirm', 'kassa') ) ) echo 'style="display: none;"';  ?>>
<!--        <div class="upper-header">-->
<!--            <div class="max-width">-->
<!--                <div class="upper-header-icons">-->
<!--                    <span><i class="material-icons">phone</i> --><?php //echo get_field('aladdin-phone', 'option') ; ?><!--</span>-->
<!--                    <a href="mailto:--><?php //echo get_field('aladdin-mail', 'option') ; ?><!--"><i class="material-icons">email</i><span>--><?php //echo get_field('aladdin-mail', 'option') ; ?><!--</span></a>-->
<!--                </div>-->
<!--                <div>-->
<!--                    --><?php //wp_nav_menu( array( 'theme_location' => 'secondary', 'menu_class' => '' ) ); ?>
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="header-top">
            <div class="lower-header max-width">
                <div class="mobile-hamburger header-right header-sides">
                    <a class="cd-dropdown-trigger" href="#0"><i class="material-icons">menu</i></a>
                </div>
                <div class="header-left">
                    <div class="header-logo">
                        <a class="absolute-link" href="<?php echo esc_url(home_url()); ?>"></a>
                        <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/aladdins-uthyrning.jpg' ) ); ?>">
                    </div>
                    <div class="header-search">
                        <?php the_widget( 'WP_Widget_Search' ); ?>
                    </div>
                </div>
                <div class="header-right header-sides">
                    <?php if( is_user_logged_in() ) :
                        wp_nav_menu( array( 'menu' => 'logged-in', 'menu_class' => '' ) );
                    else :
                        wp_nav_menu( array( 'theme_location' => 'secondary', 'menu_class' => '' ) );
                    endif; ?>
                    <a href="#" class="mobile-search"><i class="material-icons">search</i></a>
                    <a class="cart-customlocation" href="<?php echo wc_get_cart_url(); ?>" title="<?php _e( 'View your shopping cart' ); ?>"><span><?php echo sprintf ( _n( '%d', '%d', WC()->cart->get_cart_contents_count() ), WC()->cart->get_cart_contents_count() ); ?></span><i class="material-icons">shopping_cart</i></a>
                </div>
            </div>
        </div>
        <div class="header-bottom">
            <div class="max-width">
                <a class="header-sortiment-link" href="<?php echo esc_url( get_permalink( wc_get_page_id( 'shop' ) ) ); ?>">Vårt sortiment</a>
                <?php
                $taxonomy     = 'product_cat';
                $orderby      = 'name';
                $show_count   = 0;      // 1 for yes, 0 for no
                $pad_counts   = 0;      // 1 for yes, 0 for no
                $hierarchical = 1;      // 1 for yes, 0 for no
                $title        = '';
                $empty        = 0;

                $args = array(
                    'taxonomy'     => $taxonomy,
                    // 'orderby'      => $orderby,
                    'show_count'   => $show_count,
                    'pad_counts'   => $pad_counts,
                    'hierarchical' => $hierarchical,
                    'title_li'     => $title,
                    'hide_empty'   => $empty,
                    //'parent'       => 0,
                );

                //$all_categories = get_categories( $args );

                $terms = get_categories( $args );
                $sorted_terms = array();
                $mjau = sort_terms_hierarchically( $terms, $sorted_terms );
                //error_log( print_r( $sorted_terms, true ) );
                //var_dump($sorted_terms[19]->children);
                foreach ($sorted_terms as $cat) {
                    $category_id = $cat->term_id;
                    if ($cat->slug === 'uncategorized' || $cat->slug === 'okategoriserad') {
                        continue;
                    }
                    ?>
                    <div class="menu-container">
                        <a class="show-sortiment" href="<?php echo get_term_link($cat->slug, 'product_cat'); ?>" data-id="<?php echo $cat->term_id; ?>"><?php echo $cat->name; ?></a>
                        <section class="category-section max-width <?php if(get_field('daily-error', 'option')) : ?>push-down<?php endif ; ?> ">
                            <div class="category-sidebar">
                                <a class="category-all-link" href="<?php echo get_term_link($cat->slug, 'product_cat');?>">Allt inom <?php echo $cat->name; ?></a>
                                <?php $i = 0; ?>
                                <?php foreach ($cat->children as $key => $subcat) : ?>
                                    <div class="category-item" data-img="http://localhost/aladdin/wp-content/uploads/2017/08/T_1_front.jpg" data-id="<?php echo $subcat->term_id; ?>">
                                        <a href="<?php echo get_term_link($subcat->slug, 'product_cat'); ?>" class="<?php if ( $i == 0 ) echo 'category-item-hover'; ?>"><?php echo $subcat->name; ?><i class="material-icons">keyboard_arrow_right</i></a>
                                    </div>
                                    <?php $i++; ?>                            
                                <?php endforeach; ?>
                            </div>
                            <div class="category-section-right">
                                <?php $count = 0; ?>
                                <?php foreach ($cat->children as $i => $subcat2) : 
                                $thumbnail_id = get_term_meta( $cat->term_id, 'thumbnail_id', true );
                                $image = wp_get_attachment_url( $thumbnail_id );
                                ?>
                                    <div class="under-cat-section <?php echo $subcat2->term_id . '-cat-item' ?>" style="<?php if ( $count != 0 ) echo 'display: none;'; ?>">
                                        <div class="under-cat-left  <?php if ( ! $subcat2->children ) echo 'no-under-cats'; ?>">
                                            <?php foreach ( $subcat2->children as $subsub ) : ?>
                                                <div class="under-cats">
                                                    <a href="<?php echo get_term_link($subsub->slug, 'product_cat'); ?>"><?php echo $subsub->name; ?></a>
                                                    <div class="inception-cats">
                                                        <?php foreach ( $subsub->children as $inception ) : ?>
                                                            <a href="<?php echo get_term_link($inception->slug, 'product_cat'); ?>"><?php echo $inception->name; ?></a>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <div class="under-cat-right background-img" style="background-image: url('<?php echo $image; ?>');"></div>
                                    </div>
                                    <?php $count++; ?>
                                <?php endforeach; ?>
                            </div>
                        </section>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
    </header>
    <div class="open-cart-section">
        <div class="open-cart-overlay overlay"></div>
        <div class="open-cart">
            <div class="cart-overlay" style="display: none">
                <p class="loading"></p>
            </div>
            <p class="close-open-cart">Forstätt handla</p>
            <h4>Varukorg <span class="cart-header" data-count="<?php echo  WC()->cart->get_cart_contents_count(); ?>">(<?php echo  WC()->cart->get_cart_contents_count(); ?>)</span></h4>
            <?php woocommerce_mini_cart(); ?>
        </div>
    </div>



    <div class="show-sortiment-section">

    </div>
    <?php
    if ( isset($_SESSION['email_exists_error']) || isset($_SESSION['username_exists_error']) ) {
        ?>
        <div class="modal-section" id="error-modal" style="display: block;">
            <div class="modal">
                <i class="material-icons" onclick="document.getElementById('error-modal').remove();">close</i>
                <p><?php echo (isset($_SESSION['email_exists_error'])) ? $_SESSION['email_exists_error'] : $_SESSION['username_exists_error']; ?></p>
            </div>
        </div>
        <?php
        if (isset($_SESSION['email_exists_error'])) {
            unset($_SESSION['email_exists_error']);
        }else{
            unset($_SESSION['username_exists_error']);
        }
    }


    if ( ! is_user_logged_in() ) :
    ?>
        <div class="modal-section register-modal">
            <div class="modal">
                <i class="material-icons close-modal">close</i>
                <h3>Registrera dig</h3>
                <p>Fyll i dina uppgifter nedan:</p>
                <form method="POST" id="header-register-form" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>">
                    <input type="hidden" name="action" value="cone_register_user" />
                    <div class="checkout-input">
                        <label for="register-name">Namn</label>
                        <input type="text" name="register_name" id="register-name" class="cone-required" required />
                    </div>
                    <div class="checkout-input">
                        <label for="register-lastname">Efternamn</label>
                        <input type="text" name="register_lastname" id="register-lastname" class="cone-required" required />
                    </div>
                    <div class="checkout-input">
                        <label for="login-mail">Email adress</label>
                        <input type="email" name="register_email" id="user_login" class="cone-required" required />
                    </div>
                    <div class="checkout-input">
                        <label for="register-company">Företag (valfritt)</label>
                        <input type="text" name="register_company" id="register-company" />
                    </div>
                    <div class="checkout-input">
                        <label for="register-password">Välj Lösenord</label>
                        <input type="password" name="register_password" id="register-password" class="cone-required" required />
                    </div>
                    <div class="checkout-input checkout-checkbox">
                        <label><input type="checkbox" name="accept_terms" value="" id="accept-terms-2">Jag accepterar de allmäna villkoren</label>
                    </div>         
                    <div class="already-customer">
                        <span>Har du redan ett konto?</span>
                        <a data-type="register" href="#">Logga in</a>
                    </div>
                    <button type="submit">Skapa mitt konto</button>
                </form>
            </div>
        </div>
        <div class="modal-section login-modal">
            <div class="modal login-modal">
                <i class="material-icons close-modal">close</i>
                <h3>Logga in</h3>
                <!-- https://aladdins.wpengine.com/wp-login.php?wpe-login=true -->
                <p>Fyll i dina uppgifter nedan:</p>
                <form name="loginform" id="loginform" action="<?php echo esc_url(visionmate_get_login_url()); ?>" method="post">
                    <div class="checkout-input">
                        <label for="login-mail">Email adress</label>
                        <input type="email" name="log" id="user_login" />
                    </div>
                    <div class="checkout-input">
                        <label for="login-password">Lösenord</label>
                        <input type="password" name="pwd" id="user_pass" />
                    </div>
                    <div class="already-customer">
                        <span>Ny kund?</span>
                        <a href="#">Registrera dig</a>
                    </div>
                    <button type="submit" name="wp-submit">Logga in</button>
                    <input type="hidden" name="redirect_to" value="<?php echo home_url(); ?>">
                </form>
                <a href="<?php echo esc_url(visionmate_get_login_url('lostpassword')); ?>" class="forgot-password">Glömt lösenord?</a>
            </div>
        </div>
    <?php 
    endif;


    $menu_cats = get_categories( $args );
    $sorted_menu_cats = array();
    $mjau = sort_terms_hierarchically( $menu_cats, $sorted_menu_cats );


    ?>
    <div class="cd-dropdown-wrapper">
        <nav class="cd-dropdown">
            <h2>Aladdins uthyrning</h2>
            <a href="#0" class="cd-close">Close</a>
            <ul class="cd-dropdown-content">
                <li>
                    <form role="search" method="get" class="search-form cd-search" action="<?php echo home_url( '/' ); ?>">
                            <input type="search" class="search-field mobile-s-field"
                                   placeholder="<?php echo esc_attr_x( 'Sök efter vad som helst här… ', 'placeholder' ) ?>"
                                   value="<?php echo get_search_query() ?>" name="s"
                                   title="<?php echo esc_attr_x( 'Search for:', 'label' ) ?>" />
                    </form>
                </li>
                <li class="has-children">
                    <a href="#0" class="cone-sortiment">Sortiment</a>

                    <ul class="cd-secondary-dropdown is-hidden">
                        <li class="go-back"><a href="#0">Meny</a></li>
                        <li class="see-all"><a href="<?php echo home_url( get_permalink( wc_get_page_id( 'shop' ) ) ); ?>">Hela sortimentet</a></li>
                        <?php  foreach ($sorted_menu_cats as $key1 => $menu_cat) : ?>
                            <?php if ( ! empty($menu_cat->children) ) : ?>
                                <li class="has-children">
                                    <a href="#0"><?php echo $menu_cat->name; ?></a>
                                    <ul class="is-hidden">
                                        <li class="go-back"><a href="#0">Hela sortimentet</a></li>
                                        <li class="see-all"><a href="<?php echo get_term_link($menu_cat->slug, 'product_cat'); ?>">Alla <?php echo $menu_cat->name; ?></a></li>
                                        <?php foreach ($menu_cat->children as $key2 => $menu_subcat) : ?>
                                            <?php if ( ! empty($menu_subcat->children) ) : ?>
                                                <li class="has-children">
                                                    <a href="#0"><?php echo $menu_subcat->name; ?></a>
                                                    <ul class="is-hidden">
                                                        <li class="go-back"><a href="#0"><?php echo $menu_cat->name; ?></a></li>
                                                        <li class="see-all"><a href="<?php echo get_term_link($menu_subcat->slug, 'product_cat'); ?>">Alla <?php echo $menu_subcat->name; ?></a></li>
                                                        <?php foreach ($menu_subcat->children as $key3 => $menu_subsub) : ?>
                                                            <?php if ( ! empty($menu_subsub->children) ) : ?>
                                                                <li class="has-children">
                                                                    <a href="#0"><?php echo $menu_subsub->name; ?></a>
                                                                    <ul class="is-hidden">
                                                                        <li class="go-back"><a href="#0"><?php echo $menu_subcat->name; ?></a></li>
                                                                        <li class="see-all"><a href="<?php echo get_term_link($menu_subsub->slug, 'product_cat'); ?>">Alla <?php echo $menu_subsub->name; ?></a></li>
                                                                        <?php foreach ($menu_subsub->children as $key3 => $menu_inception) : ?>
                                                                            <li><a href="<?php echo get_term_link($menu_inception->slug, 'product_cat'); ?>"><?php echo $menu_inception->name; ?></a></li>
                                                                        <?php endforeach; ?>
                                                                    </ul>
                                                                </li>
                                                            <?php else: ?>
                                                                <li><a href="<?php echo get_term_link($menu_subsub->slug, 'product_cat'); ?>"><?php echo $menu_subsub->name; ?></a></li>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </li>
                                            <?php else: ?>
                                                <li><a href="<?php echo get_term_link($menu_subcat->slug, 'product_cat'); ?>"><?php echo $menu_subcat->name; ?></a></li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ul>
                                </li>
                            <?php else: ?>
                                <li><a href="<?php echo get_term_link($menu_cat->slug, 'product_cat'); ?>"><?php echo $menu_cat->name; ?></a></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                </li>
                <li class="cd-divider">Andra sidor</li>

                <li><a href="<?php echo esc_url( home_url( '/kontakt' ) ); ?>">Kontakta oss</a></li>
                <li><a href="<?php echo esc_url( home_url( '/vanliga-fragor' ) ); ?>">Vanliga frågor</a></li>
                <li><a href="<?php echo esc_url( home_url( '/transportinformation' ) ); ?>">Transport</a></li>
                <li><a href="<?php echo esc_url( home_url( '/hyresvillkor' ) ); ?>">Hyresvillkor</a></li>
                <?php if( is_user_logged_in() ) : ?>
                    <li><a href="<?php echo esc_url( home_url( '/mitt-konto' ) ); ?>">Mitt Konto</a></li>
                <?php else : ?>
                    <li class="open-register"><a href="#register">Registrera dig</a></li>
                    <li class="open-login"><a href="#logga-in">Logga in</a></li>
                <?php endif; ?>

                <!-- other list items here -->

            </ul> <!-- .cd-dropdown-content -->
        </nav> <!-- .cd-dropdown -->
    </div>
    <div class="mobile-shop-header">
        <div class="mobile-shop-header-left mobile-shop-menu-trigger cd-dropdown-trigger">
            <i class="fa fa-home" aria-hidden="true"></i>
            <p>Shop</p>
        </div>
        <div class="mobile-shop-header-right">
            <i class="fa fa-phone" aria-hidden="true"></i>
            <p><?php echo get_field('aladdin-phone', 'option') ; ?></p>
        </div>
    </div>


  
