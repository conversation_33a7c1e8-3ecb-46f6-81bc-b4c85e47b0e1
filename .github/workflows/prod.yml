on: 
  push:
    branches:
      - main
name: 🚀 Deploy website to PROD on push
jobs:
  web-deploy:
    name: 🎉 Deploy to PROD
    runs-on: ubuntu-latest
    env:
      AWS_REGION: eu-north-1
      AWS_INSTANCE_SECURITY_GROUP_ID: sg-004099e118ddd73d1
      AWS_REMOTE_HOST: ec2-13-51-149-190.eu-north-1.compute.amazonaws.com
      AWS_REMOTE_USER: ubuntu
      SERVER_PATH: /var/www/aladdinsuthyrning.se/web/wp-content/themes/aladdin/
      SERVER_USER_AND_GROUP: web2:client1
    steps:

      - name: 🔨 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET }}
          aws-region: ${{ env.AWS_REGION }}

      - name: 🌎 Get Runner IP address
        id: ip
        uses: haythem/public-ip@v1.3

      - name: 🌎 Whitelist Runner IP address
        run: |
          aws ec2 authorize-security-group-ingress \
            --group-id $AWS_INSTANCE_SECURITY_GROUP_ID \
            --protocol tcp \
            --port 22 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32

      - name: 🚚 Get latest code
        uses: actions/checkout@v3

      - name: Use Node.js 18
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - name: 🔨 Build Project
        run: |
          npm install
          npm run build

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_KEY }}
          ARGS: '-avzr --rsync-path="sudo rsync" --delete'
          REMOTE_HOST: ${{ env.AWS_REMOTE_HOST }}
          REMOTE_USER: ${{ env.AWS_REMOTE_USER }}
          TARGET: ${{ env.SERVER_PATH }}
          EXCLUDE: "/node_modules/"
          SCRIPT_AFTER: |
            sudo chown -R ${{ env.SERVER_USER_AND_GROUP }} ${{ env.SERVER_PATH }}

      - name: 🌎 Revoke Runner IP address
        run: |
          aws ec2 revoke-security-group-ingress \
            --group-id $AWS_INSTANCE_SECURITY_GROUP_ID \
            --protocol tcp \
            --port 22 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32