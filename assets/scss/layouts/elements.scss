body{
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: $proxima;
  padding: 0 !important;
}

h3{
  color: $blue;
  font-size: 24px;
  font-weight: bold;
  line-height: 29px;
  margin: 0;
}

.max-width{
  max-width: 1150px;
  width: calc(100% - 60px);
  margin: 0 auto;
  padding: 0 30px;


  @include breakpoint(tablet) {
    padding: 0 15px;
    width: calc(100% - 30px);
  }
}

.background-img{
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.absolute-link{
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  z-index: 2;
}

.overlay{
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  background: rgba(0,0,0,0.8);
}

.category-section{
  display: flex;
  position: relative;
  right: 0;
  left: 0;
  background: white;
  max-width: 100%;
  padding: 0;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 190px);
}

.push-down{
  top: 150px;
}

.category-sidebar{
  border-right: 1px solid #EEEEEE;
  background-color: #ffffff;
  min-width: 275px;
  padding: 10px 0 10px 60px;

  .category-all-link{
    display: block;
    margin: 10px 15px 10px 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    text-decoration: none;
    font-size: 17px;
    color: $blue;

    transition: 0.2s ease-in-out;

    &:hover{
      color: darken($blue, 20%);
    }
  }
}

.category-section-right{
  padding: 0 0 0 10px;
  width: calc(100% - 10px);
  position: relative;
}

.category-item{
  a{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #000;
    font-size: 15px;
    font-weight: bold;
    line-height: 12px;
    text-decoration: none;
    height: 36px;
    padding: 0 10px 0 20px;
    transition: 0.1s ease-in-out;

    // &:hover{
    //   background-color: #F2F2F2;
    //   color: $blue;
    // }
  }

  i{
    font-size: 20px;
  }
}

.category-item-hover {
  background-color: #F2F2F2;
  color: $blue !important;
}

.top-category-grid{
  display: flex;
  height: 100%;
}

.category-big{
  width: 100%;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  overflow: hidden;

  &:hover{
    .overlay{
      background: rgba(0,0,0,0.5);
    }
  }

  .overlay{
    background: rgba(0,0,0,0.3);
    transition: 0.2s ease-in-out;
  }
}

.category-background{
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
}

.categories-small{
  min-width: 500px;
  display: flex;
  flex-direction: column;

  @include breakpoint(xl) {
    min-width: 400px;
  }
}

.top-cat-item{
  padding: 16px;
  width: calc(100% - 32px);
  margin-bottom: 50px;
  position: relative;

  h4{
    color: #FFFFFF;
    margin: 0;
    font-size: 28px;
    font-weight: 300;
    line-height: 40px;
    max-width: 600px;
  }
}

.absolute-top-cat{
  position: absolute;
  bottom: 0;
  z-index: 1;
  right: 0;
  left: 0;
}

.cat-small-content{
  margin-bottom: 10px;
  height: 100%;
  display: flex;
  align-items: flex-end;
  position: relative;
  overflow: hidden;

  &:last-child{
    margin-bottom: 0;
  }

  &:hover{
    .top-cat-item{
      bottom: 0;
    }
  }

  .top-cat-item{
    margin: 0;
    background-color: rgba(74, 144, 226, 0.9);
    text-align: left;
    position: absolute;
    bottom: -100%;
    left: 0;
    width: calc(100% - 30px);
    transition: 0.3s ease-in-out;


    h4{
      color: #FFFFFF;
      margin: 0;
      font-size: 13px;
      font-weight: 600;
      line-height: 24px;
      max-width: 100%;
      text-transform: uppercase;
    }
    p{
      margin: 5px 0 0;
      color: white;
      font-size: 20px;
      line-height: 27px;
      max-width: 375px;
    }
  }
}

.under-cat-section{
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100%);
  background: white;
  padding: 0;
  z-index: 3;
  justify-content: space-between;
  display: flex;
}

.under-cat-right{
  min-width: 310px;
}

.under-cat-left{
  padding: 30px 40px 40px;

  a{
    color: $blue;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    text-decoration: none;
    transition: 0.2s ease-in-out;

    &:hover{
      color: darken($blue, 20%);
    }
  }
}

.under-cats{
  margin-bottom: 20px;
}

.inception-cats{
  margin-top: 4px;

  a{
    font-weight: normal;
    line-height: 17px;
    font-size: 14px;
    margin-right: 10px;
  }
}

.facebook-banner{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background-color: #3E2FF9;
  color: #FFFFFF;
  font-size: 22px;
  line-height: 26px;
  margin-top: 20px;

  p{
    max-width: 369px;
    margin: 0;
  }
  i{
    font-size: 45px;
  }
  span{
    font-weight: bold;
  }
}

.best-seller-section{
  padding: 100px 0;
  background-color: #fff;

  @include breakpoint(desktop) {
    padding: 60px 0;
  }
  @include breakpoint(tablet) {
    padding: 45px 0 60px;
  }
  @include breakpoint(mobile) {
    padding: 30px 0 60px;
  }

  .max-width{
    position: relative;
  }

  h3{
    color: #000;
    font-size: 24px;
    font-weight: 600;
    line-height: 29px;
    position: relative;
    margin-right: 60px;

    @include breakpoint(tablet) {
      font-size: 16px;
      line-height: 19px;
      font-weight: normal;
    }
  }

  .swiper-button-next, .swiper-button-prev{
    top:58%;
    color: #9b9b9b;
    background: white;
    border-radius: 100px;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border: 0 solid rgba(0,0,0,0.01);

    @include breakpoint(tablet) {
      width: 40px;
      height: 40px;

      i{
        font-size: 17px;
      }
    }
  }
  .swiper-button-disabled{
    display: none;
  }
  .swiper-button-next{
    right: 5px;

    @include breakpoint(tablet) {
      right: 2px;
    }
  }
  .swiper-button-prev{
    left: 5px;

    @include breakpoint(tablet) {
      left: 2px;
    }
  }
}

.cd-mobile-hide{
  @include breakpoint(mobile) {
    display: none;
  }
}

.best-seller-top{
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 19px;
  justify-content: space-between;

  @include breakpoint(tablet) {
    padding-bottom: 7px;
  }

  a{
    color: #000;
    font-size: 16px;
    line-height: 19px;
    text-align: right;
    transition: 0.2s ease-in-out;

    &:hover{
      color: $blue;
    }

    @include breakpoint(tablet) {
      font-size: 10px;
    }
  }
}

.best-seller-top-headlines{
  display: flex;
}

.best-seller-swiper{
  margin-top: 23px;


  @include breakpoint(laptop) {
    padding-bottom: 30px;
  }

  .swiper-wrapper{
    padding-bottom: 3px;
  }

  .swiper-slide{
  }
  .swiper-pagination{
    display: none;
    bottom: 0;

    @include breakpoint(laptop) {
      display: block;
    }
  }
  .swiper-pagination-bullet{
    opacity: 0.1;
  }
  .swiper-pagination-bullet-active {
    background-color: #3E3E3C;
    opacity: 1;
  }
}

.product-card{
  display: flex;
  text-align: center;
  background: white;
  flex-direction: column;
  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
  border-radius: 2px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  transition: 0.2s ease-in-out;
  justify-content: space-between;
  min-height: 372px;
  height: auto;

  @include breakpoint(mobile) {
    min-height: 245px;
  }

  &:hover{
    .product-card-excerpt{
      opacity: 1;
    }
    box-shadow: 0 5px 12px 2px rgba(0,0,0,.2);
  }

  img{
    height: auto;
    width: auto;
    max-height: 80%;
    max-width: 60%;
  }
}

.product-card-top{
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 5px 20px 0;
  height: 180px;

  @include breakpoint(mobile) {
    height: 90px;
  }
}

.product-card-icons{
  position: absolute;
  width: calc(100% - 40px);
  top: 15px;
  left: 20px;
  display: flex;
  justify-content: space-between;
  z-index: 2;

  i{
    font-size: 20px;
    cursor: pointer;
    color: white;
  }
}

.product-card-content{
  position: relative;
  padding: 0 15px 20px;
  border-bottom: 1px solid #eeeeee;

  @include breakpoint(mobile) {
    padding: 10px;
  }

  p{
    color: #000;
    font-size: 18px;
    line-height: 20px;
    text-align: left;
    margin: 0 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    max-height: 50px;
    -webkit-line-clamp: 2;
    height: 40px;
    width: 100%;

    @include breakpoint(mobile) {
      font-size: 12px;
      font-weight: 600;
      line-height: 14px;
      max-height: 29px;
    }
  }
  span{
    color: #000;
    font-size: 15px;
    line-height: 17px;
    text-align: center;
    display: flex;
    justify-content: flex-start;

    @include breakpoint(mobile) {
      font-size: 12px;
      font-weight: bold;
      line-height: 14px;
    }
  }
  bdi {
    display: flex;
  }
  del{
    color: #FD5459;
    span{
      color: #FD5459;
      font-weight: normal;
    }
    .woocommerce-Price-amount{
      margin-right: 10px;
    }
  }
  ins{
    text-decoration: none;
  }
  h6{
    color: #000;
    font-size: 15px;
    line-height: 14px;
    text-align: left;
    font-weight: normal;
    margin: 6px 0 0 0;
    display: flex;
    align-items: center;

    @include breakpoint(mobile) {
      font-size: 12px;
      font-weight: bold;
      line-height: 14px;
    }

    .vat-span{
      margin-left: 5px;
    }
  }
  .woocommerce-Price-amount{
    font-weight: bold;
  }

  .product-card-sku{
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 6px;
    text-align: left;
    line-height: 20px;
  }
}


.product-card-add{
  padding: 17px 20px 20px;

  @include breakpoint(mobile) {
    padding: 10px;
  }

  p, .product_type_simple{
    margin: 0;
    color: #4A90E2;
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    cursor: pointer;
    transition: 0.2s ease-in-out;
    text-decoration: none;

    &:hover{
      color: darken(#4A90E2, 20%);
    }

    @include breakpoint(mobile) {
      font-weight: normal;
    }
  }

  .added{
    position: relative;

    &:after{
      font-family: 'Material Icons';
      content: 'check';
      position: absolute;
      right: -24px;
      top: 1px;
      font-size: 18px;
      color: #61D19D;
      transition: 0.2s ease-in-out;
    }
  }

  .wc-forward{
    display: none !important;
  }
}

.product-card-excerpt{
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  text-align: left;
  padding: 0 20px;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
  height: 100%;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  width: calc(100% - 40px);

  @include breakpoint(small) {
    display: none;
  }

  p{
    color: #000;
    font-size: 16px;
    font-weight: normal;
    line-height: 20px;
    margin: 0 0 10px;
    max-height: 122px;
    -webkit-line-clamp: 5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    white-space: initial;
    display: -webkit-box;


    &:last-child{
      margin: 0;
    }
  }
  .cd-product-card-excerpt-link{
    font-weight: 600;
    color: $blue;
  }
}

.navigation-arrows{
  i{
    font-size: 32px;
    opacity: 0.4;
    cursor: pointer;
  }
}


.subcat{
  display: none;
}

.add-remove-card{
  display: flex;
  align-items: center;
  justify-content: space-between;

  input{
    width: 45px;
    text-align: center;
    color: #3E3E3C;
    line-height: 17px;
    border: none;
    outline: 0;
    font-size: 16px;
    font-weight: 600;
    height: 100%;
  }
  
  i{
    color: $blue;
    cursor: pointer;
    transition: 0.2s ease-in-out;

    &:hover{
      color: darken($blue, 20%);
    }
  }
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
  }
}

.woocommerce-breadcrumb{
  display: flex;
  align-items: center;
  list-style: none;
  padding: 22px 0 0 !important;
  margin: 0;
  line-height: 19px;
  color: #3E3E3C;
  font-weight: bold;
  font-size: 16px !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  @include breakpoint(tablet) {
    font-size: 13px !important;
  }
  @include breakpoint(mobile) {
    font-size: 10px !important;
  }

  a{
    color: #A6A6A6 !important;
    margin: 0 10px;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    transition: 0.2s ease-in-out;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover{
      color: #3E3E3C !important;
    }

    @include breakpoint(tablet) {
      font-size: 13px ;
    }

    @include breakpoint(mobile) {
      font-size: 10px;
      margin: 0;
    }

    &:first-child{
      margin-left: 0;
    }
  }
}

.loading{
  position: relative;
  font-size: 70px;
  color: $blue;
  line-height: 0;
  margin: 0;
}

.loading:after {
  content: ' .';
  animation: dots 1s steps(5, end) infinite;}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);}
  40% {
    color: $blue;
    text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);}
  60% {
    text-shadow:
            .25em 0 0 $blue,
            .5em 0 0 rgba(0,0,0,0);}
  80%, 100% {
    text-shadow:
            .25em 0 0 $blue,
            .5em 0 0 $blue;
  }
}

.cd-woocommerce-login{
  max-width: 600px;
  padding-top: 45px;

  .woocommerce-Input{
    height: 48px;
  }
}


#cookie-consent-block {
  position: fixed;
  top:auto;
  bottom: 0;
  background: #D0021B !important;

  a{
    color: $blue;
  }
  .close-cookie-block{
    color: white;
  }
}

/*-----------------PAGINATION STYLE--------------------*/

.woocommerce-pagination{

  ul{
    border: none !important;
  }

  li{
    padding: 3px !important;
    border: none !important;
  }

  a{
    font-size: 18px;
    line-height: 28px;
    color: $blue;
    font-weight: 700 !important;
    text-decoration: none;
    padding: 4px 15px;
    margin: 0;
    background-color: #ecf1ff;
    border: 0;
    border-radius: 4px;
    vertical-align: top;
    display: inline-block;
    transition: 0.2s ease-in-out;

    @include breakpoint(small) {
      padding: 0 11px;
      font-size: 14px;
    }
    @include breakpoint(mobile) {
      padding: 0 9px;
      line-height: 22px;
    }

    &:hover{
      color: white !important;
      background-color: $blue !important;
    }
  }
  .current{
    color: white !important;
    background-color: $blue !important;
    border-radius: 4px;
    font-weight: 700 !important;
  }
}

.algolia-autocomplete{
  @include breakpoint(mobile) {
    max-height: 237px;
    overflow: scroll;
  }
}



//ADMIN STYLING----------------------------------------------

.closed-results{
  input{
    width: 83%;
  }
}


.ui-sortable-handle {

  .post_type_thumbnail{

    width: 64px;
    text-align: center;
    height: 64px;
  }

  img{
    width: auto !important;
    height: auto;
    max-width: 64px;
    max-height: 64px;
  }
}