.shop-section{
  padding: 0 0 60px 0;
  background-color: #fff;
}

.woocommerce-products-header{
  background: white;
  display: none;

  @include breakpoint(xl) {
    display: block;
  }

  h1{
    margin: 10px 0 0 0;
    color: #3E3E3C;
    font-size: 24px;
    font-weight: normal;
    line-height: 46px;
    text-transform: uppercase;
    text-align: center;

    span{
      font-weight: bold;
    }
  }
}

.the-shop{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.shop-sidebar{
  overflow-y: scroll;
  min-width: 220px;
  margin-right: 20px;
  max-height: calc(100vh - 18px);
  padding: 10px 10px 5px;
  background: #fff;
  -webkit-box-shadow: 0 1px 0 0 rgba(0,0,0,.06);
  box-shadow: 0 1px 0 0 rgba(0,0,0,.06);
  position: sticky;
  top: 0;
  border: 1px solid #ddd;

  @include breakpoint(xl) {
    display: none;
  }

  a{
    text-decoration: none;
  }

  img{
    display: none;
  }
  li{
    list-style: none;
  }
  mark {
    background: transparent;
    color: #3E3E3C;
  }
}

.shop-top-cats{

  &:last-child{
    margin-bottom: 30px;
  }
}

.shop-top-cat{
  justify-content: space-between;
  text-decoration: none;
  display: flex;
  background: #e6e5e5;
  border-radius: 2px;
  margin-bottom: 5px;
  color: #000;
  height: 35px;
  align-items: center;
  box-sizing: border-box;
  padding: 0 5px 0 10px;
  font-size: 14px;
  transition: 0.2s ease-in-out;

  &:hover{
    color: $blue;
  }

  i{
    font-size: 20px;
    color: #777;
    transition: 0.2s ease-in-out;

    &:hover{
      color: black;
    }
  }
}

.shop-top-cats-active{
  .shop-top-cat{
    color: white;
    font-weight: bold;
    background: $blue;

    i{
      transform: rotate(90deg);
      color: white;
    }
  }

  .shop-second-cat-a{
    display: flex;
  }
}

.shop-second-cat-a{
  justify-content: space-between;
  display: none;
  border-radius: 2px;
  color: #000;
  height: 34px;
  align-items: center;
  box-sizing: border-box;
  padding: 0 5px 0 18px;
  font-size: 14px;
  transition: .2s ease-in-out;

  &:hover{
    color: $blue;
  }

  i{
    font-size: 20px;
    color: #777;
    transition: 0.2s ease-in-out;

    &:hover{
      color: black;
    }
  }
}

.shop-second-cat-active{
  .shop-second-cat-a{
    color: $blue;
    font-weight: bold;

    i{
      transform: rotate(90deg);
    }
  }
  .shop-third-cat{
    display: flex;
  }
}

.shop-third-cat{
  justify-content: space-between;
  display: none;
  border-radius: 2px;
  color: #000;
  height: auto;
  align-items: center;
  box-sizing: border-box;
  font-size: 14px;
  transition: .2s ease-in-out;
  padding: 5px 5px 5px 15px;
  margin-left: 18px;
  border-left: 1px solid #e5e5e5;

  &:hover{
    color: $blue;
  }

  i{
    font-size: 20px;
    color: #777;
  }
}

.shop-third-cat-active{

  .shop-third-cat{
    color: $blue;
    font-weight: bold;
    display: flex;
  }
  .shop-fourth-cat{
    display: flex;
  }

  i{
    transform: rotate(90deg);
  }
}

.shop-fourth-cat{
  justify-content: space-between;
  display: none;
  border-radius: 2px;
  color: #000;
  height: auto;
  align-items: center;
  box-sizing: border-box;
  font-size: 14px;
  transition: .2s ease-in-out;
  padding: 5px 5px 5px 15px;
  margin-left: 35px;
  border-left: 1px solid #e5e5e5;
}

.shop-fourth-cat-active{
  color: $blue;
}

.shop-grid{
  width: 100%;

  ul{
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
  }

  .product-card{
    width: calc(33.3% - 18px);
    margin: 0 8px 16px;

    @include breakpoint(desktop) {
      width: calc(33.3% - 18px);
    }
    @include breakpoint(medium) {
      width: calc(50% - 12px);
      margin: 0 5px 12px;
    }
  }
}

.shop-sidebar-headline{
  color: #3E3E3C;
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  text-transform: uppercase;
  border-bottom: 1px solid #3e3e3e;
  display: inline-block;
  padding-bottom: 1px;
  margin: 0 0 22px 0;
}

.woocommerce-loop-category__title{
  color: #000;
  font-size: 16px;
  line-height: 19px;
  margin: 0 0 10px 0;
  text-decoration: none;
  font-weight: normal;
}

.shop-grid-filter{
  display: flex;
  margin-bottom: 20px;
  padding-right: 8px;
  justify-content: space-between;
  border-bottom: 1px solid #DDDDDD;
  padding-bottom: 13px;
  align-items: flex-end;

  @include breakpoint(small) {
    flex-direction: column;
    align-items: flex-start;
  }

  span{
    color: #000;
    font-size: 14px;
    line-height: 17px;
    text-transform: uppercase;

    @include breakpoint(small) {
      display: block;
      margin-bottom: 10px;
    }
  }

  .woocommerce-ordering{
    margin: 0;
  }

  p{
    color: #3E3E3C;
    font-size: 15px;
    line-height: 18px;
    margin: 0 10px 0 0;
  }
  select{
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0;
    border: 1px solid #d7d7d7;
    background-color: white;
    padding: 12px 40px 12px 20px;
    color: #3E3E3C;
    font-size: 15px;
    line-height: 15px;
    background-image: url('../../images/down-arrow.png');
    background-size: 10px;
    background-repeat: no-repeat;
    background-position: right 12px center;
  }
  div{
    display: flex;
    align-items: center;
  }
}

.shop-grid-filter-sort{

  @include breakpoint(xl) {
    display: none !important;
  }
}

.sortiment-section{
  background-color: #F8F8F8;
  padding: 50px 0 100px;
}

.sortiment-top{
  h1{
    color: #000;
    font-size: 42px;
    font-weight: bold;
    line-height: 46px;
    margin: 0 0 22px 0;
  }
  p{
    max-width: 900px;
    color: #000;
    font-size: 18px;
    font-weight: 300;
    line-height: 26px;
    margin: 0;
  }
}

.sortiment-grid{
  display: flex;
  flex-wrap: wrap;
  max-width: 1165px;
  width: calc(100% - 45px);
  margin: 48px auto 0;
  padding: 0 15px;
  align-items: flex-start;

  @include breakpoint(tablet) {
    width: calc(100% - 15px);
    padding: 0 7.5px;
  }

  .category-column{
    margin: 0 7.5px;
    width: calc(25% - 15px);

    @include breakpoint(medium) {
      width: calc(33.3% - 15px);
    }
    @include breakpoint(small) {
      width: calc(50% - 15px);
    }
  }

  .category-card{
    margin: 0 0 15px;
    width: 100%;
    position: relative;

    a{
      display: block;
      color: #000;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      text-decoration: none;
      margin: 8px 0 0 0;
      transition: 0.2s ease-in-out;

      &:hover{
        color: $blue;
      }
    }
  }
}

.shop-cat-wrapper{
  position: relative;
  padding: 0 30px;

  @include breakpoint(mobile) {
    padding: 0 10px;
  }

  .swiper-button-disabled{
    display: none;
  }
}

.top-cat-next {
  right: -13px;
  position: absolute;
  top: 0;
  margin: auto;
  bottom: 0;
  height: 70px;
  cursor: pointer;
  overflow: hidden;

  i {
    font-size: 40px;
    opacity: 0.3;

    @include breakpoint(desktop) {
      width: 15px;
      display: flex;
      justify-content: center;
    }
  }
}

.top-cat-prev{
  left: -13px;
  position: absolute;
  top: 0;
  margin: auto;
  bottom: 0;
  height: 70px;
  cursor: pointer;
  overflow: hidden;

  i{
    font-size: 40px;
    opacity: 0.3;

    @include breakpoint(desktop) {
      width: 15px;
      display: flex;
      justify-content: center;
    }
  }
}

.shop-cat-section{
  max-width: 920px;
  margin: 0 auto;
}

.shop-cat-swiper{
  padding: 18px 0;

  .swiper-wrapper{
    align-items: flex-start;
  }
}

.category-headline{
  position: relative;

  a{
    margin: 0 !important;
  }

  h3{
    font-size: 18px;
    font-weight: 600;
    line-height: 16px;
    margin: 0 0 15px 0;

    @include breakpoint(medium) {
      font-size: 14px;
    }
  }
}

.top-category{
  text-align: center;
  position: relative;
  transition: 0.2s ease-in-out;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;

  &:hover{
      transform: scale(1.1);
  }

  img{
    height: auto;
    width: auto;
    max-height: 100%;
    max-width: 100%;
    display: block;
    margin: 0 auto;

  }
  p{
    color: #3E3E3C;
    font-size: 14px;
    line-height: 14px;
    margin: 17px 0 0 0;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    display: inline-block;
    width: max-content;
    max-width: 100%;

    @include breakpoint(tablet) {
      font-size: 12px;
      width: max-content;

    }
    @include breakpoint(small) {
      font-size: 10px;
    }
  }
}

.top-category-img-wrapper{
  height: 74px;
  display: flex;
  align-items: center;

  @include breakpoint(desktop) {
    height: 60px;
  }
  @include breakpoint(small) {
    height: 50px;
  }
}

.top-category-active{
  p{
    font-weight: bold;
    position: relative;

    &:after{
      content: '';
      position: absolute;
      background: $blue;
      width: 100%;
      bottom: 0;
      left: 0;
      height: 5px;
      z-index: -1;
      opacity: 0.5;
    }
  }
}

.yith-similar-products{
  margin-top: 23px;

  @include breakpoint(laptop) {
    padding-bottom: 30px;
  }

  .swiper-wrapper{
    padding-bottom: 3px;
  }
}

.recently-viewed-section{
  padding: 0 0 100px;

  .swiper-pagination{
    display: none;
    bottom: 0;

    @include breakpoint(laptop) {
      display: block;
    }
  }
  .swiper-pagination-bullet{
    opacity: 0.1;
  }
  .swiper-pagination-bullet-active {
    background-color: #3E3E3C;
    opacity: 1;
  }
}

.shop-category-text{

  @include breakpoint(xl) {
  }
  h2, h1{
    font-size: 45px;
    line-height: 1.1;
    MARGIN: 0 0 18px;
    font-weight: normal;

    @include breakpoint(xl) {
      display: none;
    }
  }
  .shop-category-text-p{
    p{
      font-size: 18px;
      line-height: 1.4;
      max-width: 800px;
      margin: 0 0 18px;
    }

    @include breakpoint(xl) {
      display: none;
    }
  }
}

.cd-shop-category-img{
  display: flex;
  align-items: flex-start;
  padding-top: 10px;

  img{
    max-width: 250px;
    max-height: 180px;
    height: auto;
    width: auto;
    margin-right: 15px;

    @include breakpoint(xl) {
      display: none;
    }
  }
}

.shop-category-seo-text{
  h2, h3{
    color: black;
    margin: 30px 0 15px;
  }
}

.add-to-existing-order{
  color: $blue;
  display: block;
  margin-top: 10px;
  border: 1px #4A90E2 solid;
  text-decoration: none;
  padding: 5px;
}

.shop-categories{
  display: flex;
  flex-wrap: wrap;

  @include breakpoint(xl) {
    display: none;
  }
}

.shop-category{
  width: calc(25% - 16px);
  position: relative;
  margin: 8px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;

  img{
    height: auto;
    width: auto;
    max-width: 120px;
    max-height: 100px;
  }

  p{
    color: black;
    overflow: hidden;
    display: -webkit-box;
    max-height: 38px;
    -webkit-line-clamp: 2;
    height: 38px;
    width: 100%;
    text-align: center;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }
}

.cd-product-span{
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  flex-direction: column;

  .cd-price{
    margin-bottom: 6px;

    &:last-child{
      margin-bottom: 0;
    }
  }

  .woocommerce-Price-amount{
    margin-right: 5px;
  }

  del span {
    color: black;
  }

  ins{
    text-decoration: none;

    span{
      color: #FD5459;
    }
  } 

}

.cd-search-button{
  cursor: pointer;
}