.checkout-wrapper{
  padding: 50px 0 0;
}

.checkout-section{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 90px;
}

.checkout-left{
  width: 100%;
  margin-right: 40px;
  max-width: 700px;

  @include breakpoint(xl) {
    margin-right: 0;
  }

  h2{
    color: #3E3E3C;
    font-size: 28px;
    font-weight: 600;
    line-height: 34px;
    margin: 0;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 16px;
  }

  h3{
    color: #3E3E3C;
    font-size: 22px;
    line-height: 26px;
    font-weight: normal;
    margin: 0 0 21px 0;
  }
}

.confirm-section{
  @include breakpoint(medium) {
    flex-direction: column;
  }
}

.confirm-left{
  margin-right: 40px;

  @include breakpoint(medium) {
    margin: 0 0 40px 0;
  }
}

.checkout-sidebar{
  min-width: 353px;
  max-width: 353px;
  border: 1px solid #DDDDDD;
  background-color: #F2F1EF;
  padding: 25px 15px 30px;
  position: sticky;
  top: 20px;
  transition: 0.2s ease-in-out;

  @include breakpoint(xl) {
    position: fixed;
    top: auto;
    bottom: 55px;
    right: 30px;
    padding: 0 15px;
  }
  @include breakpoint(tablet) {
    width: calc(100% - 30px);
    max-width: 100%;
    left: 0;
    right: 0;
    bottom: 55px;
    box-shadow: 2px 2px 8px 0 rgba(0,0,0,.2);
  }
  @include breakpoint(mobile) {
    bottom: 50px;
  }
}

.checkout-height{
  @include breakpoint(tablet) {
    height: calc(100vh - 68px);
  }
}

.checkout-sidebar-show{

  @include breakpoint(xl) {
    display: none;
    padding-bottom: 20px;
  }
}

.checkout-sidebar-headline{
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include breakpoint(xl) {
    padding: 20px 0;
    cursor: pointer;
  }

  h4{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: bold;
    line-height: 17px;
    margin: 0;
  }
  a{
    color: #9C9C9C;
    font-size: 12px;
    line-height: 14px;
  }
}

.checkout-sidebar-toggle{
  display: none;
  opacity: 0.5;
  cursor: pointer;
}


.checkout-sidebar-products{
  margin-top: 20px;
  overflow-y: scroll;
  max-height: 350px;
  border-bottom: 1px solid #dddddd;

  @include breakpoint(xl) {
    margin-top: 0;
  }
}

.checkout-sidebar-product{
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 2px;
  box-shadow: inset 0 -1px 0 0 rgba(238,238,238,0.5);
  background: white;
  margin-bottom: 5px;

  img{
    max-height: 38px;
    height: auto;
    margin-right: 25px;
    width: auto;
    max-width: 38px;
  }
}

.checkout-sidebar-content{
  min-height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;

  p{
    margin: 0;
    color: #000;
    font-size: 12px;
    line-height: 14px;
  }
}

.checkout-sidebar-product-info{
  display: flex;
  align-items: center;
  justify-content: space-between;

  h6{
    margin: 0;
    color: #000;
    font-size: 14px;
    line-height: 17px;
  }
  span{
    color: #3E3E3C;
    font-size: 12px;
    line-height: 14px;
  }
}

.checkout-sidebar-info{
  margin-top: 28px;

  div{
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    &:last-child{
      margin-bottom: 0;
    }
  }
  p, strong{
    color: #3E3E3C;
    font-size: 15px;
    line-height: 18px;
    margin: 0;
  }
}


.checkout-inputs-half{
  margin-top: 40px;
  display: flex;
  justify-content: space-between;

  @include breakpoint(tablet) {
    flex-direction: column-reverse;
  }

  h6{
    margin: 80px 10px 0;
    color: #3E3E3C;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    text-transform: uppercase;

    @include breakpoint(tablet) {
      margin: 20px 0 40px;
    }
  }
}

.checkout-half-input{
  width: 100%;
  max-width: 270px;

  @include breakpoint(tablet) {
    max-width: 100%;
  }
}

.checkout-input{
  margin-bottom: 20px;

  label{
    color: #3E3E3C;
    font-size: 12px;
    font-weight: bold;
    line-height: 14px;
    text-transform: uppercase;
    display: block;
    margin: 0 0 10px 0;
  }
  input, textarea{
    height: 47px;
    border: 1px solid #9C9C9C;
    width: calc(100% - 42px);
    border-radius: 2px;
    background-color: #F9F9F9;
    padding: 0 20px;
    outline: none;
    transition: 0.2s ease-in-out;

    &:focus{
      box-shadow: inset 0 -1px 0 0 #3E3E3C;
    }
  }
  textarea{
    min-height: 117px;
    padding: 15px 20px;
  }
}

.call-before-input{
  height: auto !important;
  margin-right: 15px;
  width: max-content !important;
}

.checkout-checkbox{

  h4{
    color: #3E3E3C;
    font-size: 18px;
    font-weight: bold;
    line-height: 22px;
    margin: 55px 0 20px;
  }

  input{
    width: auto;
    height: auto;
    margin-right: 10px;

    &:focus{
      box-shadow: none;
    }
  }
  label{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    text-transform: none;
    cursor: pointer;
  }
}

.checkout-last-text{
  padding-top: 20px;
  span{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    padding-left: 20px;
  }
  a{
    color: $blue;
    font-size: 12px;
    line-height: 14px;
    text-decoration: none;
    text-transform: uppercase;
    margin-left: 10px;
  }
}

.checkout-continue{
  display: flex;
  justify-content: center;
  margin-top: 90px;

  a{
    height: 46px;
    width: 157px;
    border-radius: 5px;
    background-color: #4A90E2;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: bold;
  }
}

.big-icon-section{
  padding-top: 60px;
  padding-bottom: 70px;
  border-top: 1px solid #e6e6e6;

  h2{
    color: #3E3E3C;
    font-size: 28px;
    font-weight: 600;
    line-height: 34px;
    margin: 0 0 50px;
    text-align: center;

    @include breakpoint(small) {
      margin: 0 0 40px;
      text-align: left;
    }
  }
}

.big-icons{
  display: flex;
  justify-content: space-between;

  @include breakpoint(laptop) {
    flex-direction: column;
    align-items: center;
  }
  @include breakpoint(small) {
    align-items: flex-start;
  }
}

.big-icons-content{
  display: flex;
  align-items: center;
  margin: 0 20px 0 0;

  @include breakpoint(laptop) {
    margin: 0 0 20px 0;
  }


  &:last-child{
    margin: 0;
  }

  img{
    width: 48px;
    height: auto;
    margin-right: 20px;
  }

  span, a{
    max-width: 260px;
    display: inline-block;
    color: #3E3E3C;
    font-size: 14px;
    line-height: 17px;
  }
  a{
    font-weight: bold;
  }
}

.type-check{
  display: flex;
  padding: 35px 0;
  flex-wrap: wrap;
}

.type-check-content{
  border: 1px solid;
  border-radius: 5px;
  background-color: #FFFFFF;
  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
  margin: 10px 20px 10px 0;
  padding: 10px 15px;
  color: #000;
  font-size: 14px;
  font-weight: bold;
  line-height: 14px;
  cursor: pointer;
  text-transform: uppercase;

  @include breakpoint(small) {
    margin: 10px 15px 10px 0;
    padding: 8px 10px;
    font-size: 12px;
  }
}

.checkout {
  .type-check-content.cheque {
    order: 1;
  }
  .type-check-content.cod {
    order: 2;
  }
  .cash-text {
    order: 3;
    flex-basis: 100%;
  }
  .cheque-text {
    order: 4;
    flex-basis: 100%;
  }
}

.cd-payment-method.cod.type-check-active{
  cursor: default;
}

.type-check-active{
  background-color: $blue;
  box-shadow: none;
  border: 1px solid $blue;
  color: white;
}

.two-inputs{
  display: flex;

  @include breakpoint(mobile) {
    flex-direction: column;
  }

  .checkout-input{
    width: 100%;
    margin-right: 20px;

    &:last-child{
      margin-right: 0;
    }
  }
}

.same-as-billing{
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
  padding-bottom: 10px;
}

.same-billing-content{
  width: calc(25% - 20px);
  margin-right: 20px;
  margin-top: 25px;

  @include breakpoint(small) {
    width: calc(33.3% - 20px);
  }

  @include breakpoint(mobile) {
    width: calc(50% - 20px);
  }

  label{
    margin: 0 0 5px;
  }
  p{
    margin: 0;
    color: #444444;
  }
}

.checkout-continue-right{
  justify-content: flex-end;
  margin-top: 35px;
}

.checkout-header{
  background-color: #F5F5F5;
  padding: 18px 0;

}

.checkout-header-content{
  display: flex;
  justify-content: space-between;
  align-items: center;

  img{
    width: 157px;
    height: auto;

    @include breakpoint(laptop) {
      display: none;
    }
  }
  h5{
    color: $blue;
    font-size: 12px;
    font-style: italic;
    font-weight: bold;
    margin: 0;

    @include breakpoint(tablet) {
      display: none;
    }
  }
}

.checkout-header-stages{
  display: flex;
  align-items: center;

  @include breakpoint(mobile) {
    justify-content: space-between;
    width: 100%;
  }
}

.checkout-header-stage{
  display: flex;
  align-items: center;
  margin-right: 60px;
  cursor: pointer;

  @include breakpoint(mobile) {
    margin: 0;
  }

  &:last-child{
    margin-right: 0;
  }

  span{
    color: #9C9C9C;
    font-size: 13px;
    font-weight: 600;
    line-height: 16px;
    text-transform: uppercase;

    @include breakpoint(laptop) {
      font-size: 11px;
      line-height: 12px;
    }
  }
}

.checkout-header-number{
  height: 32px;
  width: 32px;
  background-color: #9C9C9C;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: bold;
  margin-right: 14px;

  @include breakpoint(mobile) {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }
}

.active-checkout-stage{
  span{
    color: #3E3E3C;
  }

  .checkout-header-number{
    background-color: #3E3E3C;
  }
}

.checkout-content{
  margin-top: 40px;
  margin-bottom: 0;
  padding-bottom: 40px;
  border-bottom: 1px solid #ebebeb;

  &:last-child{
    border-bottom: none;
  }

  .type-check{
    padding: 5px 0 0;
  }
}

.last-checkout-content{
  border-bottom: 0;
}

.checkout-pick-section{
  display: flex;
  justify-content: space-between;
  margin-top: 25px;

  @include breakpoint(tablet) {
    flex-direction: column;
  }
}

.checkout-pick{
  box-sizing: border-box;
  border: 1px solid #9C9C9C;
  border-radius: 5px;
  background-color: #FFFFFF;
  margin-right: 20px;
  width: 100%;
  padding: 20px 20px 25px;
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  @include breakpoint(tablet) {
    margin: 0 0 20px 0;
  }

  &:last-child{
    margin-right: 0;
  }

  h4{
    color: #3E3E3C;
    font-size: 16px;
    font-weight: bold;
    line-height: 19px;
    margin: 0;
  }
  p{
    color: #9C9C9C;
    font-size: 14px;
    font-weight: bold;
    line-height: 17px;
    margin: 7px 0 16px;
  }
  span{
  	color: #3E3E3C;
    font-size: 14px;
    font-weight: bold;
    line-height: 17px;
  }
  i{
    font-size: 31px;
    margin-right: 20px;
    color: #3E3E3C;
  }
}

.active-checkout-pick{
  border: 1px solid $blue;
  background-color: $blue;

  h4, span, p, i{
    color: white;
  }
}

.checkout-content-border{
  padding-bottom: 40px;
  border-bottom: 1px solid #ebebeb;
}

.checkout-type-check{
  margin-top: 30px;

  label{
    margin: 0;
  }
}

.margin-inputs-wrapper{
  margin: 30px 0 0;
}

.placeholder-inputs{
  .checkout-input{
    input{
      font-size: 12px;
    }
  }
}

.checkout-input-margin{
  margin-top: 25px;
}

.checkout-continue-last{
  margin-top: 5px;
}

.date-picker-wrapper{
  z-index: 50;
}

.date-picker-wrapper .footer {
  padding: 3px 0 0 0;
  background: transparent;
}

.date-picker-wrapper .first-date-selected {
  background-color: $blue !important;
}

.date-picker-wrapper .month-wrapper table .day.toMonth.hovering {
  background-color: #C7DEF8;
  opacity: 0.6;
}

.date-picker-wrapper .month-wrapper table .day.checked {
 background-color: #C7DEF8;
  color: white !important;
}

.date-picker-wrapper{
  font-family: $proxima;
  border: none;
  background-color: white;

  .month-wrapper{
    width: auto !important;
    border: none;

    table{
      width: 100%;
    }
  }

  th{
    color: #3E3E3C;
    font-size: 18px;
    font-weight: bold;
    line-height: 22px;
    text-transform: lowercase !important;

  }

  .week-name{
    th{
      font-size: 11px;
      line-height: 11px;
    }
  }
}
.date-picker-wrapper .month-wrapper table .day{
  width: 44px;
  margin-bottom: 0;
  padding: 16px 0;
}


.confirm-content{

  h3{
    font-size: 24px;
    font-weight: 600;
    line-height: 29px;
  }
  p{
    margin: 0 0 12px;
    color: #3E3E3C;
    font-size: 14px;
    line-height: 21px;
    max-width: 469px;
  }
  a{
    color: #4A90E2;
    font-size: 14px;
    line-height: 21px;
    text-decoration: none;
  }
}

.confirm-icon{
  height: 34px;
  width: 34px;
  background-color: #61D19D;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  color: white;
  margin: 30px 0 20px;
}

.confirm-sidebar{
  width: 100%;
  max-width: 490px;

  h4{
    margin: 0;
    line-height: 34px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 16px;
  }

  h5{
    margin: 30px 0 23px;
    color: #3E3E3C;
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
  }
}

.confirm-grid{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px dashed #EEEEEE;
  padding: 8px 0;

  p, strong{
    color: #3E3E3C;
    font-size: 16px;
    line-height: 19px;
    margin: 0;
  }
}

.confirm-grid-content{
  margin-top: 35px;

  strong{
    color: #3E3E3C;
    font-size: 16px;
    line-height: 19px;
    margin: 0 0 10px 0;
    display: block;
  }

  p, a{
    display: block;
    margin: 0;
    color: #3E3E3C;
    font-size: 14px;
    line-height: 21px;
  }
}

.error-input{
  border: 1px solid red !important;
}
 
.error-text{
  color: red;
}

.woocommerce-checkout-payment{
  background: white !important;
}

.type-of-payment{
  padding: 0;
}

.checkout-continue-last{
  background: #3c3c3c !important;
  text-transform: uppercase;
  padding: 15px 50px !important;
  margin-top: 20px !important;
  color: #fff;
}

.place-order{
  padding: 0 !important;
}

.checkout-content-first{
  border: none;
  padding-bottom: 0;
}

.checkout-content-explain{
  border-bottom: 1px solid #ebebeb;
  margin: 0;
  padding: 18px 0 40px;
  font-size: 14px;
}

.checkout-product-swiper-section{
  margin-top: 30px;
  position: relative;
  width: 50%;

  @include breakpoint(desktop) {
    width: 100%;
  }

  .swiper-button-next, .swiper-button-prev{
    top:58%;
    color: #9b9b9b;
    background: white;
    border-radius: 100px;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border: 0 solid rgba(0,0,0,0.01);

    @include breakpoint(tablet) {
      width: 20px;
      height: 20px;

      i{
        font-size: 17px;
      }
    }
  }
  .swiper-button-disabled{
    display: none;
  }
  .swiper-button-next{
    right: -15px;

    @include breakpoint(tablet) {
      right: -5px;
    }
  }
  .swiper-button-prev{
    left: -15px;

    @include breakpoint(tablet) {
      left: -5px;
    }
  }
}

.type-of-payment{
  display: none;
}

.date-picker-wrapper .date-range-length-tip{
  background: white;
  color: black;
  max-width: 200px;
  width: 100%;

  span{
    white-space: normal !important;
  }

  &:after{
    border-top: 4px solid #fff;
  }
}

.place-order-overlay{
  height: 100vh;
  width: 100%;
  position: fixed;
  background-color: rgba(255,255,255,.2);
  z-index: 1000;
  left: 0;
  top: 0;
}