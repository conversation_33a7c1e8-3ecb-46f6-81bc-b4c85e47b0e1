.account-section{
  background-color: #fff;
  padding: 60px 0;

  @include breakpoint(desktop) {
    padding: 60px 0;
  }
  @include breakpoint(tablet) {
    padding: 45px 0;
  }
  @include breakpoint(mobile) {
    padding: 30px 0;
  }

  h2{
    color: #3E3E3C;
    font-size: 28px;
    font-weight: normal;
    line-height: 34px;
    margin: 0;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 16px;
  }
}

.cd-cart-heading{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 20px;
}

.cart-add-to-existing-order{
  display: flex;
  align-items: center;

  select{
    margin: 0 10px;
  }
  p{

  }
  button{
    background-color: $blue;
    color: white;
    border: none;
    padding: 5px 12px;
    font-size: 15px;
    border-radius: 3px;
  }
}

.cart-section{
  h1{
    //margin: 0 0 20px;
  }
  h2{
    border-bottom: none;
  }
  .shop_table{
    box-sizing: border-box;
    background-color: #FFF;
    border: 1px solid #DDD;
    border-radius: 0;

    thead{
      tr{
        height: 50px;
      }
    }
  }

  .woocommerce-cart-form__contents{
    tbody{
      tr{
        &:nth-child(odd){
          background: #F9F9F9;

          @include breakpoint(medium) {
            background: white;
          }
        }
        &:last-child{
          background: white;
        }
      }
    }

    .product-name{
      a{
        color: #4A90E2;
        margin: 0;
        font-size: 14px;
        line-height: 14px;
        text-decoration: none;
      }
    }

    .product-thumbnail{
      img{
        max-width: 32px;
        max-height: 32px;
        width: auto;
        height: auto;
      }
    }
  }

  .cart_totals{
    width: calc(50% - 40px)!important;
    margin-top: 30px;
    max-width: 560px;
    margin-right: 40px;

    @include breakpoint(desktop) {
      width: 100% !important;
      margin-right: 0;
    }

    .checkout-button{
      background-color: $blue !important;
    }
  }

  .actions{
    padding: 20px 30px !important;

    @include breakpoint(small) {
      padding: 20px 15px !important;
    }
  }
  .button{
    background: $blue !important;
    color: white !important;
  }

  .coupon{
    display: flex;
    align-items: center;

    .input-text{
      width: 100% !important;
    }
    .button{
      background: transparent !important;
      color: $blue !important;
      outline: none;
      transition: 0.2s ease-in-out;

      &:hover{
        background: transparent;
        
      }
    }
  }
  .product-remove{
    a{
      color: #D66E6E !important;

      &:hover{
        background: #D66E6E !important;
      }
    }
  }
}

.cart-collaterals{
  display: flex;

  @include breakpoint(desktop) {
    flex-direction: column;
  }
}

.account-halfs{
  display: flex;
  justify-content: space-between;

  @include breakpoint(desktop) {
    flex-direction: column;
  }

  .woocommerce-message--info{
    width: 100%;
    box-sizing: border-box;
    background: transparent;
    margin-top: 25px;
    border-top: none;
  }

  .woocommerce-Button{
    background-color: #3c3c3c !important;
    color: white !important;
  }
}

.account-sidebar{
  width: 100%;
  max-width: 270px;
  padding-top: 25px;
  margin-right: 45px;

  @include breakpoint(desktop) {
    max-width: 100%;
    margin-right: 0;
    margin-bottom: 50px;
  }
}

.account-sidebar-items{
  padding-top: 10px;

  @include breakpoint(desktop) {
    display: flex;
  }
  @include breakpoint(mobile) {
    flex-direction: column;
  }

  ul{
    padding: 0;
    list-style: none;
    margin: 0;
  }

  a{
    color: #000;
    font-size: 16px;
    line-height: 19px;
    display: block;
    margin: 10px 0 0;
    text-decoration: none;

    @include breakpoint(desktop) {
      margin: 10px 20px 0 0;
    }
  }
  .current-menu-item{
    a{
      font-weight: bold;
      color: $blue;
    }
  }
}

.account-table-section{
  width: 100%;

  .edit-account{
    margin-top: 30px;

    fieldset{
      margin-top: 40px;
    }
    legend{
      margin: 0 0 20px;
      font-size: 20px;
      font-weight: 600;
    }
    .account-save-button{
      height: 46px;
      border-radius: 5px;
      background-color: #4A90E2;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      color: #FFF;
      font-size: 16px;
      font-weight: 700;
      margin-top: 30px;
      outline: none;
    }
  }
}

.cone-update-order{
  height: 46px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background-color: #4A90E2;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-decoration: none;
  color: #FFF;
  font-size: 16px;
  font-weight: 700;
  margin-top: 30px;
  outline: none;
  border: none;
  padding: 0 20px;
}

.account-table-headline{
  display: flex;
  align-items: center;
  justify-content: space-between;

  @include breakpoint(tablet) {
   flex-direction: column;
    align-items: flex-start;
  }

  h1{
    color: #3E3E3C;
    font-size: 42px;
    font-weight: normal;
    line-height: 46px;
    margin: 0;
  }
  a{
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    border-radius: 5px;
    background-color: #4A90E2;
    padding: 15px 30px;
  }
}

.table-search-content{
  display: flex;
  align-items: center;

  @include breakpoint(tablet) {
    margin-top: 15px;
    justify-content: space-between;
    width: 100%;
  }

  a{
    margin-left: 15px;
  }
}

.account-info{
  .checkout-input{
    input{
      background-color: #ffffff;
    }
    select{
      text-transform: none;
      height: 47px;
      border: 1px solid #9C9C9C;
      width: calc(100% - 42px);
      border-radius: 2px;
      background-color: #fff;
      padding: 0 20px;
      outline: 0;
      -webkit-transition: .2s ease-in-out;
      transition: .2s ease-in-out;
    }
  }
  input[disabled], select[disabled] {
    background-color: transparent;
  }

  .two-inputs{
    @include breakpoint(tablet) {
      flex-direction: column;
    }
  }
}

.account-table{
  box-sizing: border-box;
  background-color: #FFFFFF;
  margin-top: 30px;
}

.account-table-border{
  border: 1px solid #ddd;
}

.account-table-grid{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 40px;
  box-shadow: inset 0 -1px 0 0 rgba(238,238,238,0.5);
  min-height: 33px;
  position: relative;

  &:nth-child(even){
    background-color: #F9F9F9
  }

  @include breakpoint(medium) {
    padding: 10px 30px 10px 15px;
  }

  h5{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: bold;
    line-height: 17px;
    text-transform: uppercase;
    margin: 0;

    @include breakpoint(tablet) {
      font-size: 11px;
    }
  }
}

.account-table-header{
  .account-item-img{
    border: none;
    height: auto;
  }
}



.account-table-item{

  p{
    color: $blue;
    margin: 0;
    font-size: 14px;
    line-height: 14px;

    @include breakpoint(tablet) {
      font-size: 11px;
    }
  }
  span{
    color: #9C9C9C;
    font-size: 14px;
    line-height: 14px;

    @include breakpoint(tablet) {
      font-size: 11px;
    }
  }
  h6{
    color: #3E3E3C;
    font-size: 16px;
    line-height: 14px;
    font-weight: normal;
    margin: 0;

    @include breakpoint(tablet) {
      font-size: 12px;
    }
  }
}

.account-item-img{
  width: 48px;
  height: 48px;
  border: 2px solid #EEEEEE;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;

  @include breakpoint(tablet) {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border: none;
  }

  img{
    max-height: 35px;
    width: auto;
    height: auto;
    max-width: 35px;
  }
}

.account-item-product{
  width: 152px;

  a{
    color: $blue;
    margin: 0;
    font-size: 14px;
    line-height: 14px;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 30px;
    -webkit-line-clamp: 2;
    height: 30px;
    width: 100%;
    display: block;

    @include breakpoint(tablet) {
      font-size: 12px;
      max-height: 26px;
      height: 26px;
    }
  }
}

.account-item-price{
  width: 50px;
}

.account-item-number{
  width: 98px;

  input{
    box-sizing: border-box;
    height: 38px;
    width: 100%;
    border: 1px solid #EEEEEE;
    background-color: #FFFFFF;
    color: #3E3E3C;
    font-size: 14px;
    line-height: 17px;
    outline: none;
    text-align: center;
  }
}

.account-item-total{
  width: 70px;
}

.account-item-remove{
  width: 15px;

  @include breakpoint(tablet) {
    width: auto;
    position: absolute;
    right: 10px;
  }

  i{
    font-size: 15px;
    color: #D66E6E;
    cursor: pointer;
  }
}

.account-table-summation{
  padding: 40px;
  display: flex;
  justify-content: flex-end;

  @include breakpoint(tablet) {
    padding: 40px 15px;
  }

  .checkout-sidebar-info div{
    p, strong{
      &:first-child{
        margin-right: 110px;
        text-align: right;
        min-width: 170px;
      }
    }
  }
}

#hidden-add-product-container{
  position: relative;

  i{
    position: absolute;
    font-size: 17px;
    left: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 17px;
    color: $blue;
  }

  input[type=search]{
    height: 31px;
    width: 100vw;
    max-width: 233px;
    border: 1px solid #ddd;
    padding: 0 15px 0 47px;
    color: #666;
    background-color: white;
    font-size: 12px;
    outline: none;

    @include breakpoint(small) {
      width: 100%;
    }
  }
}

.cone-hidden{
  visibility: hidden;
}

.cone-display-none{
  display: none;
}

.hidden-time{
  display: none;
}