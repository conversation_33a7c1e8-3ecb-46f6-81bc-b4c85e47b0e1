.page-sidebar{
  padding-top: 0;
}

.page-headline-text{
  p{
    color: #3E3E3C;
    font-size: 18px;
    line-height: 26px;
    margin: 15px 0;
  }
  a{
    color: $blue;
    font-size: 18px;
    line-height: 26px;
  }
}


/*-----------------CONTACT PAGE STYLE--------------------*/


.map-img{
  width: 100%;
  height: auto;
}

.contact-halfs{
  display: flex;
  padding: 50px;

  @include breakpoint(medium) {
    padding: 25px;
  }
  @include breakpoint(small) {
    flex-direction: column;
  }
}

.contact-content{
  width: 100%;

  &:first-child{
    margin-right: 15px;

    @include breakpoint(small) {
      margin: 0 0 20px 0;
    }
  }

  h5{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    text-transform: uppercase;
    margin: 0 0 4px;
  }
}

.contact-right{
  padding-bottom: 20px;

  p, strong{
    color: #3E3E3C;
    font-size: 14px;
    line-height: 17px;
    margin: 0;
  }
  span{
    color: #ec828f;
  }
}

.contact-right-content{
  margin: 0 0 20px;
}

.contact-icon-content{

  p, a{
    color: #3E3E3C;
    font-size: 18px;
    line-height: 22px;
    margin: 0;
    text-decoration: none;
  }
}

.contact-icon{
  display: flex;
  align-items: center;
  margin-bottom: 40px;


  img{
    width: 37px;
    height: auto;
    margin-right: 30px;

    @include breakpoint(medium) {
      margin-right: 15px;
    }
  }
}

/*-----------------FAQ PAGE STYLE--------------------*/

.question-content{
  margin-top: 35px;

  h4{
    margin: 0 0 20px;
    color: #3E3E3C;
    font-size: 18px;
    line-height: 22px;
  }
}

.question-content-grid{
  border: 1px solid #eee;
}

.question-grid{
  background-color: rgba(73, 144, 226, 0.04);

  &:last-child{
    .question-headline{
      box-shadow: none;

    }
  }
}

.question-headline{
  box-shadow: inset 0 -1px 0 0 #eee;
  padding: 22px 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background 0.2s ease-in-out;

  @include breakpoint(medium) {
    padding: 20px;
  }
  @include breakpoint(mobile) {
    padding: 15px;
  }

  &:hover{
    background: rgba(0,0,0,0.03);
  }

  h5{
    color: #3E3E3C;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    margin: 0;
  }
  i{
    opacity: 0.3;
    transition: transform 0.2s ease-in-out;
  }
}

.answer{
  padding: 33px 40px;
  box-shadow: inset 4px 0 0 0 #4A90E2;
  display: none;

  @include breakpoint(medium) {
    padding: 25px 30px;
  }

  @include breakpoint(mobile) {
    padding: 20px;
  }

  p{
    color: #3E3E3C;
    font-size: 16px;
    line-height: 19px;
    margin: 0 0 15px 0;

    &:last-child{
      margin: 0;
    }
  }
  a{
    color: $blue;
    font-size: 16px;
    line-height: 19px;
  }
}

.rotate{
  transform: rotate(-180deg);
}

.order-table-grid{
  span{
    color: #000;
  }
}

.order-item-product{
  width: 80px;
}

.order-item-price{
  width: 120px;
}

.order-item-number{
  width: 70px;
}

.order-item-total{
  width: 150px;
}

.order-item-remove{
  width: 120px;
  text-align: right;

  @include breakpoint(tablet) {
    width: auto;
    position: absolute;
    right: 10px;
  }

  a{
    color: #353535;
    font-size: 12px;
    font-weight: 600;
    line-height: 12px;
    text-decoration: none;
    text-transform: uppercase;
    background-color: #D8D8D8;
    border-radius: 5px;
    padding: 6px 8px;
  }

  .table-item-desktop{

    @include breakpoint(tablet) {
      display: none;
    }
  }

  .table-item-mobile{
    display: none;
    padding: 2px 4px;

    @include breakpoint(tablet) {
      display: inline-block;
    }

    i{
      font-size: 16px;
    }
  }
}

.page-sidebar-content{
  position: sticky;
  top: 50px;
}

.secondary-pages{
  background: white;

  .account-halfs{
    justify-content: flex-start;
  }

  .account-table-section{
    max-width: 700px;
  }

  img{
    width: 100%;
    height: auto;
  }

  h1{

  }
  h3{
    margin: 30px 0 0;
    font-weight: normal;
    color: black;
  }

  p{
    line-height: 1.5;
  }

  a{
    color: $blue;
    text-decoration: none;
  }
}