

.home-heroes{
  height: calc(100vh - 127px);
  position: relative;
  max-height: 600px;

  @include breakpoint(laptop) {
    display: none;
  }
}

.divider-section{
  padding-top: 100px;
  padding-bottom: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;

  @include breakpoint(laptop) {
    display: none;
  }

  h3{
    max-width: 940px;
    color: #000;
    font-size: 27px;
    line-height: 43px;
    text-align: center;
    font-weight: 400;
    margin: 0 auto;
  }
}

.home-text-mobile{
  display: none;
  margin-bottom: 30px;

  @include breakpoint(laptop) {
    display: block;
  }

  p{
    color: #000;
    font-size: 18px;
    font-weight: 300;
    line-height: 22px;
    margin: 0;
  }
  a{
    color: $blue;
  }
}

.background-section{
  position: relative;
  padding: 140px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  @include breakpoint(laptop) {
    display: none;
  }
}

.background-section-content{
  position: relative;

  p{
    color: #FFFFFF;
    font-size: 16px;
    line-height: 19px;
    text-align: center;
    margin: 0;
  }

  h3{
    color: #FFFFFF;
    font-size: 42px;
    line-height: 50px;
    text-align: center;
    font-weight: normal;
    margin: 50px auto 60px;
    max-width: 800px;
  }
  a{
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    text-align: center;
    text-transform: uppercase;
    text-decoration: none;
    display: block;
    transition: 0.2s ease-in-out;

    &:hover{
      color: $blue;
    }
  }
}

.package-section {
  padding: 90px 0;
  background-color: #fff;

  .max-width{
    position: relative;
  }

  @include breakpoint(laptop) {
    padding: 20px 0 60px;
  }

  h3{
    color: #000;
    font-size: 24px;
    font-weight: 600;
    line-height: 29px;
    position: relative;
    margin-right: 60px;

    @include breakpoint(tablet) {
      font-size: 16px;
      line-height: 19px;
      font-weight: normal;
    }
  }

  .swiper-button-next, .swiper-button-prev{
    top:58%;
    color: #9b9b9b;
    background: white;
    border-radius: 100px;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border: 0 solid rgba(0,0,0,0.01);

    @include breakpoint(tablet) {
      width: 20px;
      height: 20px;

      i{
        font-size: 17px;
      }
    }
  }
  .swiper-button-disabled{
    display: none;
  }
  .swiper-button-next{
    right: 15px;

    @include breakpoint(tablet) {
      right: 5px;
    }
  }
  .swiper-button-prev{
    left: 15px;

    @include breakpoint(tablet) {
      left: 5px;
    }
  }
}

.packages-swiper{
  margin-top: 23px;


  @include breakpoint(laptop) {
    padding-bottom: 30px;
  }

  .swiper-wrapper{
    padding-bottom: 3px;
  }

  .swiper-slide{
  }
  .swiper-pagination{
    display: none;
    bottom: 0;

    @include breakpoint(laptop) {
      display: block;
    }
  }
  .swiper-pagination-bullet{
    opacity: 0.1;
  }
  .swiper-pagination-bullet-active {
    background-color: #3E3E3C;
    opacity: 1;
  }
}

//  h3{
//    color: #000;
//    font-size: 24px;
//    font-weight: bold;
//    line-height: 29px;
//    margin: 0 0 20px;
//  }
//  p{
//    color: #000;
//    font-size: 14px;
//    line-height: 17px;
//    margin: 0;
//    max-width: 355px;
//
//    @include breakpoint(desktop) {
//      max-width: 450px;
//    }
//  }
//}
//
//.package-grid{
//  display: flex;
//  align-items: center;
//  justify-content: space-between;
//  margin-top: 42px;
//
//  @include breakpoint(desktop) {
//    flex-direction: column;
//  }
//}
//
//.package{
//  width: 50%;
//  margin-right: 20px;
//  border-radius: 2px;
//  background-color: #FFFFFF;
//  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
//  display: flex;
//  height: 240px;
//
//  @include breakpoint(desktop) {
//    margin: 0 0 30px;
//    width: 100%;
//  }
//
//  &:last-child{
//    margin-right: 0;
//  }
//
//  h4{
//    color: #3E3E3C;
//    font-size: 22px;
//    font-weight: bold;
//    line-height: 32px;
//    margin: 0 0 16px 0;
//
//    @include breakpoint(xl) {
//      line-height: 23px;
//    }
//  }
//  a{
//    display: block;
//    color: $blue;
//    font-size: 14px;
//    line-height: 17px;
//    text-decoration: none;
//    margin: 28px 0 0 ;
//  }
//}
//
//.package-content{
//  padding: 30px;
//  display: flex;
//  flex-direction: column;
//  justify-content: center;
//  width: 100%;
//
//  @include breakpoint(xl) {
//    padding: 30px 20px;
//  }
//}
//
//.package-img{
//  width: 100%;
//  height: 100%;
//  max-width: 220px;
//}

.category-swiper{
  margin-bottom: 100px;
  margin-top: 23px;

  @include breakpoint(laptop) {
    padding-bottom: 30px;
  }

  @include breakpoint(small) {
    margin-bottom: 150px;
  }

  .swiper-wrapper{
    padding-bottom: 3px;
  }
  .swiper-pagination{
    display: none;
    bottom: 0;

    @include breakpoint(laptop) {
      display: block;
    }
  }
  .swiper-pagination-bullet{
    opacity: 0.1;
  }
  .swiper-pagination-bullet-active {
    background-color: #3E3E3C;
    opacity: 1;
  }
}

.home-cat-section{
  .swiper-button-next, .swiper-button-prev{

    @include breakpoint(laptop) {
      top: 70%;
    }
  }
}

.category-card{
  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
  background-color: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  
  &:hover{
    .category-card-name{
      p{
        color: $blue;
      }
    }
  }
}

.category-card-name{
  padding: 25px 15px 25px 20px;

  @include breakpoint(laptop) {
    padding: 12px 15px;
  }

  p{
    margin: 0;
    color: #000;
    transition: 0.2s ease-in-out;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    @include breakpoint(small) {
      font-size: 14px;
    }
  }
}

.category-card-img{
  height: 14vw;
  max-height: 185px;

  @include breakpoint(medium) {
    height: 18vw;
  }
  @include breakpoint(small) {
    height: 30vw;
  }
}


.menu-container {
  section {
    display: none !important;
  }
}

.ais-search-box--powered-by {
  display: none;
}