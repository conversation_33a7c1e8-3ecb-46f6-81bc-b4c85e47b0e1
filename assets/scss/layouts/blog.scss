.blog-header{
  position: absolute;
  left: 0;
  right: 0;
  top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  img{
    height: 22px;
  }

  h2{
    font-weight: 700;
    text-transform: uppercase;
    font-size: 19px;
    line-height: 26px;
    margin: 0;
    color: white;
  }

  p, a{
    font-size: 15px;
    line-height: 20px;
    color: WHITE;
    font-style: italic;
    max-width: 350px;
    margin: 0;
    font-weight: 400;
    OPACITY: 0.6;

    @include breakpoint(medium) {
      display: none;
    }
  }
  a{

  }
}

.blog-hero{
  background: $blue;
  padding: 78px 0;
  min-height: 180px;
  display: flex;
  align-items: center;

  h1{
    font-size: 44px;
    line-height: 46px;
    color: #fff;
    font-weight: 700;
    margin: 0 0 20px;

    @include breakpoint(medium) {
      font-size: 36px;
      line-height: 42px;
    }
  }
  h5{
    font-size: 14px;
    line-height: 28px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin: 0 0 6px;
    color: white;
  }
  p{
    font-size: 18px;
    line-height: 28px;
    color: #fff !important;
    margin: 0;
  }
  a{
    color: white !important;
  }
}

.blog-single-hero{
  padding-top: 22px;
  h1{
    font-size: 36px;
  }
}


.blog-hero-content{
  text-align: center;
  max-width: 700px;

  @include breakpoint(small) {
    text-align: left;
  }
}

.blog-grid{
  padding: 40px 0 45px;
  border-bottom: 1px solid #ededed;
}

.blog-grid-content{
  text-align: center;

  @include breakpoint(small) {
    text-align: left;
  }
  h5{
    font-size: 14px;
    line-height: 28px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin: 0 0 6px;
  }

  h3{
    font-size: 36px;
    line-height: 46px;
    color: #505873;
    font-weight: 700;
    margin: 0 0 17px;
    position: relative;
    transition: 0.2s ease-in-out;
    cursor: pointer;

    @include breakpoint(small) {
      font-size: 32px;
      line-height: 37px;
    }

    &:hover{
      color: darken(#505873, 20%);
    }
  }

}

.blog-grid-info{
  display: flex;
  align-items: center;
  justify-content: center;

  @include breakpoint(small) {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  p,a{
    font-size: 16px;
    line-height: 22px;
    color: #000;
    font-weight: 400;
    margin: 0 7px;
    text-decoration: none;
  }
  a{
    border-bottom: 1px solid #e7e7e7;
    transition: 0.2s ease-in-out;

    &:hover{
      border-bottom: 1px solid $blue;
    }
  }
}

.insight-section{
  padding: 60px 0;


}

.insight-content{
  text-align: left;
  max-width: 700px;

  a{
    transition: 0.2s ease-in-out;

    &:hover{
      color: darken($blue, 20%);
    }
  }

  iframe{
    width: 100%;
    height: 445px;
    padding: 15px 0;

    @include breakpoint(small) {
      height: 59vw;
    }
  }

  p{
    font-size: 18px;
    line-height: 28px;
    color: #333;
    font-weight: 400;
    margin: 0 0 15px;
  }

  .wp-caption-text{
    font-size: 15px;
    line-height: 21px;
    color: rgba(57,63,77,.7);
    font-weight: 400;
    padding: 11px 0 0 0;
    margin: 0;
  }

  .lead{
    font-size: 22px;
    line-height: 32px;
    font-weight: 400;
    margin: 0 0 28px;
  }

  a, u{
    color: $blue;
    text-decoration: none;
  }

  h1, h2, h3, h4{
    font-size: 32px;
    line-height: 40px;
    margin: 45px 0 15px;
    font-weight: 700;
  }

  li{
    margin: 0;
    font-size: 18px;
    line-height: 28px;
  }

  img{
    width: 100%;
    height: auto;
    margin: 19px 0 25px;
    display: block;
  }
  blockquote{
    font-size: 18px;
    line-height: 28px;
    color: #333;
    font-weight: 400;
    font-style: normal;
    padding: 0 0 0 28px;
    margin: 17px 0 24px;
    border-left: 1px dotted #d7d7d7;
    position: relative;
  }
}

.social-sidebar{
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @include breakpoint(medium) {
    top: auto;
    left:0;
    width: 100%;
    bottom: 0;
    flex-direction: row;
    height: auto;
  }
}
.testis{
  opacity: 1;
  transition: 0.2s ease-in-out;

  @include breakpoint(medium) {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}

.social-icon-box{
  height: 60px;
  width: 60px;
  background-color: #1164BB;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;

  @include breakpoint(medium) {
    height: 40px;
  }

  @include breakpoint(mobile) {
    width: 25%;
  }

  i{
    font-size: 20px;
    color: white;
  }
}

.wp-caption{
  margin-bottom: 19px;
  max-width: 100% !important;

  img{
    margin-bottom: 0;
  }
}

.subscribe-form-section{
  font-size: 18px;
  line-height: 26px;
  color: #fff;
  font-weight: 400;
  text-align: center;
  background: $blue;
  padding: 90px 0;
  position: relative;
  overflow: hidden;

  @include breakpoint(small) {
    text-align: left;
  }

  h6{
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -0;
    opacity: 0.08;
    font-size: 13.5vw;
    text-transform: uppercase;

    @include breakpoint(small) {
      display: none;
    }
  }

  h3{
    font-size: 32px;
    line-height: 40px;
    font-weight: 700;
    margin: 0 0 9px;
    color: white;
  }
  p{
    margin: 0 0 20px;
    padding: 0 30px;

    @include breakpoint(small) {
      padding: 0;
    }
  }
}

.subscribe-form{
  max-width: 500px;
  position: relative;

  input[type=submit]{
    min-width: 86px;
    background: #fbaa1a;
    border: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
  }
  .subscribe-email{
    width: 100%;
    height: 48px;
    color: $blue;
    padding: 5px 15px;
    background: #fff;
    border: 0;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }
}
.subscribe-inputs{
  display: flex;
}

.disqus-comment-section{
  padding-top: 60px;
}

.pagination-section{
  padding-top: 40px;
  padding-bottom: 80px;

  span, a{
    font-size: 18px;
    line-height: 28px;
    color: $blue;
    font-weight: 700;
    text-decoration: none;
    padding: 4px 16px;
    margin: 0 4px;
    background-color: #ecf1ff;
    border: 0;
    border-radius: 4px;
    vertical-align: top;
    display: inline-block;
    transition: 0.2s ease-in-out;

    &:hover{
      color: white;
      background-color: $blue;
    }
  }

  span{
    color: #ecf1ff;
    background: $blue;
  }
}