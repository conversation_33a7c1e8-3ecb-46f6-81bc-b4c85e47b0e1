.single-product-grid{
  display: flex;
  padding: 40px 0 50px;

  @include breakpoint(laptop) {
    flex-direction: column;
  }

  p{
    color: #000;
    font-size: 16px;
    line-height: 24px;
  }
}

.single-product-left{
  width: 100%;
  margin-right: 50px;
}

.single-product-right{
  width: 100%;
  max-width: 385px;

  @include breakpoint(laptop) {
   max-width: 100%;
  }

  h1{
    color: #3E3E3C;
    font-size: 36px;
    line-height: 46px;
    margin: 0 0 20px 0;
    font-weight: normal;

    @include breakpoint(mobile) {
      font-size: 27px;
      line-height: 36px;
    }
  }
  .woocommerce-product-details__short-description{
    p{
      margin: 0 0 15px;

      @include breakpoint(mobile) {
        font-size: 14px;
        line-height: 17px;
      }

      &:last-child{
        padding-bottom: 30px;
        border-bottom: 1px solid #979797;
        margin: 0;

        @include breakpoint(laptop) {
          padding-bottom: 0;
          border-bottom: none;
        }
      }
    }
  }
  .cart, .woocommerce-variation-add-to-cart{
    display: flex;
    width: 100%;
    margin-bottom: 7px;

    @include breakpoint(laptop) {
      max-width: 500px;
      padding-bottom: 38px;
    }
    @include breakpoint(mobile) {
      border-bottom: 1px solid #EEEEEE;
    }

    .qty{
      border: 1px solid #9D9D9D;
      background-color: #FFFFFF;
      height: 46px;
      padding: 0;
      text-align: center;
      margin-right: 15px;
      color: #3E3E3C;
      font-size: 18px;
      line-height: 18px;
      max-width: 58px;
      outline: none;
    }
    button{
      border-radius: 2px;
      background-color: #4759FF !important;
      border: 0;
      width: 100%;
      color: #FFFFFF;
      font-size: 16px;
      font-weight: bold;
      line-height: 16px;
      transition: 0.2s ease-in-out;

      &:hover{
        background: darken(#4759FF, 20%) !important;
      }
    }
    .add-to-existing-order{
      background-color: transparent !important;
      color: $blue;
      padding: 0;
      text-align: left;
    }
  }
  .variations_form{
    display: flex;
    flex-direction: column;
  }
}

.single-product-price{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 15px 0;
  flex-direction: column;

  @include breakpoint(laptop) {
    max-width: 500px;
    padding: 50px 0 25px;
  }

  .price{
    position: relative;
    margin: 0;
  }
  del{
    color: #D0021B;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    //position: absolute;
    left: 0;
    top: -12px;
    opacity: 1 !important;

    .woocommerce-Price-amount{
      color: #3E3E3C;
      font-size: 14px;
      font-weight: 600;
      line-height: 17px;
      //position: absolute;
      left: 0;
      top: -12px;
      text-decoration: line-through;
    }
  }
  ins {
    text-decoration: none;
    .woocommerce-Price-amount{
        color: #D0021B;
    }
  
  }
  .woocommerce-Price-amount{
    font-size: 25px;
    line-height: 2px;
    text-decoration: none;
    margin: 0 5px 0 0;
  }
}

.woocommerce-variation-price{
  display: flex;
  flex-direction: column;

  .cd-price{
    font-size: 16px;
  }

  .price{
    position: relative;
    margin: 0;
    display: flex;
    flex-direction: column;
  }
  del{
    color: #D0021B;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    //position: absolute;
    left: 0;
    top: -12px;
    opacity: 1 !important;

    .woocommerce-Price-amount{
      color: #3E3E3C;
      font-size: 14px;
      font-weight: 600;
      line-height: 17px;
      //position: absolute;
      left: 0;
      top: -12px;
      text-decoration: line-through;
    }
  }
  ins {
    text-decoration: none;
    .woocommerce-Price-amount{
        color: #D0021B;
    }
  
  }
  .woocommerce-Price-amount{
    font-size: 25px;
    line-height: 2px;
    text-decoration: none;
    margin: 0 5px 0 0;
  }
}

.single-product-vat{
  color: #000;
  font-size: 14px;
  font-weight: 600;
  line-height: 17px;
  text-align: right;
  margin-top: 10px;

  p{
    margin: 0;
    color: #3E3E3C;
    font-size: 25px;
    font-weight: normal;
  }
  .vat-span{
    font-size: 16px;
    color: black;
  }
}

.single-ex-price{
  display: flex;
  align-items: flex-end;

  .price{
    display: flex;
    flex-direction: column;

    .cd-price{
      margin-bottom: 10px;
      font-size: 16px;

      &:last-child{
        margin-bottom: 0;
      }

      ins{
        font-weight: 500 !important;
      }
    }
  }
}

.single-product-images{
  padding-bottom: 50px;
  border-bottom: 1px solid #979797;
  display: flex;
  flex-direction: row-reverse;

  @include breakpoint(laptop) {
    border-bottom: none;
    padding-bottom: 50px;
  }
}

.single-product-img{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  img{
    height: auto;
    width: auto;
    max-width: 85%;
    max-height: 469px;
  }
}

.single-product-gallery{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
}

.gallery-img-wrapper{
  height: 56px;
  width: 56px;
  opacity: .6;
  margin-bottom: 15px;
  cursor: pointer;
  overflow: hidden;
  border:1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;

  @include breakpoint(mobile) {
    width: 40px;
    height: 40px;
  }

  &:last-child{
    margin: 0;
  }
  img{
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }
}
.active-gallery{
  border: 1px solid $blue;
  opacity: 1;
}

.single-sidebar-products{
  margin-top: 23px;
  background-color: #F2F1EF;
  border-radius: 2px;
  padding: 20px 15px;

  @include breakpoint(laptop) {
    margin-top: 0;
    padding: 20px 30px;
  }
  @include breakpoint(tablet) {
    padding: 20px 15px;
  }

  h4{
    color: #3E3E3C;
    font-size: 12px;
    font-weight: bold;
    line-height: 14px;
    margin: 0 10px 0 0;
    text-transform: uppercase;
    width: 100%;
    min-width: fit-content;
  }

  .product-card-add{
    box-shadow: 1px 1px 4px 0 rgba(0,0,0,.1);
    background-color: #FFF;
    padding: 6px 20px 20px;

    a{
      text-align: center;
      width: auto;
      margin: 4px auto 0;
      display: table;
    }
  }

}

.sidebar-products-headline{
  display: flex;
  align-items: center;
  overflow: hidden;
}

.sidebar-products-border{
  height: 1px;
  width: 100%;
  background-color: #D2D2D1;
}

.sidebar-products-grid{
  border-radius: 2px;
  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
  background-color: #FFFFFF;
  padding: 20px;
  display: flex;
  align-items: center;
  margin-top: 15px;

  img{
    width: auto;
    height: auto;
    max-height: 70px;
    margin-right: 30px;
    max-width: 70px;
  }
}

.sidebar-products-content{
  width: 100%;

  a{
    text-decoration: none;
  }
  p{
    font-size: 14px;
    line-height: 17px;
    margin: 0;
    transition: 0.2s ease-in-out;

    &:hover{
      color: black;
    }
  }

}

.sidebar-products-info{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  p{
  }
  span{
    color: #000;
    font-size: 14px;
    line-height: 21px;
  }
  div{
    display: flex;
    flex-direction: column;
  }
  i{
    font-size: 13px;
    color: $blue;
    border: 1px solid $blue;
    width: 31px;
    height: 31px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    cursor: pointer;
    transition: 0.2s ease-in-out;

    &:hover{
      background: $blue;
      color: white;
    }
  }
}

.single-product-tabs{
  border-bottom: 1px solid #979797;

  @include breakpoint(laptop) {
    display: none;
  }

  h5{
    font-size: 16px;
    font-weight: bold;
    line-height: 19px;
    text-transform: uppercase;
    color: #3E3E3C;
    margin: 0;

    @include breakpoint(laptop) {
      font-weight: normal;
    }
  }
  a{
    color: $blue;
  }
  p{
    margin: 0 0 15px;
    font-size: 18px;
    line-height: 26px;

    @include breakpoint(mobile) {
      font-size: 14px;
      line-height: 17px;
    }

    &:last-child{
      margin: 0;
    }
  }
}

.grey-products{
  @include breakpoint(laptop) {
    padding: 40px 0 0 ;
    background-color: #F2F1EF;
    width: calc(100% + 60px);
    position: relative;
    left: -30px;
  }
  @include breakpoint(tablet) {
    width: calc(100% + 30px);
    left: -15px;
  }
}

.mobile-product-tabs{
  display: none;

  @include breakpoint(laptop) {
    display: block;

    .single-product-tabs{
      display: block;
      border-bottom: 1px solid #EEEEEE;
    }
  }
}

.product-tabs-info{
  padding-bottom: 25px;
}

.show-product-tab{
  .product-tabs-info{
    display: none;
  }

  .product-tabs-headline{
    transition: 0.2s ease-in-out;

    &:hover{
      background: rgba(0,0,0,0.05);
    }
  }
}

.product-tabs-headline{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  cursor: pointer;

  i{
    transition: 0.2s ease-in-out;
  }
}

.p-t-headline{
  display: flex;
  align-items: center;

  a{
    color: #3E3E3C;
    font-size: 12px;
    line-height: 14px;
    margin: 0 20px 0 0;
  }

  i{
    font-size: 20px;
    color: #3E3E3C;
  }
}

.variations{
  tr{
    display: flex;
    flex-direction: column;
  }
  td{
    margin-bottom: 15px;
  }
  select{
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-border-radius: 0;
    border-radius: 0;
    border: 1px solid #9D9D9D;
    background-color: #fff;
    padding: 12px 40px 12px 20px;
    color: #3E3E3C;
    font-size: 15px;
    line-height: 15px;
    background-image: url('../../images/down-arrow.png');
    background-size: 10px;
    background-repeat: no-repeat;
    background-position: right 12px center;
}
  .reset_variations{
    display: none;
  }
}

.single_variation{
  margin-bottom: 20px;
  color: #3E3E3C;
  font-size: 16px;
  line-height: 24px;

  ins{
    text-decoration: none;
  }
  del{
    font-weight: normal;
    color: #D0021B;
  }
}

.single-best-sellers{
  .best-seller-section{
    padding: 0 0 50px;

    @include breakpoint(laptop) {
      padding: 20px 0 45px;
    }
  }
}

.woocommerce div.product p.price, .woocommerce div.product span.price {
  color: #3E3E3C;
  font-size: 25px;
  font-weight: normal;
}


/*-----------------SEARCH STYLE--------------------*/

#ais-wrapper{
  padding-top: 45px;
  padding-bottom: 45px;
  max-width: 850px;
  margin: 0 auto;

  #algolia-search-box input{
    border-bottom: 1px solid #ccc;
  }

  a{
    text-decoration: none;
    color: $blue;
    transition: 0.2s ease-in-out;

    &:hover{
      color: darken($blue, 20%);
    }
  }
  p{
    line-height: 1.5;
  }
  .ais-Hits-item em, .ais-Hits-item a em{
    background: rgba(246, 166, 38, 0.5);
  }
  #algolia-search-box input{
    padding: 10px 0 10px 50px;
  }
  #algolia-search-box {
    margin-bottom: 60px;
  }
  .ais-Hits-item{
    article{
      display: flex;
      align-items: center;
    }
  }
  .ais-Pagination{
    display: flex;
    justify-content: center;
    padding: 0;

    a{
      font-size: 18px;
      line-height: 28px;
      color: $blue;
      font-weight: 700;
      text-decoration: none;
      padding: 4px 15px;
      margin: 0 0px;
      background-color: #ecf1ff;
      border: 0;
      border-radius: 4px;
      vertical-align: top;
      display: inline-block;
      transition: 0.2s ease-in-out;

      @include breakpoint(small) {
        padding: 0 11px;
        font-size: 14px;
      }
      @include breakpoint(mobile) {
        padding: 0 9px;
        line-height: 22px;
      }

      &:hover{
        color: white;
        background-color: $blue;
      }
    }
    .ais-Pagination-item--selected{
      a{
        color: white;
        background-color: $blue;
      }
    }
  }
  .ais-hits--thumbnail{
    width: 120px;
    min-width: 120px;

    @include breakpoint(mobile) {
      width: 60px;
      min-width: 60px;
      margin-right: 10px !important;
    }

    a{
      display: block;
      position: relative;
      width: 100%;
    }
    img{
      width: auto !important;
      height: auto;
      max-width: 100%;
      max-height: 100px;
      margin: 0 auto;
      display: block;
    }
  }
}

#ais-main{
  padding: 0;
}

#ais-facets{
  display: none;
}

/*-----------------BUNDLE STYLE--------------------*/

.product-type-yith_bundle{

  form{
    flex-direction: column;
  }

  table.yith-wcpb-product-bundled-items td.yith-wcpb-product-bundled-item-image {
    width: 60px;
    padding: 20px 10px !important;
  }
  td.yith-wcpb-product-bundled-item-image img {
    max-width: 60px;
    max-height: 60px;
    width: auto;
    height: auto;
    margin: 0 auto;
    display: block;
  }

  td.yith-wcpb-product-bundled-item-data h3 {
    margin: 0;
    line-height: 17px;

    a{
      color: #4A90E2;
      text-decoration: none;
      font-size: 14px;
    }
  }
  td.yith-wcpb-product-bundled-item-data p {
    margin: 8px 0 15px;
    font-size: 14px;
    line-height: 19px;
  }

  table.yith-wcpb-product-bundled-items td.yith-wcpb-product-bundled-item-data {
    padding: 20px 12px 30px 0 !important;
    position: relative;
  }
  .yith-wcpb-product-bundled-item-instock{
    font-weight: bold;
  }
  
  .yith-wcpb-product-bundled-item-outofstock{
    font-weight: bold;
    color: #D0021B;
    opacity: 0.5;
  }
}

.single-product-right .bundled_item_cart_content{
  display: none;
}