header{
  box-shadow: 0 0 1px 1px rgba(20,23,28,.1), 0 3px 1px 0 rgba(20,23,28,.1);
  height: auto;
  background: white;
  position: relative;

  @include breakpoint(laptop) {
    background: transparent;
  }
}

.upper-header{
  background-color: #262624;
  height: 32px;
  display: flex;
  align-items: center;

  span, a{
    color: #fff;
    font-size: 14px;
    line-height: 14px;
    margin-right: 30px;
    display: flex;
    align-items: center;
    text-decoration: none;
  }
  a{
    span{
      text-decoration: underline;
    }
  }

  i{
    font-size: 15px;
    margin-right: 10px;
  }

  ul{
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;

    a{
      text-decoration: none;
      margin: 0 0 0 30px;
    }
  }

  .max-width{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.daily-message{
  background: rgba(208, 2, 27, 0.8);
  padding: 5px 0;
  text-align: center;
  font-size: 16px;
  color: white;
  font-weight: 400;
}

.page-id-134, .page-id-137{
  header{
    display: none;

    @include breakpoint(laptop) {
      display: block;
    }
  }
}

.upper-header-icons{
  display: flex;
  align-items: center;
}

.lower-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 81px;

  @include breakpoint(laptop) {
    height: 60px;
  }
  @include breakpoint(tablet) {
    height: 50px;
  }

  h1{
    color: #000000;
    font-size: 19px;
    font-weight: bold;
    line-height: 26px;
    text-transform: uppercase;
    margin: 0;
  }

  ul{
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;

    @include breakpoint(laptop) {
      display: none;
    }

    li{
      position: relative;

      &:first-child{
        a{
          margin: 0;
        }
      }
    }

    a{
      text-decoration: none;
      margin-left: 40px;
      color: #535A65;
      font-size: 15px;
      line-height: 18px;

      @include breakpoint(xl-screen) {
        margin-left: 15px;
        font-size: 13px;
      }
    }

    .link-border{
      position: relative;

      &:after{
        content: '';
        position: absolute;
        width: 1px;
        height: 100%;
        background: #535A65;
        right: -6px;
        top: 0;
      }
    }
  }

  .sub-menu{
    position: absolute;
    border-radius: 2px;
    background-color: #FFFFFF;
    box-shadow: 1px 2px 4px 0 rgba(0,0,0,0.3);
    display: block;
    padding: 10px 15px;
    z-index: 100;
    right: 0;
    top: 36px;
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-in-out;

    a{
      margin: 10px 0 !important;
      display: block;
      width: max-content;
      transition: 0.2s ease-in-out;

      &:hover{
        color: $blue;
      }
    }
  }

  .open-login{
    a{
      margin-left: 12px;
    }
  }
}

.menu-item-has-children{
  padding: 10px 0;

  &:hover{
    .sub-menu{
      opacity: 1;
      visibility: visible;
    }
  }
}

.mobile-search{
  display: none;

  @include breakpoint(laptop) {
    display: block;
  }
}

.header-top{
  //box-shadow: 0 0 1px 1px rgba(20,23,28,0.1), 0 3px 1px 0 rgba(20,23,28,0.1);
  position: relative;
}

.header-left{
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.header-logo{
  margin-right: 35px;
  position: relative;

  @include breakpoint(xl-screen) {
    margin-right: 20px;
  }

  @include breakpoint(laptop) {
    margin-right: 0;
  }

  img{
    height: auto;
    width: 100%;
    max-width: 220px;

    @include breakpoint(xl-screen) {
      max-width: 150px;
    }

    @include breakpoint(laptop) {
      max-width: 130px;
    }
  }
}

.header-search{

  @include breakpoint(laptop) {
    display: none;
  }

  input{
    height: 45px;
    width: 100vw;
    max-width: 360px;
    border: none;
    padding: 0 20px;
    background-color: #F2F3F5;
    font-size: 13px;
    color: #535A65;
    outline: none;

    @include breakpoint(xl-screen) {
      max-width: 290px;
    }
  }
  form{
    position: relative;

    i{
      height: 45px;
      width: 45px;
      right: 0;
      position: absolute;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $blue;
      color: white;
    }
  }
}

.header-right{
  display: flex;
  align-items: center;

  a{
    color: #9B9B9B;
    font-size: 14px;
    line-height: 17px;
    text-decoration: none;
    margin-left: 40px;
    cursor: pointer;

    @include breakpoint(xl-screen) {
      margin-left: 15px;
    }

    @include breakpoint(laptop) {
      color: #3c3c3c;
    }
  }
}

.header-sides{
  @include breakpoint(laptop) {
    width: 90px;
  }
}

.mobile-hamburger{
  display: none;

  @include breakpoint(laptop) {
    display: flex;
  }

  a{
    margin-left: 0;
  }
}

.cart-customlocation{
  display: flex;
  position: relative;

  span{
    position: absolute;
    height: 15px;
    width: 15px;
    border-radius: 100px;
    background-color: $blue;
    color: #FFFFFF;
    font-size: 10px;
    line-height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: -8px;
    right: 0;

    @include breakpoint(laptop) {
      background-color: #F5A623;
    }
  }

  i{
    font-size: 26px;
    padding-right: 8px;
    color: black;
  }
}

.header-bottom{
  border-top: 1px solid #EEEEEE;
  background-color: #F7F8FA;
  box-shadow: inset 0 -1px 0 0 rgba(222,223,224,0.5);

  @include breakpoint(laptop) {
    display: none;
  }

  .max-width{
    display: flex;

    @include breakpoint(xl-screen) {
      overflow: scroll;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  a{
    text-decoration: none;
    position: relative;
    padding: 14px 10px;
    display: block;
    margin-left: 40px;
    color: $blue;
    font-size: 16px;
    line-height: 1;
    white-space: nowrap;
    font-weight: bold;
    transition: 0.2s ease-in-out;
    font-family: "Arial Narrow", Arial, sans-serif;

    &:hover{
      color: darken($blue, 20%);
    }

    &:first-child{
      margin: 0;
    }

    @include breakpoint(desktop) {
      font-size: 13px;
      margin-right: 25px;
    }
  }
  .header-sortiment-link{
    font-weight: bold;
    color: #3d3166;
    padding-left: 0;
    transition: 0.2s ease-in-out;

    &:hover{
      color: darken(#3d3166, 20%);
    }
  }
  .active-bottom-item{
    transition: 0.2s ease-in-out;
    &:after{
      content: '';
      position: absolute;
      width: 100%;
      height: 3px;
      background: $blue;
      left:0;
      bottom: 0px;
    }
  }
}

.show-sortiment-section{
  display: none;
  background: white;
  position: absolute;
  top: 124px;
  z-index: 5;
  width: 100%;
  min-height: calc(100vh - 190px);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px 0px;
}


/*------------------CART STYLING---------------------*/

.open-cart-section{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 100;
  display: flex;
  justify-content: flex-end;
  display: none;

  .overlay{
    background-color: rgba(255,255,255,0.5);
    display: none;
  }
}

.open-cart{
  border-radius: 2px;
  box-shadow: -2px 2px 4px 0 rgba(0,0,0,0.1);
  background-color: #F2F1EF;
  width: 385px;
  position: relative;
  padding: 30px 15px;
  transform: translate3d(100%,0,0);
  transition: 0.3s ease-in-out;

  @include breakpoint(tablet) {
    padding: 30px 15px 60px;
  }

  h4{
    color: #3E3E3C;
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    margin: 0;
    border-bottom: 1px solid #e6e6e6;
    padding-bottom: 17px;

    span{
      opacity: 0.4;
    }
  }
}

.cart-overlay{
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
}

.close-open-cart{
  position: absolute;
  top:15px;
  right: 15px;
  color: #3E3E3C;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;

  &:hover{
    opacity: 0.6;
  }
}

.open-cart-buttons{
  display: flex;
  flex-direction: column;
  position: absolute;
  width: calc(100% - 30px);
  left: 0;
  padding: 0 15px;
  bottom: 30px;

  @include breakpoint(tablet) {
    bottom: 60px;
  }
}

.open-cart-sum{
  color: #3E3E3C;
  font-size: 14px;
  font-weight: bold;
  line-height: 17px;
  text-align: right;
  margin: 0;
}

.open-cart-button{
  border-radius: 2px;
  background-color: $blue;
  text-align: center;
  padding: 19px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  margin: 10px 0;
}

.open-cart-link{
  color: $blue;
  font-size: 16px;
  font-weight: 600;
  line-height: 14px;
  text-align: center;
  border: 1px solid;
  padding: 18px 0;
}

.open-cart-grid{
  padding-top: 30px;
  max-height: calc(100% - 240px);
  overflow: scroll;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.open-cart-products{
  box-shadow: 1px 1px 4px 0 rgba(0,0,0,0.1);
  background-color: #FFFFFF;
  border-radius: 2px;
  padding: 20px 30px 20px 20px;
  display: flex;
  align-items: center;
  margin-top: 5px;
  position: relative;

  .remove_from_cart_button{
    font-size: 25px;
    color: #8C8C8C !important;
    text-decoration: none;
    position: absolute;
    top: 10px;
    right: 10px;
    transition: 0.2s ease-in-out;
    cursor: pointer;

    &:hover{
      color: black !important;
    }
  }

  img{
    width: auto;
    height: auto;
    max-height: 70px;
    max-width: 70px;
    margin-bottom: 8px;
  }
}
.o-cart-products-left{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 30px 0 0;
}

.cart-quantity{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;

  p{
    margin: 5px 0 0;
    font-size: 14px;
  }

  input{
    width: 33px !important;
    text-align: center;
    color: #3E3E3C;
    font-size: 14px;
    line-height: 17px;
    border: none;
    outline: none;
  }
  i{
    border: 1px solid #3E3E3C;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
  }
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
  }
}

.o-cart-products-right{
  width: 100%;
  p, a{
    color: #3E3E3C;
    font-size: 14px;
    line-height: 17px;
    margin: 0;
    text-decoration: none;
    transition: 0.2s ease-in-out;

    &:hover{
      color: #000;
    }
  }
}
.open-cart-info{
  text-align: right;
  margin-top: 35px;

  p{
    color: #3E3E3C;
    font-size: 18px;
    line-height: 22px;
  }
  span{
    color: #000;
    font-size: 15px;
    line-height: 14px;
  }
}

/*----------------MODAL STYLING------------------*/

.modal-section{
  position: fixed;
  top:0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(62,62,60,0.7);
  z-index: 100;
  overflow: scroll;
  display: none;
}

.modal{
  width: calc(100% - 120px);
  max-width: 470px;
  border-radius: 5px;
  background-color: #FFFFFF;
  box-shadow: 1px 1px 2px 0 rgba(0,0,0,0.2);
  margin: 100px auto;
  padding: 50px 60px;
  position: relative;

  @include breakpoint(small) {
    max-width: 100%;
    width: calc(100% - 40px);
    padding: 50px 20px;
    min-height: calc(100vh - 100px);
    margin: 0;  }

  h3{
    color: #3E3E3C;
    font-size: 42px;
    font-weight: bold;
    line-height: 46px;
  }
  p{
    color: #3E3E3C;
    font-size: 18px;
    line-height: 26px;
    margin: 8px 0 15px;
  }
  button{
    border-radius: 5px;
    background-color: $blue;
    width: 100%;
    padding: 15px;
    text-align: center;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: bold;
    border: none;
    margin: 20px 0 0;
  }
}

.already-customer{
  display: flex;
  align-items: center;

  span{
    color: #3E3E3C;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    margin-right: 10px;
  }
  a{
    color: $blue;
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    text-decoration: none;
    text-transform: uppercase;
  }
}

.forgot-password{
  color: #3E3E3C;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  display: block;
  margin: 15px 0 0;
}

.close-modal{
  position: absolute;
  right: 28px;
  top: 28px;
  cursor: pointer;

  @include breakpoint(small) {
    right: 20px;
    top: 45px;
  }
}



/*----------------MOBILE MENU STYLING------------------*/

.cd-dropdown{
  ul{
    list-style: none;
    margin: 0;
    padding-left: 0;
  }
  h2{
    margin: 0;
  }

}

.cd-search input[type="search"]{
  box-sizing: border-box;
}

.cd-dropdown-wrapper {
  display: none;
  position: relative;
  height: 0;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  padding: 0;
  z-index: 50;

  @include breakpoint(laptop) {
    display: block;
  }

  a{
    text-decoration: none;
  }
}

.cd-divider{
  font-size: 12px !important;
}

.cd-dropdown-content a, .cd-dropdown-content ul a {
  border: none;
  border-top: 1px solid #242643;
}

.cd-dropdown-trigger{
  background: transparent !important;
  padding: 0;
  display: none;

  @include breakpoint(laptop) {
    display: block;
  }

  &:before{
    display: none;
  }
  &:after{
    display: none;
  }
}

.mobile-shop-header{
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 15;
  display: none;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  background: #262624;
  height: 55px;

  @include breakpoint(laptop) {
    display: flex;
  }
  @include breakpoint(mobile) {
    height: 50px;
  }
}

.mobile-shop-header-left, .mobile-shop-header-right{
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  box-sizing: border-box;

  p, a{
    color: white;
    text-transform: uppercase;
    font-size: 18px;
    margin: 0;
    text-decoration: none;
    transition: 0.2s ease-in-out;

    @include breakpoint(mobile) {
      font-size: 16px;
    }
  }
  i{
    font-size: 20px;
    color: white;
    margin-right: 11px;

    @include breakpoint(mobile) {
      margin-right: 9px;
    }
  }
}

.mobile-shop-header-left{
  border-right: 1px solid #555;
  cursor: pointer;

  &:hover{
    p{
      color: $blue;
    }
  }
}


/*----------------FOOTER STYLING------------------*/

.contact-divider{
  display: flex;
  align-items: center;
  justify-content: center;

  @include breakpoint(mobile) {
    display: none;
  }

  p{
    color: #3E3E3C;
    font-size: 22px;
    font-weight: 600;
    line-height: 26px;
    margin: 45px 30px 45px 0;

    @include breakpoint(laptop) {
      margin: 25px 30px 25px 0;
      font-size: 20px;
    }
  }
  a{
    color: $blue;
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    text-decoration: none;
    text-transform: uppercase;
    padding-bottom: 4px;
    border-bottom: 2px solid;

    @include breakpoint(laptop) {
      font-size: 12px;
    }
  }
}

footer{
  background: white;
  position: relative;
  z-index: 40;
}

.footer{
  background-color: #262624;
  padding: 70px 0 80px;

}

.footer-content{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  @include breakpoint(tablet) {
    justify-content: flex-start;
  }

  @include breakpoint(mobile) {
    flex-direction: column;
  }

  h3{
    color: #FFFFFF;
    font-size: 19px;
    line-height: 23px;
  }

  h4{
    color: white;
    margin: 0 0 10px;
    text-transform: uppercase;
  }

  p{
    color: white;
    margin: 11px 0 20px;
    font-size: 16px;
    line-height: 19px;
    display: flex;
    align-items: center;

    a{
      color: white;
      margin-left: 5px;
    }
  }

  a{
    color: $blue;
    font-size: 16px;
    line-height: 27px;
    display: block;
    text-decoration: none;

    &:hover{
      text-decoration: underline;
    }
  }
  .cd-footer-p{
    @include breakpoint(mobile) {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}

.footer-column{
  @include breakpoint(laptop) {
    margin: 0 30px 30px 0;

    &:last-child{
      margin: 0;
    }
  }
}

