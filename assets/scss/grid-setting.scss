/*--------------------------

BREAKPOINTS mobile first

----------------------------*/


@mixin breakpoint($point) {

  @if $point == xl-screen {
    @media (max-width: 1200px) { @content ; }
  }
  @else if $point == xl {
    @media (max-width: 1100px) { @content ; }
  }
  @else if $point == desktop {
    @media (max-width: 1000px) { @content ; }
  }
  @else if $point == laptop {
    @media (max-width: 900px) { @content ; }
  }
  @else if $point == medium {
    @media (max-width: 800px) { @content ; }
  }
  @else if $point == tablet {
    @media (max-width: 700px) { @content ; }
  }
  @else if $point == small {
    @media (max-width: 600px) { @content ; }
  }
  @else if $point == mobile {
    @media (max-width: 500px) { @content ; }
  }
  @else if $point == mobileonly {
    @media (min-width: 500px)  { @content ; }

  }
}

