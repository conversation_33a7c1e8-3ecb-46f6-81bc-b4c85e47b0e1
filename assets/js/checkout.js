jQuery(document).ready( function($) {

    var ReturnDP;

    // Fix payment method text visibility: prioritize selected payment method.
    // Fall back to customer type when no method is selected yet.
    function fixPaymentMethodDisplay() {
        var selectedMethod = $('input[name="payment_method"]:checked').val();

        // Reset both texts first
        $('.cash-text').hide();
        $('.cheque-text').hide();

        if (selectedMethod === 'cod') {
            $('.cash-text').show();
            return;
        }
        if (selectedMethod === 'cheque') {
            $('.cheque-text').show();
            return;
        }

        // Fallback: Check which customer type is selected (privat, company, catering)
        var selectedCustomerType = $('.type-of-service .type-check-content.type-check-active').data('name');
        if (selectedCustomerType === 'privat') {
            $('.cash-text').show();
        } else if (selectedCustomerType === 'company' || selectedCustomerType === 'catering') {
            $('.cheque-text').show();
        }
    }

    // WooCommerce lifecycle events that can re-render payment methods
    $(document.body).on('updated_checkout', fixPaymentMethodDisplay);
    $(document.body).on('init_checkout', fixPaymentMethodDisplay);
    $(document).on('change', 'input[name="payment_method"]', fixPaymentMethodDisplay);


    // Run on page load
    fixPaymentMethodDisplay();

    // Also run when payment section becomes visible
    $(document).on('DOMNodeInserted', '#payment', function() {
        setTimeout(fixPaymentMethodDisplay, 200);
    });

    // Run when payment section is shown
    $(document).on('show', '#payment', function() {
        fixPaymentMethodDisplay();
    });

    // Run when any element with payment class becomes visible
    $(document).on('DOMSubtreeModified', function() {
        if ($('#payment').is(':visible')) {
            fixPaymentMethodDisplay();
        }
    });

    // Also check periodically for the first few seconds after page load
    var checkCount = 0;
    var paymentChecker = setInterval(function() {
        if ($('#payment').length > 0) {
            fixPaymentMethodDisplay();
            checkCount++;
            if (checkCount > 5) {
                clearInterval(paymentChecker);
            }
        }
    }, 500);

    //Init dateRangePicker
    if ( $('#pick-date').length != 0 ) {
        var unavailableDates = ($('#pick-date').data('value')) ? $('#pick-date').data('value') : [];//.replace(/\s/g, '').split(',');
        var DRP = $('#pick-date').dateRangePicker({
            singleMonth: true,
            autoClose: true,
            startOfWeek: 'monday',
            singleDate: true,
            language: 'se',
            beforeShowDay: unavailable,
        });
    }

    //On date change
    if ( DRP ) {
        DRP.bind('datepicker-change', function(event,obj){
            var date = setDateValue('#cone_billing_order_date', 1);
            initReturn(date);
        });
    }

    function initReturn(date){
        ReturnDP = $('#return-date').dateRangePicker({
            applyBtnClass: "return",
            singleMonth: true,
            autoClose: true,
            startOfWeek: 'monday',
            singleDate: true,
            language: 'se',
            startDate: date,
            beforeShowDay: unavailable,
        });

        ReturnDP.data('dateRangePicker').open();

        ReturnDP.bind('datepicker-change', function(event,obj){
            $('.checkout-receiving').attr('style', '');
            setDateValue('#cone_billing_return_date', 2);
        });
    }


    function setDateValue(element, type){
        date = ( type == 1 ) ? DRP[0].value : ReturnDP[0].value;
        $(element).val(date);
        return date;
    }

    function unavailable(date) {
        //Add 0 to month and day so it matches date string because JS cant have 0:s i dates
        var month = (date.getMonth().toString().length == 1 && date.getMonth() != 9) ? '0' + (date.getMonth()+1) : (date.getMonth()+1);
        var day = (date.getDate().toString().length == 1) ? '0' + date.getDate() : date.getDate();
        //Create datestring in same format as array dates.
        dmy = day + "-" + month + "-" + date.getFullYear();
        var m = date.getFullYear() + '-' + month + '-' + day;

        var d1 = (DRP && this.applyBtnClass == 'return') ? Date.parse(DRP[0].value) : 0;
        var d2 = (DRP && this.applyBtnClass == 'return') ? Date.parse(m): 1;

        var objType = (this.applyBtnClass == 'return') ? 'Returdatum' : 'Hyrdatum';
        var limit = $('.checkout-header-stages').data('days');//2;
        var tooltip = (m < getToday(limit)[0] ) ? 'Passerat datum.' : '';

        //If current date isnt in blocked dates array && date isnt passed
        if ( (!( dmy in unavailableDates ) && d1 <= d2) && m >= getToday(limit)[1] ) {
            //Dont block
            return [true,"",""];
        } else {
            tooltip = (unavailableDates[dmy]) ? unavailableDates[dmy][objType] : tooltip;
            //Else block
            return [false,"",tooltip];
        }
    }


    //Open order sum
    $('.checkout-sidebar-headline').on('click', function() {
        $('.checkout-sidebar-show').slideToggle('fast');
        $('.checkout-sidebar-toggle').fadeToggle(100);
    });

    //Submit new user form
    // $('.checkout-continue').on('click', function(){
    //     event.preventDefault();
    //     $('#register-user-form').submit();
    // });


    //Choose delivery type and show associated time-section
    $('.checkout-pickup-return .checkout-pick').on('click', function(){

    	//If is delivery or return
    	var section = ( $(this).parent().parent().hasClass('checkout-return') ) ? 'return' : 'delivery';

        //Set hidden fields values
        if ( section == 'delivery' ) {
            $('#cone_billing_get_products').val( $(this).data('type') );
        }else {
            $('#cone_billing_return_products').val( $(this).data('type') );
        }



    	//Set active class to cliked item and remove it from other
    	$(this).siblings().removeClass('active-checkout-pick');
    	$(this).addClass('active-checkout-pick');

    	//Get clicked type ('aladdin' or 'customer')
    	var type = $(this).data('type');

    	//Figure out what the other_type is
    	var other_type = ($(this).data('type') == 'aladdin') ? 'customer' : 'aladdin';

    	//Remove active-class from time to reset choosen time
    	$('.'+section+'-time .type-check-content').removeClass('type-check-active');

    	//Show clicked items time section and hide the other
    	$('.'+section+'-time-'+other_type).hide();
    	$('.'+section+'-time-'+type).attr('style', '');

    	//Init var to see if aladdin is delivering
    	var isAladdin = false;

    	//Check if aladdin is to deliver or pickup
    	$('.active-checkout-pick').each(function(){
    		if ( $(this).data('type') == 'aladdin' ) {
    			isAladdin = true;
    		}
    	});

        toggleRequired(isAladdin);

    	//If aladdin is delivering, show special fields
    	(isAladdin) ? $('.aladdin-delivery-special').attr('style', '') : $('.aladdin-delivery-special').hide();
    });

    //Choose between 'Privatperson', 'Företag' and 'Catering'
    $('.type-of-service .type-check-content').on('click', function(){
        //Set value in hidden input
        $('#billing_customer_type').val( $(this).find('span').html() );
    	//Remove active class from siblings
    	$(this).siblings().removeClass('type-check-active');
    	//Add active class tio clicked button
    	$(this).addClass('type-check-active');
    	//Get visibility status from form
    	var billing_form_status =  document.getElementById('billing-form-info').style.display;
    	//If form is hidden
    	if ( billing_form_status == 'none' ) {
    		//Show form
    		$('#billing-form-info').removeAttr('style');
    	}
    	//If 'Privatperson' was clicked
    	if ( $(this).data('name') == 'privat' ) {
    		//Hide 'Företag'-specific inputs
    		$('.company-inputs').hide();
    		//And make them not required
    		$('.company-inputs input').removeClass('cone-required');
    		//Also empty their values
    		$('.company-inputs input').val('');
            //Hide Boxaddress on shipping form
            $('#shipping_address_2').parent().hide();
            //Hide all payment types bur COD
            $('#payment .type-check-content').each(function(){
                if ( ! $(this).hasClass('cod') ) {
                    $(this).hide();
                    $(this).removeClass('type-check-active');
                }
            });
            $('.cd-payment-method input').prop('checked',false);
            //Add active class to COD option
            $('.cod').addClass('type-check-active');
            //And check hidden radio
            $('#payment_method_cod').prop('checked', true);
            //Change Other delivery text
            $('.other-delivery-address span').text('Annan än bostadsadress');
            //Show delivery text info
            $('.delivery-private-text').show();
 		// If other button was clicked
    	}else{
            //Show all payment methods
            $('#payment .type-check-content').show();
    		//Show 'Företag'-specific inputs
    		$('.company-inputs').removeAttr('style');
            //Show Boxaddress on shipping form
            $('#shipping_address_2').parent().show();
    		//And make them required
            //$('#billing_org_nr').addClass('cone-required');
            $('#billing_company').addClass('cone-required');
            //Change Other delivery text
            $('.other-delivery-address span').text('Annan än fakturaadress');
            //Show delivery text info
            $('.delivery-private-text').hide();
    	}

    	// Update payment method text based on customer type selection
    	fixPaymentMethodDisplay();
    });


    //Coose pickup time and show return
    $('.checkout-time-section .type-check-content').on('click', function(){
    	$(this).siblings().removeClass('type-check-active');
        $(this).siblings('.extra-charge-text').hide();
    	$(this).addClass('type-check-active');

        if ( $(this).hasClass('extra-charge') ) $(this).siblings('.extra-charge-text').show();

    	if ( $(this).parent().hasClass('delivery-time') ){
    		$('.checkout-return').attr('style', '');
            //Set hidden fields value
            $('#cone_billing_get_products_time').val( $(this).find('span').html() );
    	}else{
            //Show shipping form
    		$('.checkout-delivery').attr('style', '');
            //Show payment
            $('#payment').attr('style', '');
            //Set hidden fields value
            $('#cone_billing_return_products_time').val( $(this).find('span').html() );
    	}
    });


    //Coose pickup time and show return
    $('.aladdin-delivery-special .type-check-content').on('click', function(){
    	$(this).siblings().removeClass('type-check-active');
    	$(this).addClass('type-check-active');

        //Set hidden field value
        $('#special_venue_type').val( $(this).find('span').html() );

    	if ( $(this).find('span').html() == 'Annat' ){
            $('#special_venue_other').addClass('cone-required');
    		$('.other-facility').attr('style', '');
    	}else{
    		$('.other-facility').hide();
            $('#special_venue_other').removeClass('cone-required');
    	}

    });


    //Set Venue type to users typed text
    $('#special_venue_other').on('change', function(){
        $('#special_venue_type').val('Annat - ' + $(this).val() );
    });

    //Change form to submit on change of inputs
    $('.checkout-login-input').on('change', function(){
        var id = $(this).parent().parent().attr('id');
        var form = ( id == 'register-user-form' ) ? 'checkout-login-form' : 'register-user-form' ;
        $('#'+form+' input').each(function(i){
            this.value = '';
        });
        //console.log(id);
        $('.checkout-register-login').attr('data-form', id);
    });

    $('#accept-terms').on('click', function(){
        ( $('#accept-terms').prop('checked') == true ) ? $('#accept-terms').val(1) : $('#accept-terms').val(0);
    });

    $('#accept-terms-2').on('click', function(){
        ( $('#accept-terms-2').prop('checked') == true ) ? $('#accept-terms-2').val(1) : $('#accept-terms-2').val(0);
    });


    $('.checkout-register-login').on('click', function(event){
        event.preventDefault();
        form_id = $(this).attr('data-form');

        if ( form_id != 'register-user-form' || (!coneRequired('#'+form_id) && $('#accept-terms').prop('checked') == true) ) $('#'+form_id).submit();
        if ( form_id == 'register-user-form' && $('#accept-terms').prop('checked') == false ) alert('Du måste acceptera villkoren för att fortsätta.');
    });

    $('#header-register-form button').on('click', function(event){
        event.preventDefault();
        if ( !coneRequired('#header-register-form') && $('#accept-terms-2').prop('checked') == true ) $('#header-register-form').submit();
        if ( $('#accept-terms-2').prop('checked') == false ) alert('Du måste acceptera villkoren för att fortsätta.');
    });



    //Toggle if shipping info should be same as billing
    $('.different-from-billing .type-check-content').on('click', function(){
    	//Toggle button active class
    	$(this).toggleClass('type-check-active');

        //toggle hidden input value

        //toggle visibility of form.

    	//If active class was removed from button
    	if ( $(this).hasClass('type-check-active') ) {
            //Set ship_to_different_address to 1
            $(this).find('input').val(1);
            //Hide shipping form
            $('#shipping-form').show();
            //Empty shipping form
            $('#shipping-form input').each(function(i){
                this.value = '';
            });
    	}else{
            //Set ship_to_different_address to 0
            $(this).find('input').val(0);
            //Hide shipping form
            $('#shipping-form').hide();
            //Populate shipping form
            copyBillingToShipping();
    	}
    });

    //Toggle payment methods
    $(document).on('click', '.cd-payment-method', function(){
        //Remove active class
        $('.cd-payment-method').removeClass('type-check-active');
        //Add active class to clicked
        $(this).addClass('type-check-active');
        //Remove prop checked
        $('.cd-payment-method input').prop('checked', false);
        //Add prop checked to clicked
        $(this).find('input').prop('checked', true);

        if ( $(this).hasClass('cod') ){
            // Uncheck invoice email and hide field when switching to COD
            $('#invoice_email').prop('checked', false).val(0);
            $('.input-invoice-email-address').hide();
            $('#invoice_email_address').removeClass('cone-required');
        }

        // Update payment method text based on customer type selection, not payment method
        fixPaymentMethodDisplay();
    });

    //Show email input if checkbox is checked
    $('#invoice_email').on('click', function(){
        if ( $(this).prop('checked') == true ) {
            $('.input-invoice-email-address').show();
            $('#invoice_email_address').addClass('cone-required');
        }else{
            $('.input-invoice-email-address').hide();
            $('#invoice_email_address').removeClass('cone-required');
        }
    });

    //Go to second step
    $('.checkout-continue-first a').on('click', function(event){
    	event.preventDefault();
    	//Get all inputs in form
    	var billingForm = $('#step1').serializeArray();

    	//Init html that is going to be copied to shipping form
    	var html = '';

    	//Loop through billing info
    	for (var input in billingForm) {
    		//If input has value
    		if ( billingForm[input].value != '' ){
    			//Create HTML for that value
    			html += '<div class="same-billing-content">';
    			html += '<label for="'+billingForm[input].name+'">' + $('input[name="'+billingForm[input].name+'"]').siblings().html() + '</label>';
    			html += '<p id="'+billingForm[input].name+'">'+ billingForm[input].value +'</p>';
    			html += '</div>';
    		}
    	}

        copyBillingToShipping();

   		//Check if there are required fields that are empty otherwise go to second step.
    	if ( !coneRequired('#billing-form-info') ) changeSteps('1', '2');

    });

    //Toggle call before checkboxes
    $('.call-before-input').on('click', function(){
        if ( this.id === 'call-before-return-input' ) {
            var input = document.getElementById('cone_billing_call_before_return');
            $(this).prop('checked') == true ? input.value = 'Ja' : input.value = '';
        }else{
            var input = document.getElementById('cone_billing_call_before_delivery');
            $(this).prop('checked') == true ? input.value = 'Ja' : input.value = '';
        }
    });

    //Change steps in checkout-header
    $('.checkout-header-stages .checkout-header-stage').on('click', function(){

        //If on thankyou page abort
        if ( $('.active-checkout-stage').hasClass('checkout-step-3') ) return false;

    	var billing_form_style =  document.getElementById('billing-form-info').style.display;

    	if ( billing_form_style == 'none' ) {
    		alert('Innan du kan gå vidare måste du välja Privatperson, Företag eller Catering och sedan fylla i formuläret.');
    		return false;
    	}

    	//Get currently active step
    	var active_step = $('.active-checkout-stage').find('.checkout-header-number').html();
    	//Get clicked step
    	var clicked_step = $(this).find('.checkout-header-number').html();

    	//If clicked step already is active, abort.
    	if ( active_step == clicked_step ) return false;

    	//Call changeSteps func
    	changeSteps(active_step, clicked_step);

    });

    // $('#place_order').on('click', function(){
    //     $('#place_order').prop('disabled', true);
    //     $("form.woocommerce-checkout").submit();
    // });


    //Create Order
    $("form.woocommerce-checkout").on('submit', function(e) {
        // e.preventDefault();
        // console.log($(this).serializeArray());
        // return false;
        $('#terms').parent().css('border', '');
        $('body').prepend('<div class="place-order-overlay"></div>');
        //Terms and conditions must be approved
        if ( ! $('#terms')[0].checked ){
            e.preventDefault();
            alert('Du måste godkänna villkoren');
            $('#terms').parent().css('border', '1px solid red');
            $(document).find('.place-order-overlay').remove();
            //$('#place_order').prop('disabled', false);

        }
        //Set is-offer flag
        var isOffer = ( $('#cone-is-offer')[0].checked ) ? 'true' : 'false';
        $('#cone_is_offer').val(isOffer);
        //If required fields are empty, prevent formsubmition.
        if ( coneRequired('body') ) {e.preventDefault(); $(document).find('.place-order-overlay').remove();}//$('#place_order').prop('disabled', false);}
    });

    function toggleRequired(isAladdin){
        //console.log( $('#aladdin-special-form').find('.cone-required') );
        if (isAladdin) {
            $('#aladdin-special-form').find('.cone-not-required').addClass('cone-required');
            $('#aladdin-special-form').find('.cone-not-required').removeClass('cone-not-required');
        }else{
            $('#aladdin-special-form').find('.cone-required').addClass('cone-not-required');
            $('#aladdin-special-form').find('.cone-required').removeClass('cone-required');
        }

    }


    //Check if there are empty required fields
    function coneRequired(section)
    {
    	//Remove previous errors
    	$('.cone-required').removeClass('error-input');
    	$('.type-of-facility-block').parent().removeClass('error-input');
        $('.error-text').remove();

    	//Init error variable
    	var error = false;

    	//Loop through required fields
    	$(section+' .cone-required').each(function(){
            //console.log(this.name +' - '+this.value );
    		//If value is empty
    		if ( this.value == '' ) {
                //Specail modification so special venue type error is shown as it isnt an input
                if ( this.name == 'special_venue_type' ){
                    $('.type-of-facility-block').parent().addClass('error-input');
                    $(this).parent().after('<p class="error-text">Fältet är obligatoriskt.</p>');
                }
    			//Set error var to true
    			error = true;
    			//Add error class to required input
    			this.className += ' error-input';
    			//Add text under the input
    			$(this).after('<p class="error-text">Fältet är obligatoriskt.</p>');
    		}
    	});

    	//If there is an error alert user
    	if ( error ) alert('Du måste fylla i alla obligatoriska fält innan du kan gå vidare.');

    	return error;
    }

    //Changes checkout steps
    function changeSteps(active_step, clicked_step){

    	//If clicked step is 2
    	if ( clicked_step == '2' ) {
    		//Check if there are required fields that are empty
    		if ( !coneRequired('#billing-form-info') ) {
    			//if not change to second step
    			$('#step'+active_step).hide();
    			$('#step'+clicked_step).attr('style', '');

                if ( $('.checkout-delivery').is(':visible') ) $('#payment').show();

    			//Remove active-class from currently active step
    			$('.active-checkout-stage').removeClass('active-checkout-stage');

    			//Add active-class to clicked step
    			$('.checkout-step-'+clicked_step).addClass('active-checkout-stage');

                $("html, body").animate({ scrollTop: "0" });

                copyBillingToShipping();
    		}
    	}
    	//If clicked step is 1
    	else if ( clicked_step == '1' ) {
    		//Change to 1 step
    		$('#step'+active_step).hide();
            $('#payment').hide();
    		$('#step'+clicked_step).attr('style', '');

    		//Remove active-class from currently active step
    		$('.active-checkout-stage').removeClass('active-checkout-stage');

    		//Add active-class to clicked step
    		$('.checkout-step-'+clicked_step).addClass('active-checkout-stage');
    	}
    }

    function copyBillingToShipping(){
        $('#shipping_first_name').val( $('#billing_first_name').val() );
        $('#shipping_last_name').val( $('#billing_last_name').val() );
        $('#shipping_address_1').val( $('#billing_address_1').val() );
        $('#shipping_address_2').val( $('#billing_address_2').val() );
        $('#shipping_postcode').val( $('#billing_postcode').val() );
        $('#shipping_city').val( $('#billing_city').val() );
    }

    function getToday(days){
        var limit;
        var today = new Date();
        var dd = today.getDate();
        d = dd + days;
        var mm = today.getMonth()+1; //January is 0!

        var yyyy = today.getFullYear();
        if(dd<10){
            dd='0'+dd;
        }
        if(d<10){
            d='0'+d;
        }
        if(mm<10){
            mm='0'+mm;
        }
        today = yyyy+'-'+mm+'-'+dd;
        limit = yyyy+'-'+mm+'-'+d;
        return [today, limit];
    }


});
