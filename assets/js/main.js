jQuery(document).ready( function($) {

    //All swipers
    var bestSellers = new Swiper('.best-seller-swiper', {
        slidesPerView: 4,
        slidesPerGroup: 4,
        nextButton: '.nav-right',
        prevButton: '.nav-left',
        spaceBetween: 15,
        pagination: '.swiper-pagination',
        paginationClickable: true,
        breakpoints: {
            // when window width is <= 320px
            1000: {
                slidesPerView: 3,
                slidesPerGroup: 3
            },
            800:{
                slidesPerView: 2,
                slidesPerGroup: 2
            }
        }
    });

    var checkoutProducts = new Swiper('.checkout-product-swiper', {
        slidesPerView: 2,
        slidesPerGroup: 2,
        nextButton: '.nav-right',
        prevButton: '.nav-left',
        spaceBetween: 15,
        pagination: '.swiper-pagination',
        paginationClickable: true,
        breakpoints: {
            // when window width is <= 320px
            1000: {
                slidesPerView: 3,
                slidesPerGroup: 3
            },
            800:{
                slidesPerView: 2,
                slidesPerGroup: 2
            }
        }
    });

    var recentlyViewed = new Swiper('.yith-similar-products', {
        slidesPerView: 4,
        slidesPerGroup: 4,
        nextButton: '.navy-right',
        prevButton: '.navy-left',
        spaceBetween: 15,
        pagination: '.swiper-pagination',
        paginationClickable: true,
        breakpoints: {
            // when window width is <= 320px
            1000: {
                slidesPerView: 3,
                slidesPerGroup: 3
            },
            800:{
                slidesPerView: 2,
                slidesPerGroup: 2
            }
        }
    });

    var packages = new Swiper('.packages-swiper', {
        slidesPerView: 4,
        slidesPerGroup: 4,
        nextButton: '.nav-pack-right',
        prevButton: '.nav-pack-left',
        spaceBetween: 15,
        pagination: '.swiper-pagination',
        paginationClickable: true,
        breakpoints: {
            // when window width is <= 320px
            1000: {
                slidesPerView: 3,
                slidesPerGroup: 3
            },
            800:{
                slidesPerView: 2,
                slidesPerGroup: 2
            }
        }
    });

    var catSwiper = new Swiper('.category-swiper', {
        slidesPerView: 4,
        slidesPerGroup: 4,
        nextButton: '.nav-right-cat',
        prevButton: '.nav-left-cat',
        spaceBetween: 15,
        pagination: '.swiper-pagination',
        paginationClickable: true,

        breakpoints: {
            // when window width is <= 320px
            800: {
                slidesPerView: 3,
                slidesPerGroup: 3
            },
            600:{
                slidesPerView: 2,
                slidesPerGroup: 2
            }
        }
    });

    var topCatSwiper = new Swiper('.shop-cat-swiper', {
        slidesPerView: 5,
        slidesPerGroup: 5,
        spaceBetween: 60,
        nextButton: '.top-cat-next',
        prevButton: '.top-cat-prev',
        breakpoints: {
            // when window width is <= 320px
            900: {
                slidesPerView: 4,
                slidesPerGroup: 4,
                spaceBetween: 40
            },
            700: {
                slidesPerView: 4,
                slidesPerGroup: 4,
                spaceBetween: 20
            },
            500: {
                slidesPerView: 3,
                slidesPerGroup: 3,
                spaceBetween: 10
            }
        }
    });


    /*------------------------------------------------------------------
    *  MENU
    *------------------------------------------------------------------*/

    //var menuTimer;
    //var aborted = false;
    //
    //$(document).on('mouseenter', '.category-item', function(){
    //    var item = $(this);
    //    item.siblings('.category-item').find('a').removeClass('category-item-hover');
    //    item.find('a').addClass('category-item-hover');
    //    var cat_id = $(this).data('id');
    //    var under_section = $('.'+cat_id+'-cat-item');
    //    under_section.siblings('.under-cat-section').hide();
    //    under_section.attr('style', 'display: flex;');
    //});
    //
    //
    //
    //
    //
    ////Show categories in header
    //$(document).on('click', '.show-sortiment', function(e){
    //    e.preventDefault();
    //    var item = $(this);
    //    if( !item.hasClass('active-bottom-item') ){
    //
    //        var clone = item.siblings('.category-section').clone()[0];
    //        $('.show-sortiment-section').html(clone);
    //        $('.show-sortiment').removeClass('active-bottom-item');
    //        item.addClass('active-bottom-item');
    //
    //        $('.show-sortiment-section').fadeIn('fast');
    //    }else{
    //        $('.show-sortiment').removeClass('active-bottom-item');
    //        $('.show-sortiment-section').fadeOut('fast');//fadeOut(100);
    //    }
    //});
    //
    ////Fade out on click outside
    //$(document).mouseup(function(e){
    //    var container = $(".show-sortiment-section, .show-sortiment");
    //    // if the target of the click isn't the container nor a descendant of the container
    //    if (!container.is(e.target) && container.has(e.target).length === 0){
    //        $(".show-sortiment-section").fadeOut('fast');
    //        $('.show-sortiment').removeClass('active-bottom-item');
    //    }
    //});


    /*------------------------------------------------------------------
    *  END MENU
    ------------------------------------------------------------------*/
    

    /*------------------------------------------------------------------
    *  CART
    ------------------------------------------------------------------*/
    
    //Open cart
    $(document).on('click', '.cart-customlocation',function(e){
        e.preventDefault();
        console.log('clicked menu');
        $('.open-cart-section').css('display', 'flex');
        $('.open-cart-section .overlay').fadeIn('fast');
        $('.open-cart')
            .delay(10)
            .queue(function (next) {
                $(this).css('transform', 'translate3d(0%,0,0)');
                next();
            });
    });
    //Close cart
    $(document).on('click', '.close-open-cart, .open-cart-section .overlay',function(){
        $('.open-cart').css('transform', 'translate3d(100%,0,0)');
        $('.open-cart-section .overlay').fadeOut('fast');
        $('.open-cart-section')
            .delay(300)
            .queue(function (next) {
                $(this).css('display', 'none');
                next();
            });
    });

    //Open cart on product add.
    $(document).on('click', '.product-card-add a',function(){
        $('.cart-customlocation').trigger('click');
    });


    /*------------------------------------------------------------------
    *  END CART
    ------------------------------------------------------------------*/

    //Open Faq answers
    $('.question-headline').on('click', function() {
        $(this).siblings('.answer').slideToggle('fast');
        $(this).children('.material-icons').toggleClass('rotate');
    });

    //Open Product tabs
    $('.show-product-tab').on('click', function() {
        $(this).children('.product-tabs-info').slideToggle('fast');
        $(this).find('.material-icons').toggleClass('rotate');
    });

    //Open modals
    $('.open-register').on('click', function(e) {
        e.preventDefault();
        $('.register-modal').fadeIn('fast');
        $("html, body").css({
            height: '100%',
            overflow: 'hidden',
            position: 'relative'
        });
    });

    $('.open-login').on('click', function(e) {
        e.preventDefault();
        $('.login-modal').fadeIn('fast');
        $("html, body").css({
            height: '100%',
            overflow: 'hidden',
            position: 'relative'
        });
    });

    //Close modal
    $('.close-modal').on('click', function() {
        $('.modal-section').fadeOut('fast');
        $("html, body").css({
            height: 'auto',
            overflow: 'visible'
        });
    });

    //Toggle between login and register modals
    $('.already-customer a').on('click', function(){
        //Get open modal type
        var type = $(this).data('type') == 'register' ? '.open-login' : '.open-register';
        //Close modal
        $('.close-modal').trigger('click');
        //Open other modal
        $(type).trigger('click');
    });

    //Open order sum
    //$(window).resize(function() {
    //    if ($(window).width() < 1100) {
    //        $('.checkout-sidebar-headline').on('click', function() {
    //            $('.checkout-sidebar-show').slideToggle('fast');
    //            $('.checkout-sidebar-toggle').fadeToggle(100);
    //            $('.checkout-sidebar').toggleClass('checkout-height');
    //        });
    //    }
    //});
    if ( $(window).width() < 1100 ) {
        $('.checkout-sidebar-headline').on('click', function() {
            $('.checkout-sidebar').toggleClass('checkout-height');
            ( $('.checkout-sidebar').hasClass('checkout-height') ) ? $('.checkout-sidebar-show').show() : $('.checkout-sidebar-show').hide();
            //$('.checkout-sidebar-toggle').fadeToggle(100);
        });
    }

    $('#place_order').on('click', function(){
        //event.preventDefault();
        //console.log( $('.checkout-left form').serializeArray() );
    });

    $(document).on('click', '.add_to_cart_button', function(){
        toggleLoading('open');
    });

    $(document).ajaxComplete(function() {
      toggleLoading('close');
    });

    $('.shop-first-i').on('click', function(e){
        e.preventDefault();
        $(this).parent().parent().toggleClass('shop-top-cats-active');
        $(this).parent().siblings('.shop-second-cats').removeClass('shop-second-cat-active');
        $(this).parent().siblings('.shop-second-cats').find('.shop-third-cat-active').removeClass('shop-third-cat-active');
        console.log($(this).parent().siblings('.shop-second-cats').find('.shop-third-cat-active').removeClass('.shop-third-cat-active'));
    });
    $('.shop-second-i').on('click', function(e){
        e.preventDefault();
        $(this).parent().parent().toggleClass('shop-second-cat-active');
        $(this).parent().siblings('.shop-third-cat-active').removeClass('shop-third-cat-active');
    });

    $('.shop-third-i').on('click', function(e){
        e.preventDefault();
        $(this).parent().parent().toggleClass('shop-third-cat-active');
    });

    $('.mobile-shop-menu-trigger').on('click', function(){
        $('.cone-sortiment').trigger('click');
    });

    $('.mobile-search').on('click', function(e){
        e.preventDefault();
        //$('.cd-dropdown-trigger').trigger('click');
        var navIsVisible = ( !$('.cd-dropdown').hasClass('dropdown-is-active') ) ? true : false;
        $('.cd-dropdown').toggleClass('dropdown-is-active', navIsVisible);
        $('.cd-dropdown-trigger').toggleClass('dropdown-is-active', navIsVisible);
        if( !navIsVisible ) {
            $('.cd-dropdown').one('webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend',function(){
                $('.has-children ul').addClass('is-hidden');
                $('.move-out').removeClass('move-out');
                $('.is-active').removeClass('is-active');
            }); 
        }

        $('.mobile-s-field')[0].focus();
    });

});

//Toggle loading overlay
function toggleLoading(type){
    if( type == 'open'){
        //console.log('ladda');
        (jQuery)('.cart-overlay').removeAttr('style');
}
    else {
        (jQuery)('.cart-overlay').css('display', 'none');
        //console.log('sluta ladda');
    }
}