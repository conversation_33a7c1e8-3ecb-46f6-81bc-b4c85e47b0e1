jQuery(document).ready( function($) {

	//Change img on single product
	$('.gallery-img-wrapper').on('click', function(){
		var src = $(this).find('img').data('full');
		console.log(src);
		//Change active class
		$('.gallery-img-wrapper').removeClass('active-gallery');
		$(this).addClass('active-gallery');

		$('.single-product-img img').attr('src', src);
		$('.single-product-img img').attr('srcset', src);
	});

	//Ajax add to cart on single product
	// $('.single_add_to_cart_button').on('click', function(e) {
	    
	//     if ( $(this).hasClass('disabled') ) return false;
	//     //Prevent from sending form
	//     e.preventDefault();
	// 	toggleLoading('open');

	//     //Get product id
	//     var product_id = $(this).val();
 //        //Get quantity
 //        var quantity = $('input[name="quantity"]').val();

 //        console.log(product_id + ' - ' + quantity);

 //        var variation_id = ( $('.variation_id').val() ) ? '&variation_id=' + $('.variation_id').val() : '' ;

 //        console.log(variation_id);

 //        var count = parseInt($('.cart-header').data('count'));
        
 //        //Make ajax call 
 //        $.ajax ({
	// 		url: ajaxApi.ajaxurl,
	// 		type:'POST',
	// 		data:'action=cone_wc_add_cart&product_id=' + product_id + '&quantity=' + quantity + variation_id,

	// 		success:function(results) {

	// 			//console.log(results);

	// 			//Update header count
				
	// 			//Remove eariler
	// 			$('.open-cart-grid').remove();
	// 			// Add new cart HTML
	// 		    $('.open-cart').append(results);
	// 		    //Calculate cart total items
	// 		    var b_add = parseInt($('.cart-customlocation span').html()) + parseInt(quantity);
	// 		    //Update cart symbol with calculated items
	// 		    $('.cart-customlocation span').html(b_add);
	// 		    //Update header count
	// 		    $('.cart-header').html('(' + b_add +')');
	// 		    //Open cart modal
	// 		    $('.cart-customlocation').trigger('click');

	// 			toggleLoading('close');
	// 		}
	// 	});
	// });

	//Add related product to cart
	$('.add-rel-product').on('click', function(){
		if ( $(this).hasClass('cd-variable-product') ) {
			window.location.href = $(this).data('link');
			return false;
		}
		$(this).parent().parent().parent().next('.product-card-add').attr('style', ' ');
	});

	//Show prompt if product has prompt
	$('.cd-prompt').on('click', function(){
		alert( $(this).data('text') );
	});


	//If add to existsing order is possible
	if ( $('.add-to-existing-order').length > 0 ) {
		$('input.qty').on('change', function(){
			$('.add-to-existing-order').each(function(){
				var s = $(this).attr('href');
				var n = s.indexOf('&');
				s = s.substring(0, n != -1 ? n : s.length);
				$(this).attr('href', s+'&addQty='+$('input.qty').val());
			});
			//document.write(s);
		});
	}

	// Ajax add to cart on the product page
	var $warp_fragment_refresh = {
	    url: wc_cart_fragments_params.wc_ajax_url.toString().replace( '%%endpoint%%', 'get_refreshed_fragments' ),
	    type: 'POST',
	    success: function( data ) {
	        if ( data && data.fragments ) {

	            $.each( data.fragments, function( key, value ) {
	                $( key ).replaceWith( value );
	            });

	            $( document.body ).trigger( 'wc_fragments_refreshed' );
	        }
	    }
	};

	$('.single-product-grid form.cart').on('submit', function (e)
	{
	    e.preventDefault();

	    // $('.entry-summary').block({
	    //     message: null,
	    //     overlayCSS: {
	    //         cursor: 'none'
	    //     }
	    // });

	    toggleLoading('open');

	    $('.cart-customlocation').trigger('click');

	    var product_url = window.location,
	        form = $(this);

	    $.post(product_url, form.serialize() + '&_wp_http_referer=' + product_url, function (result)
	    {
	    	//console.log(result);
	        var cart_dropdown = $('.open-cart-grid', result)

	        console.log(cart_dropdown);

	        // update dropdown cart
	        $('.open-cart-grid').replaceWith(cart_dropdown);

	        // update fragments
	        $.ajax($warp_fragment_refresh);

	        //$('.entry-summary').unblock();

	    });
	});


});