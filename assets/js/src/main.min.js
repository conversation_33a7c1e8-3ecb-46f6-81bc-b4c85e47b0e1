function bulkAddToOrder(e){var t,i={};jQuery(".woocommerce-cart-form__cart-item").each(function(){var e=jQuery(this).find(".product-remove a").data("product_id"),t=jQuery(this).find(".product-quantity input").val();i[e]=t}),t=encodeURIComponent(JSON.stringify(i)),window.location.href=e+"?json="+t}function toggleLoading(e){"open"==e?jQuery(".cart-overlay").removeAttr("style"):jQuery(".cart-overlay").css("display","none")}jQuery(document).ready(function(r){var n;function e(){var e=r('input[name="payment_method"]:checked').val();r(".cash-text").hide(),r(".cheque-text").hide(),"cod"===e?r(".cash-text").show():"cheque"===e?r(".cheque-text").show():"privat"===(e=r(".type-of-service .type-check-content.type-check-active").data("name"))?r(".cash-text").show():"company"!==e&&"catering"!==e||r(".cheque-text").show()}r(document.body).on("updated_checkout",e),r(document.body).on("init_checkout",e),r(document).on("change",'input[name="payment_method"]',e),e(),r(document).on("DOMNodeInserted","#payment",function(){setTimeout(e,200)}),r(document).on("show","#payment",function(){e()}),r(document).on("DOMSubtreeModified",function(){r("#payment").is(":visible")&&e()});var c,s,t=0,i=setInterval(function(){0<r("#payment").length&&(e(),5<++t)&&clearInterval(i)},500);function a(e,t){return date=(1==t?s:n)[0].value,r(e).val(date),date}function o(e){var t=1==e.getMonth().toString().length&&9!=e.getMonth()?"0"+(e.getMonth()+1):e.getMonth()+1,i=1==e.getDate().toString().length?"0"+e.getDate():e.getDate(),e=(dmy=i+"-"+t+"-"+e.getFullYear(),e.getFullYear()+"-"+t+"-"+i),t=s&&"return"==this.applyBtnClass?Date.parse(s[0].value):0,i=s&&"return"==this.applyBtnClass?Date.parse(e):1,n="return"==this.applyBtnClass?"Returdatum":"Hyrdatum",a=r(".checkout-header-stages").data("days"),o=e<h(a)[0]?"Passerat datum.":"";return!(dmy in c)&&t<=i&&e>=h(a)[1]?[!0,"",""]:[!1,"",c[dmy]?c[dmy][n]:o]}function l(e){r(".cone-required").removeClass("error-input"),r(".type-of-facility-block").parent().removeClass("error-input"),r(".error-text").remove();var t=!1;return r(e+" .cone-required").each(function(){""==this.value&&("special_venue_type"==this.name&&(r(".type-of-facility-block").parent().addClass("error-input"),r(this).parent().after('<p class="error-text">Fältet är obligatoriskt.</p>')),t=!0,this.className+=" error-input",r(this).after('<p class="error-text">Fältet är obligatoriskt.</p>'))}),t&&alert("Du måste fylla i alla obligatoriska fält innan du kan gå vidare."),t}function u(e,t){"2"==t?l("#billing-form-info")||(r("#step"+e).hide(),r("#step"+t).attr("style",""),r(".checkout-delivery").is(":visible")&&r("#payment").show(),r(".active-checkout-stage").removeClass("active-checkout-stage"),r(".checkout-step-"+t).addClass("active-checkout-stage"),r("html, body").animate({scrollTop:"0"}),p()):"1"==t&&(r("#step"+e).hide(),r("#payment").hide(),r("#step"+t).attr("style",""),r(".active-checkout-stage").removeClass("active-checkout-stage"),r(".checkout-step-"+t).addClass("active-checkout-stage"))}function p(){r("#shipping_first_name").val(r("#billing_first_name").val()),r("#shipping_last_name").val(r("#billing_last_name").val()),r("#shipping_address_1").val(r("#billing_address_1").val()),r("#shipping_address_2").val(r("#billing_address_2").val()),r("#shipping_postcode").val(r("#billing_postcode").val()),r("#shipping_city").val(r("#billing_city").val())}function h(e){var t=new Date,i=t.getDate(),e=(d=i+e,t.getMonth()+1),n=t.getFullYear();return[t=n+"-"+(e=e<10?"0"+e:e)+"-"+(i=i<10?"0"+i:i),n+"-"+e+"-"+(d=d<10?"0"+d:d)]}0!=r("#pick-date").length&&(c=r("#pick-date").data("value")?r("#pick-date").data("value"):[],s=r("#pick-date").dateRangePicker({singleMonth:!0,autoClose:!0,startOfWeek:"monday",singleDate:!0,language:"se",beforeShowDay:o})),s&&s.bind("datepicker-change",function(e,t){var i=a("#cone_billing_order_date",1);i=i,(n=r("#return-date").dateRangePicker({applyBtnClass:"return",singleMonth:!0,autoClose:!0,startOfWeek:"monday",singleDate:!0,language:"se",startDate:i,beforeShowDay:o})).data("dateRangePicker").open(),n.bind("datepicker-change",function(e,t){r(".checkout-receiving").attr("style",""),a("#cone_billing_return_date",2)})}),r(".checkout-sidebar-headline").on("click",function(){r(".checkout-sidebar-show").slideToggle("fast"),r(".checkout-sidebar-toggle").fadeToggle(100)}),r(".checkout-pickup-return .checkout-pick").on("click",function(){var e=r(this).parent().parent().hasClass("checkout-return")?"return":"delivery",t=(r("delivery"==e?"#cone_billing_get_products":"#cone_billing_return_products").val(r(this).data("type")),r(this).siblings().removeClass("active-checkout-pick"),r(this).addClass("active-checkout-pick"),r(this).data("type")),i="aladdin"==r(this).data("type")?"customer":"aladdin",n=(r("."+e+"-time .type-check-content").removeClass("type-check-active"),r("."+e+"-time-"+i).hide(),r("."+e+"-time-"+t).attr("style",""),!1);r(".active-checkout-pick").each(function(){"aladdin"==r(this).data("type")&&(n=!0)}),n?(r("#aladdin-special-form").find(".cone-not-required").addClass("cone-required"),r("#aladdin-special-form").find(".cone-not-required").removeClass("cone-not-required")):(r("#aladdin-special-form").find(".cone-required").addClass("cone-not-required"),r("#aladdin-special-form").find(".cone-required").removeClass("cone-required")),n?r(".aladdin-delivery-special").attr("style",""):r(".aladdin-delivery-special").hide()}),r(".type-of-service .type-check-content").on("click",function(){r("#billing_customer_type").val(r(this).find("span").html()),r(this).siblings().removeClass("type-check-active"),r(this).addClass("type-check-active"),"none"==document.getElementById("billing-form-info").style.display&&r("#billing-form-info").removeAttr("style"),"privat"==r(this).data("name")?(r(".company-inputs").hide(),r(".company-inputs input").removeClass("cone-required"),r(".company-inputs input").val(""),r("#shipping_address_2").parent().hide(),r("#payment .type-check-content").each(function(){r(this).hasClass("cod")||(r(this).hide(),r(this).removeClass("type-check-active"))}),r(".cd-payment-method input").prop("checked",!1),r(".cod").addClass("type-check-active"),r("#payment_method_cod").prop("checked",!0),r(".other-delivery-address span").text("Annan än bostadsadress"),r(".delivery-private-text").show()):(r("#payment .type-check-content").show(),r(".company-inputs").removeAttr("style"),r("#shipping_address_2").parent().show(),r("#billing_company").addClass("cone-required"),r(".other-delivery-address span").text("Annan än fakturaadress"),r(".delivery-private-text").hide()),e()}),r(".checkout-time-section .type-check-content").on("click",function(){r(this).siblings().removeClass("type-check-active"),r(this).siblings(".extra-charge-text").hide(),r(this).addClass("type-check-active"),r(this).hasClass("extra-charge")&&r(this).siblings(".extra-charge-text").show(),(r(this).parent().hasClass("delivery-time")?(r(".checkout-return").attr("style",""),r("#cone_billing_get_products_time")):(r(".checkout-delivery").attr("style",""),r("#payment").attr("style",""),r("#cone_billing_return_products_time"))).val(r(this).find("span").html())}),r(".aladdin-delivery-special .type-check-content").on("click",function(){r(this).siblings().removeClass("type-check-active"),r(this).addClass("type-check-active"),r("#special_venue_type").val(r(this).find("span").html()),"Annat"==r(this).find("span").html()?(r("#special_venue_other").addClass("cone-required"),r(".other-facility").attr("style","")):(r(".other-facility").hide(),r("#special_venue_other").removeClass("cone-required"))}),r("#special_venue_other").on("change",function(){r("#special_venue_type").val("Annat - "+r(this).val())}),r(".checkout-login-input").on("change",function(){var e=r(this).parent().parent().attr("id");r("#"+("register-user-form"==e?"checkout-login-form":"register-user-form")+" input").each(function(e){this.value=""}),r(".checkout-register-login").attr("data-form",e)}),r("#accept-terms").on("click",function(){1==r("#accept-terms").prop("checked")?r("#accept-terms").val(1):r("#accept-terms").val(0)}),r("#accept-terms-2").on("click",function(){1==r("#accept-terms-2").prop("checked")?r("#accept-terms-2").val(1):r("#accept-terms-2").val(0)}),r(".checkout-register-login").on("click",function(e){e.preventDefault(),"register-user-form"==(form_id=r(this).attr("data-form"))&&(l("#"+form_id)||1!=r("#accept-terms").prop("checked"))||r("#"+form_id).submit(),"register-user-form"==form_id&&0==r("#accept-terms").prop("checked")&&alert("Du måste acceptera villkoren för att fortsätta.")}),r("#header-register-form button").on("click",function(e){e.preventDefault(),l("#header-register-form")||1!=r("#accept-terms-2").prop("checked")||r("#header-register-form").submit(),0==r("#accept-terms-2").prop("checked")&&alert("Du måste acceptera villkoren för att fortsätta.")}),r(".different-from-billing .type-check-content").on("click",function(){r(this).toggleClass("type-check-active"),r(this).hasClass("type-check-active")?(r(this).find("input").val(1),r("#shipping-form").show(),r("#shipping-form input").each(function(e){this.value=""})):(r(this).find("input").val(0),r("#shipping-form").hide(),p())}),r(document).on("click",".cd-payment-method",function(){r(".cd-payment-method").removeClass("type-check-active"),r(this).addClass("type-check-active"),r(".cd-payment-method input").prop("checked",!1),r(this).find("input").prop("checked",!0),r(this).hasClass("cod")&&(r("#invoice_email").prop("checked",!1).val(0),r(".input-invoice-email-address").hide(),r("#invoice_email_address").removeClass("cone-required")),e()}),r("#invoice_email").on("click",function(){1==r(this).prop("checked")?(r(".input-invoice-email-address").show(),r("#invoice_email_address").addClass("cone-required")):(r(".input-invoice-email-address").hide(),r("#invoice_email_address").removeClass("cone-required"))}),r(".checkout-continue-first a").on("click",function(e){e.preventDefault();var t,i=r("#step1").serializeArray(),n="";for(t in i)""!=i[t].value&&(n=(n=(n+='<div class="same-billing-content">')+'<label for="'+i[t].name+'">'+r('input[name="'+i[t].name+'"]').siblings().html()+"</label>")+'<p id="'+i[t].name+'">'+i[t].value+"</p></div>");p(),l("#billing-form-info")||u("1","2")}),r(".call-before-input").on("click",function(){var e;e="call-before-return-input"===this.id?document.getElementById("cone_billing_call_before_return"):document.getElementById("cone_billing_call_before_delivery"),1==r(this).prop("checked")?e.value="Ja":e.value=""}),r(".checkout-header-stages .checkout-header-stage").on("click",function(){var e,t;return!r(".active-checkout-stage").hasClass("checkout-step-3")&&("none"==document.getElementById("billing-form-info").style.display?(alert("Innan du kan gå vidare måste du välja Privatperson, Företag eller Catering och sedan fylla i formuläret."),!1):(e=r(".active-checkout-stage").find(".checkout-header-number").html())!=(t=r(this).find(".checkout-header-number").html())&&void u(e,t))}),r("form.woocommerce-checkout").on("submit",function(e){r("#terms").parent().css("border",""),r("body").prepend('<div class="place-order-overlay"></div>'),r("#terms")[0].checked||(e.preventDefault(),alert("Du måste godkänna villkoren"),r("#terms").parent().css("border","1px solid red"),r(document).find(".place-order-overlay").remove());var t=r("#cone-is-offer")[0].checked?"true":"false";r("#cone_is_offer").val(t),l("body")&&(e.preventDefault(),r(document).find(".place-order-overlay").remove())})}),jQuery(document).ready(function(c){var a,o,i;function n(e,t){date=(1==t?o:i)[0].value,c(e).val(date)}function e(e){var t=1==e.getMonth().toString().length?"0"+(e.getMonth()+1):e.getMonth()+1,i=1==e.getDate().toString().length?"0"+e.getDate():e.getDate(),e=(dmy=i+"-"+t+"-"+e.getFullYear(),e.getFullYear()+"-"+t+"-"+i),t=o&&"return"==this.applyBtnClass?Date.parse(o[0].value):0,i=o&&"return"==this.applyBtnClass?Date.parse(e):1,n=e<r()?"Passerat datum.":"";return!(dmy in a)&&t<i&&e>r()?[!0,"",""]:[!1,"",a[dmy]||n]}function r(){var e=new Date,t=e.getDate(),i=(t+=2,e.getMonth()+1);return e=e.getFullYear()+"-"+(i=i<10?"0"+i:i)+"-"+(t=t<10?"0"+t:t)}c(document).on("click",".cone-remove-product",function(){var e=this.parentNode.parentNode;e.style.display="none",e.className+=" cone-removed-item",c(this).data("bundle")&&(e=c(this).data("bundle"),c(".cone-is-bundle-child-"+e).addClass("cone-removed-item"),c(".cone-is-bundle-child-"+e).css("display","none")),c(".account-table-grid:visible").filter(":even").css("background-color","#FFF"),c(".account-table-grid:visible").filter(":odd").css("background-color","#F9F9F9")}),c(document).on("click","#edit-existing-order",function(e){e.preventDefault();e=document.getElementById("hidden-add-product-container");"block"==e.style.display?e.style.display="none":e.style.display="block","Ändra order"==this.innerHTML?(this.innerHTML="Avbryt",c(".account-item-remove").removeClass("cone-hidden"),c(".account-table").css("border","1px dashed red"),c(".cone-quantity input").prop("disabled",!1),c(".account-table-summation").addClass("cone-display-none"),c("#cone-update-order-form").removeClass("cone-display-none"),c(".account-info input").prop("disabled",!1),c(".account-info select").prop("disabled",!1)):location.reload(),c(".account-item-remove").removeClass("cone-hidden")}),c("#cone-update-order-form").submit(function(){c(".cone-update-order").prop("disabled",!0),c(".special-fields-container input").is(":visible")||c(".special-fields-container input").val(""),alert("Vi har mottagit dina ändringar! Du får ett bekräftelse mail när vi gått igenom ändringarna!")}),c(".clone-select").on("change",function(){var e=c(this).val();c("option",this).removeAttr("selected").filter(function(){return c(this).attr("value")==e}).first().attr("selected","selected")}),c(".cone-update-order").on("click",function(e){if(t=c(".edit-required"),c(".edit-required").attr("style",""),i=!1,t.each(function(){c(this).is(":visible")&&""==c.trim(c(this).val())&&(c(this).css("border","1px red solid"),i=!0)}),i)return e.preventDefault(),c(this).prop("disabled",!1),alert("Vänligen fyll i alla obligatoriska fält."),!1;var t,i;c(".cone-remove-product").each(function(){var e=0<c(this).parent().siblings(".account-item-number").find("input").length?c(this).parent().siblings(".account-item-number").find("input").val():c(this).parent().siblings(".account-item-number").find("span").text(),t=c(this).data("id"),i=c(this).parent().parent().hasClass("cone-removed-item")?"cone_removed_product[]":"cone_product[]",t='<input type="hidden" name="'+i+'" value="'+t+'" />',i=("cone_product[]"==i&&(t+='<input type="hidden" name="cone_qty[]" value="'+e+'" />'),c(".cone-update-order").before(t),c("#account-info-inputs").clone());c(".order-info-container").html(i),document.getElementsByName("cone_billing_get_products")[1].value=document.getElementsByName("cone_billing_get_products")[0].value,document.getElementsByName("cone_billing_get_products_time")[1].value=document.getElementsByName("cone_billing_get_products_time")[0].value,document.getElementsByName("cone_billing_return_products")[1].value=document.getElementsByName("cone_billing_return_products")[0].value,document.getElementsByName("cone_billing_return_products_time")[1].value=document.getElementsByName("cone_billing_return_products_time")[0].value})}),c(document).on("click",".cone-alg-test",function(e){var t,i;return e.preventDefault(),"variable"===c(this).data("product_type")?(window.location.href=c(this).attr("href"),!1):"algolia-autocomplete-listbox-0"!=c(this).parent().parent().parent().parent().attr("id")&&(e=c(this).data("id"),t=c("#cone_orderID").val(),i=1,void c.ajax({url:"",type:"POST",data:{action:"cone_quick_add_item_to_order",order_id:t,product_id:e},success:function(e){c(e).hasClass("existing-products-list")?alert("Produkten finns redan i orden. Öka antal istället."):(1<i&&c(e).find(".cone-quantity").val(i),c(".account-table-summation").before(e))},error:function(e){console.log(e.responseText)}}))}),c(document).on("change",".cone-quantity input",function(){var e=this.value,t=c(this).parent().parent().parent(),i=t.data("price"),n=this.getAttribute("min")?parseInt(this.getAttribute("min")):0;if(parseInt(e)<n&&(e=this.value=n),t.hasClass("cone-is-bundle")){t.data("bundle");var a,o=c(this).parent().parent().data("bpid"),r=t.data("bundleqty");for(a in r)c(".cone-bundle-id-"+o+"-"+r[a].id).find(".account-item-number span").text(e*r[a].qty)}c(this).parent().parent().siblings(".account-item-total").find("h6").html(parseFloat(e*i).toFixed(2)+" kr")}),c(".customer-type-select").on("change",function(){var t=1;c(".customer-type-select").each(function(){var e;"customer"!=c(this).val()?(t=0,(e="cone_billing_return_products"==this.id?c("#cone_billing_return_products_time"):c("#cone_billing_get_products_time")).find(".aladdin-time-option").removeClass("hidden-time"),e.find(".customer-time-option").addClass("hidden-time")):((e="cone_billing_return_products"==this.id?c("#cone_billing_return_products_time"):c("#cone_billing_get_products_time")).find(".aladdin-time-option").addClass("hidden-time"),e.find(".customer-time-option").removeClass("hidden-time"))}),1===t?(c(".special-fields-container").css("display","none"),c(".cd-conditional-info").hide()):(c(".cd-conditional-info").show(),c(".special-fields-container").attr("style","")),c("#cone_billing_get_products_time option").each(function(){if(!c(this).hasClass("hidden-time"))return c(this).attr("selected","selected"),!1}),c("#cone_billing_return_products_time option").each(function(){if(!c(this).hasClass("hidden-time"))return c(this).attr("selected","selected"),!1})}),0!=c("#edit-order-pick-date").length&&(a=c("#edit-order-pick-date").data("value")?c("#edit-order-pick-date").data("value"):[],o=c("#edit-order-pick-date").dateRangePicker({singleMonth:!0,autoClose:!0,startOfWeek:"monday",singleDate:!0,language:"se",beforeShowDay:e}),i=c("#edit-order-return-date").dateRangePicker({applyBtnClass:"return",singleMonth:!0,autoClose:!0,startOfWeek:"monday",singleDate:!0,language:"se",beforeShowDay:e})),o&&(o.bind("datepicker-change",function(e,t){n("#cone_billing_order_date",1)}),i.bind("datepicker-change",function(e,t){n("#cone_billing_return_date",2)}))}),jQuery(document).ready(function(t){new Swiper(".best-seller-swiper",{slidesPerView:4,slidesPerGroup:4,nextButton:".nav-right",prevButton:".nav-left",spaceBetween:15,pagination:".swiper-pagination",paginationClickable:!0,breakpoints:{1e3:{slidesPerView:3,slidesPerGroup:3},800:{slidesPerView:2,slidesPerGroup:2}}}),new Swiper(".checkout-product-swiper",{slidesPerView:2,slidesPerGroup:2,nextButton:".nav-right",prevButton:".nav-left",spaceBetween:15,pagination:".swiper-pagination",paginationClickable:!0,breakpoints:{1e3:{slidesPerView:3,slidesPerGroup:3},800:{slidesPerView:2,slidesPerGroup:2}}}),new Swiper(".yith-similar-products",{slidesPerView:4,slidesPerGroup:4,nextButton:".navy-right",prevButton:".navy-left",spaceBetween:15,pagination:".swiper-pagination",paginationClickable:!0,breakpoints:{1e3:{slidesPerView:3,slidesPerGroup:3},800:{slidesPerView:2,slidesPerGroup:2}}}),new Swiper(".packages-swiper",{slidesPerView:4,slidesPerGroup:4,nextButton:".nav-pack-right",prevButton:".nav-pack-left",spaceBetween:15,pagination:".swiper-pagination",paginationClickable:!0,breakpoints:{1e3:{slidesPerView:3,slidesPerGroup:3},800:{slidesPerView:2,slidesPerGroup:2}}}),new Swiper(".category-swiper",{slidesPerView:4,slidesPerGroup:4,nextButton:".nav-right-cat",prevButton:".nav-left-cat",spaceBetween:15,pagination:".swiper-pagination",paginationClickable:!0,breakpoints:{800:{slidesPerView:3,slidesPerGroup:3},600:{slidesPerView:2,slidesPerGroup:2}}}),new Swiper(".shop-cat-swiper",{slidesPerView:5,slidesPerGroup:5,spaceBetween:60,nextButton:".top-cat-next",prevButton:".top-cat-prev",breakpoints:{900:{slidesPerView:4,slidesPerGroup:4,spaceBetween:40},700:{slidesPerView:4,slidesPerGroup:4,spaceBetween:20},500:{slidesPerView:3,slidesPerGroup:3,spaceBetween:10}}});t(document).on("click",".cart-customlocation",function(e){e.preventDefault(),console.log("clicked menu"),t(".open-cart-section").css("display","flex"),t(".open-cart-section .overlay").fadeIn("fast"),t(".open-cart").delay(10).queue(function(e){t(this).css("transform","translate3d(0%,0,0)"),e()})}),t(document).on("click",".close-open-cart, .open-cart-section .overlay",function(){t(".open-cart").css("transform","translate3d(100%,0,0)"),t(".open-cart-section .overlay").fadeOut("fast"),t(".open-cart-section").delay(300).queue(function(e){t(this).css("display","none"),e()})}),t(document).on("click",".product-card-add a",function(){t(".cart-customlocation").trigger("click")}),t(".question-headline").on("click",function(){t(this).siblings(".answer").slideToggle("fast"),t(this).children(".material-icons").toggleClass("rotate")}),t(".show-product-tab").on("click",function(){t(this).children(".product-tabs-info").slideToggle("fast"),t(this).find(".material-icons").toggleClass("rotate")}),t(".open-register").on("click",function(e){e.preventDefault(),t(".register-modal").fadeIn("fast"),t("html, body").css({height:"100%",overflow:"hidden",position:"relative"})}),t(".open-login").on("click",function(e){e.preventDefault(),t(".login-modal").fadeIn("fast"),t("html, body").css({height:"100%",overflow:"hidden",position:"relative"})}),t(".close-modal").on("click",function(){t(".modal-section").fadeOut("fast"),t("html, body").css({height:"auto",overflow:"visible"})}),t(".already-customer a").on("click",function(){var e="register"==t(this).data("type")?".open-login":".open-register";t(".close-modal").trigger("click"),t(e).trigger("click")}),t(window).width()<1100&&t(".checkout-sidebar-headline").on("click",function(){t(".checkout-sidebar").toggleClass("checkout-height"),t(".checkout-sidebar").hasClass("checkout-height")?t(".checkout-sidebar-show").show():t(".checkout-sidebar-show").hide()}),t("#place_order").on("click",function(){}),t(document).on("click",".add_to_cart_button",function(){toggleLoading("open")}),t(document).ajaxComplete(function(){toggleLoading("close")}),t(".shop-first-i").on("click",function(e){e.preventDefault(),t(this).parent().parent().toggleClass("shop-top-cats-active"),t(this).parent().siblings(".shop-second-cats").removeClass("shop-second-cat-active"),t(this).parent().siblings(".shop-second-cats").find(".shop-third-cat-active").removeClass("shop-third-cat-active"),console.log(t(this).parent().siblings(".shop-second-cats").find(".shop-third-cat-active").removeClass(".shop-third-cat-active"))}),t(".shop-second-i").on("click",function(e){e.preventDefault(),t(this).parent().parent().toggleClass("shop-second-cat-active"),t(this).parent().siblings(".shop-third-cat-active").removeClass("shop-third-cat-active")}),t(".shop-third-i").on("click",function(e){e.preventDefault(),t(this).parent().parent().toggleClass("shop-third-cat-active")}),t(".mobile-shop-menu-trigger").on("click",function(){t(".cone-sortiment").trigger("click")}),t(".mobile-search").on("click",function(e){e.preventDefault();e=!t(".cd-dropdown").hasClass("dropdown-is-active");t(".cd-dropdown").toggleClass("dropdown-is-active",e),t(".cd-dropdown-trigger").toggleClass("dropdown-is-active",e),e||t(".cd-dropdown").one("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){t(".has-children ul").addClass("is-hidden"),t(".move-out").removeClass("move-out"),t(".is-active").removeClass("is-active")}),t(".mobile-s-field")[0].focus()})}),jQuery(document).ready(function(i){i(document).on("click",".no-children",function(e){e.preventDefault(),i(".top-category-active").removeClass("top-category-active"),i(this).parent().addClass("top-category-active"),cat_id=i(this).data("id"),i(".woocommerce-pagination").hide(),window.history.pushState("Utkast","Title",i(this).attr("href")),e=cat_id,i.ajax({url:ajaxApi.ajaxurl,type:"POST",data:"action=cone_filter_products_by_category&cat_id="+e,success:function(e){console.log(e),i(".products").html(e);e=i(".product-card").length;i(".shop-grid-filter span").html(e+" Resultat"),i(".woocommerce-breadcrumb a:last")[0].nextSibling.textContent="/ "+i(".top-category-active p").text()},error:function(e){alert("Något gick fel med filtreringen!"),console.log(e)}})}),i(document).on("click",".cd-search-button",function(){var e=i(this).parent("form").find("input").val();window.location.href="/?s="+e}),i(document).on("click",".product-card-add p",function(){if(i(this).hasClass("cd-variable-product"))return window.location.href=i(this).data("link"),!1;this.style.display="none",i(this).siblings().removeAttr("style")}),i(document).on("change",".product-card-add input",function(){i(this).val()<1&&i(this).val(1),i(this).closest(".product-card-add").find("a").data("quantity",i(this).val())}),i(document).on("click",".add-remove-card i",function(){var e=i(this).html(),t=parseInt(i(this).siblings(".quantity").find("input").val());if("add"!=e&&t<=1)return!1;e="add"==e?t+1:t-1,t=i(this).closest(".add-remove-card").find("input");t.val(e),t.trigger("change")})}),jQuery(document).ready(function(i){i(".gallery-img-wrapper").on("click",function(){var e=i(this).find("img").data("full");console.log(e),i(".gallery-img-wrapper").removeClass("active-gallery"),i(this).addClass("active-gallery"),i(".single-product-img img").attr("src",e),i(".single-product-img img").attr("srcset",e)}),i(".add-rel-product").on("click",function(){if(i(this).hasClass("cd-variable-product"))return window.location.href=i(this).data("link"),!1;i(this).parent().parent().parent().next(".product-card-add").attr("style"," ")}),i(".cd-prompt").on("click",function(){alert(i(this).data("text"))}),0<i(".add-to-existing-order").length&&i("input.qty").on("change",function(){i(".add-to-existing-order").each(function(){var e=(t=i(this).attr("href")).indexOf("&"),t=t.substring(0,-1!=e?e:t.length);i(this).attr("href",t+"&addQty="+i("input.qty").val())})});var n={url:wc_cart_fragments_params.wc_ajax_url.toString().replace("%%endpoint%%","get_refreshed_fragments"),type:"POST",success:function(e){e&&e.fragments&&(i.each(e.fragments,function(e,t){i(e).replaceWith(t)}),i(document.body).trigger("wc_fragments_refreshed"))}};i(".single-product-grid form.cart").on("submit",function(e){e.preventDefault(),toggleLoading("open"),i(".cart-customlocation").trigger("click");var e=window.location,t=i(this);i.post(e,t.serialize()+"&_wp_http_referer="+e,function(e){e=i(".open-cart-grid",e);console.log(e),i(".open-cart-grid").replaceWith(e),i.ajax(n)})})});