/**
 * @preserve
 * Sharer.js
 *
 * @description Create your own social share buttons
 * @version 0.2.11
 * <AUTHOR> <ellis<PERSON><PERSON><PERSON>@gmail.com>
 * @license MIT
 *
 */
!function(e,t){"use strict";var r=function(e){this.elem=e};r.prototype={getValue:function(e){var t=this.elem.getAttribute("data-"+e);return void 0===t||null===t?"":t},share:function(){var e,t,r=this.getValue("sharer"),l=this,u={facebook:function(){e="https://www.facebook.com/sharer/sharer.php",t={u:l.getValue("url")},l.urlSharer(e,t)},googleplus:function(){e="https://plus.google.com/share",t={url:l.getValue("url")},l.urlSharer(e,t)},linkedin:function(){e="https://www.linkedin.com/shareArticle",t={url:l.getValue("url"),mini:!0},l.urlSharer(e,t)},twitter:function(){var r=l.getValue("via");e="https://twitter.com/intent/tweet/",t={text:l.getValue("title"),url:l.getValue("url"),hashtags:l.getValue("hashtags")},r&&(t.via=r),l.urlSharer(e,t)},email:function(){var r=l.getValue("subject");e="mailto:"+l.getValue("to"),t={subject:r,body:r+"\n"+l.getValue("title")+"\n"+l.getValue("url")},l.urlSharer(e,t,!0)},whatsapp:function(){e="whatsapp://send",t={text:l.getValue("title")+" "+l.getValue("url")},l.urlSharer(e,t,!0)},telegram:function(){e="tg://msg_url",t={text:l.getValue("title")+" "+l.getValue("url")},l.urlSharer(e,t,!0)},viber:function(){e="viber://forward",t={text:l.getValue("title")+" "+l.getValue("url")},l.urlSharer(e,t,!0)},line:function(){var t=l.getValue("title")+" "+l.getValue("url");e="http://line.me/R/msg/text/?"+encodeURIComponent(t),l.urlSharer(e,{},!0)},pinterest:function(){e="https://www.pinterest.com/pin/create/button/",t={url:l.getValue("url")},l.urlSharer(e,t)},tumblr:function(){e="http://tumblr.com/widgets/share/tool",t={canonicalUrl:l.getValue("url"),content:l.getValue("url"),posttype:"link",title:l.getValue("title"),caption:l.getValue("caption"),tags:l.getValue("tags")},l.urlSharer(e,t)},hackernews:function(){e="https://news.ycombinator.com/submitlink",t={u:l.getValue("url"),t:l.getValue("title")},l.urlSharer(e,t)},reddit:function(){e="https://www.reddit.com/submit",t={url:l.getValue("url")},l.urlSharer(e,t)},vk:function(){e="http://vk.com/share.php",t={url:l.getValue("url"),title:l.getValue("title"),description:l.getValue("caption"),image:l.getValue("picture")},l.urlSharer(e,t)},xing:function(){e="https://www.xing.com/app/user",t={op:"share",url:l.getValue("url"),title:l.getValue("title")},l.urlSharer(e,t)},buffer:function(){e="https://buffer.com/add",t={url:l.getValue("url"),title:l.getValue("title"),via:l.getValue("twitter-username"),picture:l.getValue("picture")},l.urlSharer(e,t)},instapaper:function(){var r=l.getValue("title")+" "+l.getValue("url");e="http://www.instapaper.com/text",t={u:r},l.urlSharer(e,t,!0)},pocket:function(){e="https://getpocket.com/save",t={url:l.getValue("url"),title:l.getValue("title")},l.urlSharer(e,t)},digg:function(){e="http://www.digg.com/submit",t={url:l.getValue("url")},l.urlSharer(e,t)},stumbleupon:function(){e="http://www.stumbleupon.com/submit",t={url:l.getValue("url"),title:l.getValue("title")},l.urlSharer(e,t)},flipboard:function(){e="https://share.flipboard.com/bookmarklet/popout",t={v:2,title:l.getValue("title"),url:l.getValue("url"),t:Date.now()},l.urlSharer(e,t)},"default":function(){}};return(u[r]||u["default"])()},urlSharer:function(r,l,u){var n,a="object"==typeof l?l:{},i=Object.keys(a),o=i.length>0?"?":"";for(n=0;n<i.length;n++)"?"!==o&&(o+="&"),o+=i[n]+"="+encodeURIComponent(a[i[n]]);if(r+=o,u)e.location.href=r;else{var c=void 0!=e.screenLeft?e.screenLeft:screen.left,h=void 0!=e.screenTop?e.screenTop:screen.top,g=e.innerWidth?e.innerWidth:t.documentElement.clientWidth?t.documentElement.clientWidth:screen.width,s=e.innerHeight?e.innerHeight:t.documentElement.clientHeight?t.documentElement.clientHeight:screen.height,p=g/2-300+c,f=s/2-240+h,m="scrollbars=no, width=600, height=480, top="+f+", left="+p,V=e.open(r,"",m);e.focus&&V.focus()}}},e.addEventListener("load",function(){function e(e){var t=e.currentTarget||e.srcElement,l=new r(t);l.share()}var l,u=t.querySelectorAll(".sharer"),n=u.length;for(l=0;n>l;l++)u[l].addEventListener("click",e)})}(window,document);
