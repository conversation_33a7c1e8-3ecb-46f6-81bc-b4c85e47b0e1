function bulkAddToOrder(order_url) {
	//console.log(order_url);
	var queryJSON = {};
	var url = '';
	jQuery('.woocommerce-cart-form__cart-item').each(function(){
		var p_id = jQuery(this).find('.product-remove a').data('product_id');
		var qty = jQuery(this).find('.product-quantity input').val();
		//console.log(`Id: ${p_id} Qty: ${qty}`);
		queryJSON[p_id] = qty;
	});
	url = encodeURIComponent(JSON.stringify(queryJSON));
	window.location.href = order_url+'?json='+url;
}