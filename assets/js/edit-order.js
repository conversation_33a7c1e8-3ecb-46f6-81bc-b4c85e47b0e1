jQuery(document).ready( function($) {

	$(document).on('click', '.cone-remove-product',function(){
		var parent = this.parentNode.parentNode;
		parent.style.display = 'none';
		parent.className += ' cone-removed-item';
		//this.remove();

		//Remove child rows if product bundle
		if ( $(this).data('bundle') ) {
			var bundle_id = $(this).data('bundle');
			$('.cone-is-bundle-child-'+bundle_id).addClass('cone-removed-item');
			$('.cone-is-bundle-child-'+bundle_id).css('display', 'none');
		} 

		$('.account-table-grid:visible').filter(":even").css('background-color', '#FFF');
		$('.account-table-grid:visible').filter(":odd").css('background-color', '#F9F9F9');
	});

	$(document).on('click', '#edit-existing-order', function(event){
		event.preventDefault();
		var search = document.getElementById('hidden-add-product-container');
		(search.style.display == 'block') ? search.style.display = 'none' : search.style.display = 'block';
		if (this.innerHTML == 'Ändra order') {
			this.innerHTML = 'Avbryt';
			$('.account-item-remove').removeClass('cone-hidden');
			$('.account-table').css('border', '1px dashed red');
			$('.cone-quantity input').prop('disabled', false);
			$('.account-table-summation').addClass('cone-display-none');
			$('#cone-update-order-form').removeClass('cone-display-none');

			$('.account-info input').prop('disabled', false);
			$('.account-info select').prop('disabled', false);
		}else{
			// this.innerHTML = 'Ändra order';
			// $('.account-item-remove').addClass('cone-hidden');
			// $('.account-table').css('border', 'none');
			// $('.cone-quantity input').prop('disabled', true);
			location.reload();
		}

		$('.account-item-remove').removeClass('cone-hidden');
	});

	$('#cone-update-order-form').submit(function() {
	  $('.cone-update-order').prop("disabled", true);
	  //Empty special fields if they're hidden
	  if ( ! $('.special-fields-container input').is(':visible') ) {
	  	$('.special-fields-container input').val('');	
	  }
	  
	  //Show prompt
	  alert('Vi har mottagit dina ändringar! Du får ett bekräftelse mail när vi gått igenom ändringarna!');
	})

	//Set selects option to selected so they can be cloned before submit
	$('.clone-select').on('change', function(){
		var val = $(this).val(); //get new value
		 //find selected option
		 $("option", this).removeAttr("selected").filter(function(){
		     return $(this).attr("value") == val;
		 }).first().attr("selected", "selected"); //add selected attribute to selected optio
	})



	//Update order
	$('.cone-update-order').on('click', function(event){

		//Check if required fields are filled
		if ( coneValidation($('.edit-required')) ) {
			//If not, prevent form submition and alert message
			event.preventDefault();
			$(this).prop('disabled', false)
			alert('Vänligen fyll i alla obligatoriska fält.');
			return false;
		}

		$('.cone-remove-product').each(function(){
			var qty = $(this).parent().siblings('.account-item-number').find('input').length > 0 ? $(this).parent().siblings('.account-item-number').find('input').val() : $(this).parent().siblings('.account-item-number').find('span').text();
			var productID = $(this).data('id');
			var input_name = ( $(this).parent().parent().hasClass('cone-removed-item') ) ? 'cone_removed_product[]' : 'cone_product[]';
			var html = '<input type="hidden" name="'+input_name+'" value="'+ productID + '" />';
			if( input_name == 'cone_product[]' ) html += '<input type="hidden" name="cone_qty[]" value="'+ qty + '" />';
			$('.cone-update-order').before(html);
			var $div = $('#account-info-inputs').clone();
			$('.order-info-container').html( $div );
			document.getElementsByName('cone_billing_get_products')[1].value = document.getElementsByName('cone_billing_get_products')[0].value;
			document.getElementsByName('cone_billing_get_products_time')[1].value = document.getElementsByName('cone_billing_get_products_time')[0].value;
			document.getElementsByName('cone_billing_return_products')[1].value = document.getElementsByName('cone_billing_return_products')[0].value;
			document.getElementsByName('cone_billing_return_products_time')[1].value = document.getElementsByName('cone_billing_return_products_time')[0].value;
		});
	});

	//Add product to order
	$(document).on('click', '.cone-alg-test', function(event){
		event.preventDefault();
		if ( $(this).data('product_type') === 'variable' ) {
			window.location.href = $(this).attr('href');
			//console.log($(this).attr('href'));
			return false;
		}

		//console.log($(this).parent().parent().parent().parent().attr('id'));

		if( $(this).parent().parent().parent().parent().attr('id') == 'algolia-autocomplete-listbox-0' ) return false;

		var product_id = $(this).data('id');

		var order_id = $('#cone_orderID').val();

		//console.log(order_id);

	    //Make ajax call 
	    addToOrderAjax(order_id, product_id, 1);
	});

	//Update line item
	$(document).on('change', '.cone-quantity input', function(){

		var qty = this.value;
		var parent = $(this).parent().parent().parent();
		var price = parent.data('price');
		var min_limit = this.getAttribute('min') ? parseInt( this.getAttribute('min') ) : 0;
		var q = parseInt(qty);

		//console.log(q + " - " + min_limit);

		if ( q < min_limit ) {
			this.value = min_limit;
			qty = min_limit;
			//console.log(this.value);
			//return false;
		}

		//Check if item is bundled product
		if ( parent.hasClass('cone-is-bundle') ){
			var id = parent.data('bundle');
			var belongsTo = $(this).parent().parent().data('bpid');
			var json = parent.data('bundleqty');
			//console.log(json);
			for (var product in json) {
				//console.log(json[product].id);
				var t = $('.cone-bundle-id-'+belongsTo+'-'+json[product].id).find('.account-item-number span');
				t.text(qty * json[product].qty);
			}
		}

		$(this).parent().parent().siblings('.account-item-total').find('h6').html(( parseFloat(qty * price).toFixed(2)) + ' kr');

		//updateTotals();
	});

	function addToOrderAjax(order_id, product_id, qty){
	    $.ajax ({
			url: '',
			type:'POST',
			data: {action: 'cone_quick_add_item_to_order', order_id: order_id, product_id: product_id},
			//data:'action=cone_quick_add_item_to_order&order_id='+ order_id +'&product_id=' + product_id,

			success:function(results) {
				//console.log(results);
				if ( $(results).hasClass('existing-products-list') ) {
					alert('Produkten finns redan i orden. Öka antal istället.');
				}else {
					if ( qty > 1 ) {
						$(results).find('.cone-quantity').val(qty);
					}
					$('.account-table-summation').before(results);
				}
			},
			error: function(data){
				console.log(data.responseText);
			}
		});
	}


	//function that updates totals in realtime
	function updateTotals(){

		var total = 0;
		var	subtotal = 0;
		var	tax = 0;
		var	reduced_tax = 0;

		$(document).find('.account-table-grid').each(function(){
			if ( !$(this).hasClass('account-table-header') ) {
				var item_total = $(this).find('.account-item-total').data('total');
				var tax_class = $(this).data('tax');
				//console.log(item_total);
				subtotal += item_total;

				if (tax_class == 'reduced-rate') {
					reduced_tax += item_total * 0.12;
				}
				else {
					tax += item_total * 1.25;
				}			
			} 
		});


		total = subtotal + tax + reduced_tax;

		// console.log('Subtotal: ' + subtotal); 
		// console.log('25 %: ' + tax);
		// console.log('12 %: ' + reduced_tax);
		// console.log('Total: ' + total);

		$('.cart_subtotal strong:last').html(subtotal + ' kr');
		$('.se-moms-1 strong:last').html(tax + ' kr');
		$('.se-matmoms-1 strong:last').html(reduced_tax + ' kr');
		$('.order_total strong:last').html(total + ' kr');
	}

	//Toggle special fields section
	$('.customer-type-select').on('change', function(){
		var hide = 1;
		//Loop through both selects
		$('.customer-type-select').each(function(){
			//If value of on select isnt customer
			if ($(this).val() != 'customer'){
				//Show the special-fields form
				hide = 0;
				//Set aladdin time in time select'
				var time_select = ( this.id == 'cone_billing_return_products' ) ? $('#cone_billing_return_products_time') : $('#cone_billing_get_products_time');
				//console.log(time_select);
				time_select.find('.aladdin-time-option').removeClass('hidden-time');
				time_select.find('.customer-time-option').addClass('hidden-time');
			}else{
				//Set aladdin time in time select
				var time_select = ( this.id == 'cone_billing_return_products' ) ? $('#cone_billing_return_products_time') : $('#cone_billing_get_products_time');
				time_select.find('.aladdin-time-option').addClass('hidden-time');
				time_select.find('.customer-time-option').removeClass('hidden-time');
			}
		});
		
		//Hide special-fields if both selects was customer
		if (hide === 1) {
			$('.special-fields-container').css('display', 'none');
			$('.cd-conditional-info').hide();
		}
		//Otherwise show it
		else{
			$('.cd-conditional-info').show();
			$('.special-fields-container').attr('style', '');
		}

		//Empty time selects
		$('#cone_billing_get_products_time option').each(function(){
			if ( ! $(this).hasClass('hidden-time') ) {
				$(this).attr('selected', 'selected');
				return false;
			}
		});
		$('#cone_billing_return_products_time option').each(function(){
			if ( ! $(this).hasClass('hidden-time') ) {
				$(this).attr('selected', 'selected');
				return false;
			}
		});
	});

	//Init dateRangePicker
	if ( $('#edit-order-pick-date').length != 0 ) {
	    var unavailableDates = ($('#edit-order-pick-date').data('value')) ? $('#edit-order-pick-date').data('value') : [];//.replace(/\s/g, '').split(',');
	    var editDRP = $('#edit-order-pick-date').dateRangePicker({
	        singleMonth: true,
	        autoClose: true,
	        startOfWeek: 'monday',
	        singleDate: true,
	        language: 'se',
	        beforeShowDay: editUnavailable,
	    });

	    var editReturnDP = $('#edit-order-return-date').dateRangePicker({
	        applyBtnClass: "return",
	        singleMonth: true,
	        autoClose: true,
	        startOfWeek: 'monday',
	        singleDate: true,
	        language: 'se',
	        beforeShowDay: editUnavailable,
	    });
	}

	//On date change
	if ( editDRP ) {
	    editDRP.bind('datepicker-change', function(event,obj){
	        editSetDateValue('#cone_billing_order_date', 1);
	    });

	    editReturnDP.bind('datepicker-change', function(event,obj){
	        editSetDateValue('#cone_billing_return_date', 2);
	    });
	}


	function editSetDateValue(element, type){
	    date = ( type == 1 ) ? editDRP[0].value : editReturnDP[0].value;
	    $(element).val(date);
	}

	function editUnavailable(date) {
	    //Add 0 to month and day so it matches date string because JS cant have 0:s i dates 
	    var month = (date.getMonth().toString().length == 1) ? '0' + (date.getMonth()+1) : (date.getMonth()+1);
	    var day = (date.getDate().toString().length == 1) ? '0' + date.getDate() : date.getDate();
	    //Create datestring in same format as array dates.
	    dmy = day + "-" + month + "-" + date.getFullYear();
	    var m = date.getFullYear() + '-' + month + '-' + day;

	    var d1 = (editDRP && this.applyBtnClass == 'return') ? Date.parse(editDRP[0].value) : 0;
	    var d2 = (editDRP && this.applyBtnClass == 'return') ? Date.parse(m): 1;

	    var tooltip = (m < getToday() ) ? 'Passerat datum.' : '';

	    //If current date isnt in blocked dates array && date isnt passed
	    if ( (!( dmy in unavailableDates ) && d1 < d2) && m > getToday() ) {
	        //Dont block
	        return [true,"",""];
	    } else {
	        tooltip = (unavailableDates[dmy]) ? unavailableDates[dmy] : tooltip;
	        //Else block
	        return [false,"",tooltip];
	    }
	}

	function getToday(){
	    var today = new Date();
	    var dd = today.getDate();
	    dd = dd + 2;
	    var mm = today.getMonth()+1; //January is 0!

	    var yyyy = today.getFullYear();
	    if(dd<10){
	        dd='0'+dd;
	    } 
	    if(mm<10){
	        mm='0'+mm;
	    } 
	    today = yyyy+'-'+mm+'-'+dd;
	    return today;
	}

	function coneValidation(required_fields){
		//Clear earlier errors
		$('.edit-required').attr('style', '');
		//Set fails to false
		var fails = false
		//Loop through required inputs
		required_fields.each(function(){
			//Skip hidden fields
			if ( $(this).is(':visible') ) {
				//Trim value and check for empty values
				if ( $.trim($(this).val()) == '' ) {
					//Add error border and set fails varible to true
					$(this).css('border','1px red solid')
					fails = true;
				}
			}
		});

		return fails;
	}
	
});