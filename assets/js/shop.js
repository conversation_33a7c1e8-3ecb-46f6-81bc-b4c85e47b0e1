jQuery(document).ready( function($) {


	//Filter products on category
	$(document).on('click', '.no-children', function(event){
		event.preventDefault();
		//Change active class to clicked category
		$('.top-category-active').removeClass('top-category-active');
		$(this).parent().addClass('top-category-active');
		//Get the category id for filtering
		cat_id = $(this).data('id');

		//Hide pagination links
		$('.woocommerce-pagination').hide();

		//Push new cat to URL
		window.history.pushState("Utkast", "Title", $(this).attr('href'));
		//Filter products
		ajaxFilterProducts(cat_id);

	});

	$(document).on('click', '.cd-search-button', function(){
		var value = $(this).parent('form').find('input').val();
		window.location.href = '/?s='+value;
	});

	//Open quantity section add-to-cart
	$(document).on('click', '.product-card-add p', function(){
		if (  $(this).hasClass('cd-variable-product') ) {
			window.location.href = $(this).data('link');
			return false;
		}
		this.style.display = 'none';
		$(this).siblings().removeAttr('style');
	});

	//On quantity change, update add-to-cart link.
	$(document).on('change', '.product-card-add input',function(){

		if ( $(this).val() < 1 ) $(this).val(1); 

		//Set link-quantity attr to input  value
		$(this).closest('.product-card-add').find('a').data('quantity', $(this).val());
		//console.log( $(this).closest('.product-card-add').find('a').data('quantity') );

	});

	//Increase or decrease quantity
	$(document).on('click', '.add-remove-card i',function(){
		//Get if increase or decrease is clicked
		var type = $(this).html();
		//Get current quantity value
		var current_value = parseInt( $(this).siblings('.quantity').find('input').val() );
		//If decreased is clicked and quantity is 1 or less abort
		if ( type != 'add' && current_value <= 1 ) return false;
		//Get increased or decreased quantity
		var new_value = (type == 'add') ? current_value + 1 : current_value - 1;
		//Get the input field that should update
		var quantity = $(this).closest('.add-remove-card').find('input');
		//Set new value
		quantity.val(new_value);
		//Trigger change so links and stuff matches new value
		quantity.trigger('change');
	});

	//Filter products on sub categories with no children
	function ajaxFilterProducts(cat_id){

	    //Make ajax call 
	    $.ajax ({
			url: ajaxApi.ajaxurl,
			type:'POST',
			data:'action=cone_filter_products_by_category&cat_id='+cat_id,

			success:function(results) {
				console.log(results);
				$('.products').html(results);
				var count = $('.product-card').length;
				$('.shop-grid-filter span').html(count + ' Resultat');
				$('.woocommerce-breadcrumb a:last')[0].nextSibling.textContent = '/ '+$('.top-category-active p').text();
			},
			error:function(error) {
				alert('Något gick fel med filtreringen!');
				console.log(error);
			}
		});
	}
	
});