<?php
/**
 * Mini-cart
 *
 * Contains the markup for the mini-cart, used by the cart widget.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/mini-cart.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 9.3.0
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

do_action( 'woocommerce_before_mini_cart' ); ?>

<div class="open-cart-grid">

<?php if ( ! WC()->cart->is_empty() ) : ?>

		<?php
			do_action( 'woocommerce_before_mini_cart_contents' );

			foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
				$_product     = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
				$product_id   = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

				if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_widget_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
					$product_name      = apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key );
					$thumbnail         = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image('shop_single'), $cart_item, $cart_item_key );
					$product_price     = apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key );
					$product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
					//var_dump($_product->type);
					?>
					<div class="open-cart-products" <?php if( isset($cart_item['bundled_by']) ) echo 'style="display: none;"'; ?>>
						<?php 
						echo apply_filters( 'woocommerce_cart_item_remove_link', sprintf(
							'<a href="%s" class="remove remove_from_cart_button" aria-label="%s" data-product_id="%s" data-cart_item_key="%s" data-product_sku="%s">&times;</a>',
							esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
							__( 'Remove this item', 'woocommerce' ),
							esc_attr( $product_id ),
							esc_attr( $cart_item_key ),
							esc_attr( $_product->get_sku() )
						), $cart_item_key ); ?>
					    <div class="o-cart-products-left">
					    	<?php echo $thumbnail; ?>
					        <div class="cart-quantity ">
					            <p>Antal: <?php echo $cart_item['quantity']; ?></p>
					        </div>
					    </div>
					    <div class="o-cart-products-right">
					        <a href="<?php echo $product_permalink; ?>"><?php echo $product_name; ?></a>
					        <div class="open-cart-info">
					            <p class="cd-product-span"><?php echo $_product->get_price_html(); ?></p>
					        </div>
					    </div>
					</div>
					<?php
				}
			}

			do_action( 'woocommerce_mini_cart_contents' );
		?>


	<?php do_action( 'woocommerce_widget_shopping_cart_before_buttons' ); ?>

<?php else : ?>
	<span class="no-items-mini"><?php _e( 'No products in the cart.', 'woocommerce' ); ?></span>
<?php endif; ?>
	<div class="open-cart-buttons">
	    <p class="open-cart-sum cart-excl-tax"><?php echo WC()->cart->get_cart_total(); ?> exkl moms</p>
	    <p class="open-cart-sum cart-incl-tax"><?php echo wc_price( WC()->cart->total ); ?> inkl moms</p>
		<a href="<?php echo wc_get_cart_url(); ?>" class="open-cart-button">Visa varukorgen</a>
		<a href="<?php echo wc_get_checkout_url(); ?>" class="open-cart-link">Till kassan</a>
	</div>
</div>
<?php do_action( 'woocommerce_after_mini_cart' ); ?>
