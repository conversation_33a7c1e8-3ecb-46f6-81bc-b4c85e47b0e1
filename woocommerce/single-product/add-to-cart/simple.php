<?php
/**
 * Simple product add to cart
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/add-to-cart/simple.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     7.0.1
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

global $product;

if ( ! $product->is_purchasable() ) {
	return;
}

echo wc_get_stock_html( $product );

if ( $product->is_in_stock() ) : ?>

	<?php do_action( 'woocommerce_before_add_to_cart_form' ); ?>

	<form class="cart" method="post" enctype='multipart/form-data'>
		<?php
			/**
			 * @since 2.1.0.
			 */
			do_action( 'woocommerce_before_add_to_cart_button' );

			/**
			 * @since 3.0.0.
			 */
			do_action( 'woocommerce_before_add_to_cart_quantity' );

			woocommerce_quantity_input( array(
				'min_value'   => apply_filters( 'woocommerce_quantity_input_min', $product->get_min_purchase_quantity(), $product ),
				'max_value'   => apply_filters( 'woocommerce_quantity_input_max', $product->get_max_purchase_quantity(), $product ),
				'input_value' => isset( $_POST['quantity'] ) ? wc_stock_amount( $_POST['quantity'] ) : $product->get_min_purchase_quantity(),
			) );

			/**
			 * @since 3.0.0.
			 */
			do_action( 'woocommerce_after_add_to_cart_quantity' );
		?>

<!-- 		<button type="submit" name="add-to-cart" value="<?php echo esc_attr( $product->get_id() ); ?>" class="single_add_to_cart_button button alt"><?php echo esc_html( $product->single_add_to_cart_text() ); ?></button> -->

		<input type="hidden" name="add-to-cart" value="<?php echo esc_attr( $product->get_id() ); ?>" />

		<button type="submit" class="single_add_to_cart_button button alt <?php if ( get_field('prompt') ) echo 'cd-prompt'; ?>" <?php if ( get_field('prompt') ) echo 'data-text="'.get_field('prompt').'"'; ?>><?php echo esc_html( $product->single_add_to_cart_text() ); ?></button>

		<?php
			/**
			 * @since 2.1.0.
			 */
			do_action( 'woocommerce_after_add_to_cart_button' );
		?>
	</form>

	<?php
	if ( is_user_logged_in() ) {

	    $number_of_orders = -1;

	    $customer_orders = get_posts( apply_filters( 'woocommerce_my_account_my_orders_query', array(
	        'numberposts' => $number_of_orders,
	        'meta_key'    => '_customer_user',
	        'meta_value'  => get_current_user_id(),
	        'post_type'   => wc_get_order_types( 'view-orders' ),
	        'post_status' => array( 'wc-on-hold', 'wc-processing', 'wc-completed')
	    ) ) );

	    //var_dump($customer_orders);
	    $temp = true;
	    if ( $customer_orders && $temp){
	    	if ( count($customer_orders) >= 1 ) {
	    		foreach ($customer_orders as $key => $orderPOST) {
	    			$order = wc_get_order($orderPOST->ID);
	    			//var_dump($order);
    				if ( date('Y-m-d', strtotime("tomorrow")) < date('Y-m-d', strtotime( get_post_meta($order->get_id(), 'cone_billing_order_date', true) )) ) :
    				//var_dump(get_post_meta($order->get_id(), 'cone_billing_order_date', true) . ' '  .get_post_meta($order->get_id(), 'cone_billing_get_products_time', true));
    					$query_string = '?addId='.$product->get_id().'&addQty=1'; 
    				?>
    				<a class="add-to-existing-order" href="<?php echo esc_url( $order->get_view_order_url().$query_string ); ?>">Lägg till i order med hyrdatum <?php echo get_post_meta($order->get_id(), 'cone_billing_order_date', true); ?> | #<?php echo $order->get_order_number(); ?></a>
    			    <?php
    				endif;
	    		}
	    	}
	    }

	}
	?>

	<?php do_action( 'woocommerce_after_add_to_cart_form' ); ?>

<?php endif; ?>
