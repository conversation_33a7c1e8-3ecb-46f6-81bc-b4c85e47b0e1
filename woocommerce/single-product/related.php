 <?php
/**
 * Related Products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/related.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     3.9.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $related_products ) : ?>

	<section class="best-seller-section">
		<div class="max-width">
			<div class="best-seller-top">
				<div class="best-seller-top-headlines">
					<h3 class="active-best-seller"><?php esc_html_e( 'Relaterade Produkter', 'woocommerce' ); ?></h3>
				</div>
			</div>
			<div class="swiper-container best-seller-swiper">
				<div class="swiper-wrapper">

					<?php foreach ( $related_products as $related_product ) : ?>

						<?php
						$post_object = get_post( $related_product->get_id() );

						setup_postdata( $GLOBALS['post'] =& $post_object );

						wc_get_template_part( 'content', 'product' ); ?>

					<?php endforeach; ?>
				</div>
			</div>
			<div class="swiper-button-next nav-right">
				<i class="material-icons ">keyboard_arrow_right</i>
			</div>
			<div class="swiper-button-prev nav-left">
				<i class="material-icons ">keyboard_arrow_left</i>
			</div>
		</div>
	</section>

<?php endif;

wp_reset_postdata();
