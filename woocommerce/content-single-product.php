<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     3.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
global $product;

?>

<?php
	/**
	 * woocommerce_before_single_product hook.
	 *
	 * @hooked wc_print_notices - 10
	 */
	 do_action( 'woocommerce_before_single_product' );

	 if ( post_password_required() ) {
	 	echo get_the_password_form();
	 	return;
	 }
//die(var_dump($product));
//var_dump($product->get_upsells());

?>
<div id="product-<?php the_ID(); ?>" <?php post_class(); ?>>
	<div class="max-width">
		<?php woocommerce_breadcrumb(); ?>
	</div>
	<div class="single-product-grid max-width">
		<div class="single-product-left">
<!--			--><?php
//			/**
//			 * woocommerce_before_single_product_summary hook.
//			 *
//			 * @hooked woocommerce_show_product_sale_flash - 10
//			 * @hooked woocommerce_show_product_images - 20
//			 */
//			do_action( 'woocommerce_before_single_product_summary' );
//
//			woocommerce_output_product_data_tabs();
//			?>
			<div class="single-product-images">
				<div class="single-product-img">
					<?php  echo $product->get_image('full') ; ?>
				</div>
				<div class="single-product-gallery">
				<?php 
					$fullImg = wp_get_attachment_url( get_post_thumbnail_id( get_the_ID() ) );
					$image = wp_get_attachment_image_src( get_post_thumbnail_id( get_the_ID() ) ); ?>

					<div class="gallery-img-wrapper active-gallery">
						<img width="<?php echo $image[1]; ?>" height="<?php echo $image[2]; ?>" src="<?php echo $image[0]; ?>" data-full="<?php echo $fullImg; ?>" />
					</div>
				<?php
				$attachment_ids = $product->get_gallery_image_ids();

				foreach( $attachment_ids as $attachment_id ) 
				{
				  $fullAttachmentImg = wp_get_attachment_url( $attachment_id );
				  $image_link = wp_get_attachment_image_src($attachment_id);

				  echo '<div class="gallery-img-wrapper"><img width="'.$image_link[1].'" height="'.$image_link[2].'" src="'.$image_link[0].'" data-full="'.$fullAttachmentImg.'" /></div>';
				}
				?>
				</div>
			</div>
			<?php if( $product->get_description()) : ?>
			<div class="single-product-tabs">
				<div class="product-tabs-headline">
					<h5>Överblick</h5>
				</div>
				<div class="product-tabs-info">
					<?php echo the_content(); ?>
				</div>
			</div>
			<?php endif ; ?>
			<?php if ( !empty( $product->get_height() ) && !empty( $product->get_width() ) && !empty( $product->get_length() ) ) : ?>
				<div class="single-product-tabs">
					<div class="product-tabs-headline">
						<h5>Mått</h5>
					</div>
					<div class="product-tabs-info">
						<p>H: <?php echo $product->get_height(); ?> cm<br/>
							B: <?php echo $product->get_width(); ?> cm<br/>
							D: <?php echo $product->get_length(); ?> cm<br/>
							V: <?php echo $product->get_weight(); ?> kg<br/>
						</p>
					</div>
				</div>
			<?php endif; ?>
			<?php if( have_rows('product-other-info') ): ?>
				<?php while( have_rows('product-other-info') ) : the_row();?>
					<div class="single-product-tabs show-product-tab">
						<div class="product-tabs-headline">
							<h5><?php the_sub_field('product-other-headline') ; ?></h5>
							<div class="p-t-headline">
								<a href="#">visa</a>
								<i class="material-icons">keyboard_arrow_down</i>
							</div>
						</div>
						<div class="product-tabs-info">
							<?php the_sub_field('product-other-content') ; ?>
						</div>
					</div>
				<?php endwhile; ?>
			<?php endif; ?>
		</div>
		<div class="single-product-right <?php if ( !$product->is_type( 'yith_bundle' ) ) echo '$product->type'; ?>">
			<?php woocommerce_template_single_title() ; ?>
			<?php woocommerce_template_single_excerpt() ; ?>
			<?php if($product->get_sku()) : ?>
				<?php if ( !$product->is_type( 'variable' ) ) : ?>
					<p><span>Artikelnummer: </span><?php echo $product->get_sku(); ?></p>
				<?php endif; ?>
			<?php endif ; ?>
			<div class="single-product-price">
				<div class="single-ex-price" <?php if ( $product->is_type( 'variable' ) ) echo 'style="margin-top: 30px;"'; ?>><?php woocommerce_template_single_price() ; ?></div>
			</div>
			<?php woocommerce_template_single_add_to_cart() ; ?>
			<div class="mobile-product-tabs">
				<?php if( $product->get_description()) : ?>
					<div class="single-product-tabs">
						<div class="product-tabs-headline">
							<h5>Överblick</h5>
						</div>						
						<div class="product-tabs-info">
							<?php the_content() ; ?>
						</div>
					</div>
				<?php endif; ?>
				<?php if ( !empty( $product->get_height() ) && !empty( $product->get_width() ) && !empty( $product->get_length() ) ) : ?>
					<div class="single-product-tabs">
						<div class="product-tabs-headline">
							<h5>Mått</h5>
							<div class="p-t-headline">
								<a href="#">visa</a>
								<i class="material-icons">keyboard_arrow_down</i>
							</div>
						</div>
						<div class="product-tabs-info">
							<p>H: <?php echo $product->get_height(); ?> cm<br/>
								B: <?php echo $product->get_width(); ?> cm<br/>
								D: <?php echo $product->get_length(); ?> cm<br/>
							</p>
						</div>
					</div>
				<?php endif; ?>
				<?php if( have_rows('product-other-info') ): ?>
					<?php while( have_rows('product-other-info') ) : the_row();?>
						<div class="single-product-tabs">
							<div class="product-tabs-headline">
								<h5><?php the_sub_field('product-other-headline') ; ?></h5>
								<div class="p-t-headline">
									<a href="#">visa</a>
									<i class="material-icons">keyboard_arrow_down</i>
								</div>
							</div>
							<div class="product-tabs-info">
								<?php the_sub_field('product-other-content') ; ?>
							</div>
						</div>
					<?php endwhile; ?>
				<?php endif; ?>
			</div>
			<div class="grey-products">
				<?php if ( $product->get_upsell_ids() ): ?>
					<div class="single-sidebar-products">
						<div class="sidebar-products-headline">
							<h4>Aladdin rekommenderar</h4>
							<div class="sidebar-products-border"></div>
						</div>
						<?php foreach ($product->get_upsell_ids() as $key => $upsell_product_id) {
							$up_product = wc_get_product($upsell_product_id); 
							?>
							<div class="sidebar-products-grid">
								<?php echo $up_product->get_image(); ?>
								<div class="sidebar-products-content">
									<a href="<?php echo get_permalink( $upsell_product_id ); ?>"><p> <?php echo $up_product->get_name(); ?> </p></a>
									<div class="sidebar-products-info">
										<div>
											<p class="cd-product-span"><?php echo $up_product->get_price_html(); ?></p>
										</div>
										<i class="material-icons add-rel-product <?php if ( $up_product->is_type( 'variable' ) ) echo 'cd-variable-product' ?>" data-link="<?php echo get_permalink( $upsell_product_id ); ?>">shopping_cart</i>
									</div>
								</div>
							</div>
							<div class="product-card-add" style="display: none;">
								<div class="add-remove-card">
								    <i class="material-icons">remove</i>
								    <?php woocommerce_quantity_input(array('input_value' => isset( $quantity ) ? $quantity : 1)) ; ?>
								    <i class="material-icons">add</i>
								</div>
								<a rel="nofollow" href="<?php echo esc_url( $up_product->add_to_cart_url() ); ?>" data-quantity="<?php echo esc_attr( isset( $quantity ) ? $quantity : 1 ); ?>" data-product_id="<?php echo esc_attr( $up_product->get_id() ); ?>" data-product_sku="<?php echo esc_attr( $up_product->get_sku() ); ?>" class="<?php echo esc_attr( isset( $class ) ? $class : 'bitt' ); ?> product_type_simple add_to_cart_button ajax_add_to_cart">Lägg till</a>
							</div>
						<?php } ?>
					</div>
				<?php endif; ?>
				<?php if ($product->get_cross_sell_ids()) : ?>
					<div class="single-sidebar-products">
						<div class="sidebar-products-headline">
							<h4>Kompletterande produkter</h4>
							<div class="sidebar-products-border"></div>
						</div>
						<?php
						foreach ($product->get_cross_sell_ids() as $key => $cross_sell_product_id) {
							$cross_product = wc_get_product($cross_sell_product_id); 
							if ( $cross_product ) :
							?>
							<div class="sidebar-products-grid">
								<?php echo $cross_product->get_image('full'); ?>
								<div class="sidebar-products-content">
									<a href="<?php echo get_permalink( $cross_sell_product_id ) ?>"><p> <?php echo $cross_product->get_name(); ?> </p></a>
									<div class="sidebar-products-info">
										<div>
											<p class="cd-product-span"><?php echo $cross_product->get_price_html(); ?></p>
										</div>
										<i class="material-icons add-rel-product <?php if ( $cross_product->is_type( 'variable' ) ) echo 'cd-variable-product' ?>" data-link="<?php echo get_permalink( $cross_sell_product_id ); ?>">shopping_cart</i>
									</div>
								</div>
							</div>
							<?php if ( ! $cross_product->is_type( 'variable' ) ) : ?>
								<div class="product-card-add" style="display: none;">
									<div class="add-remove-card">
									    <i class="material-icons">remove</i>
									    <?php woocommerce_quantity_input(array('input_value' => isset( $quantity ) ? $quantity : 1)) ; ?>
									    <i class="material-icons">add</i>
									</div>
									<a rel="nofollow" href="<?php echo esc_url( $cross_product->add_to_cart_url() ); ?>" data-quantity="<?php echo esc_attr( isset( $quantity ) ? $quantity : 1 ); ?>" data-product_id="<?php echo esc_attr( $cross_sell_product_id ); ?>" data-product_sku="<?php echo esc_attr( $cross_product->get_sku() ); ?>" class="<?php echo esc_attr( isset( $class ) ? $class : 'bitt' ); ?> product_type_simple add_to_cart_button ajax_add_to_cart">Lägg till</a>
								</div>
							<?php endif; ?>
							<?php endif; ?>
						<?php } ?>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</div>

	<div class="single-best-sellers">
		<?php
		//woocommerce_output_related_products();
		woocommerce_related_products(array('posts_per_page' => 8));
		?>
	</div>
	<?php echo do_shortcode('[yith_similar_products]') ; ?>


</div><!-- #product-<?php the_ID(); ?> -->

<?php do_action( 'woocommerce_after_single_product' ); ?>

<?php

