<?php
/**
 * My Account navigation
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/navigation.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 9.3.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

do_action( 'woocommerce_before_account_navigation' );
?>

<div class="account-sidebar">
    <h2>Kontoinställningar</h2>
    <div class="account-sidebar-items">
    <?php foreach ( wc_get_account_menu_items() as $endpoint => $label ) : ?>
    	<a href="<?php echo esc_url( wc_get_account_endpoint_url( $endpoint ) ); ?>" class="<?php echo wc_get_account_menu_item_classes( $endpoint ); ?>"><?php echo esc_html( $label ); ?></a>
    <?php endforeach; ?>
    	<a href="<?php echo wp_logout_url( home_url() ); ?>" class="woocommerce-MyAccount-navigation-link woocommerce-MyAccount-navigation-link--orders">Logga ut</a>
    </div>
</div>

<?php do_action( 'woocommerce_after_account_navigation' ); ?>
