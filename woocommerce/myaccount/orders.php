<?php
/**
 * Orders
 *
 * Shows orders on the account page.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/orders.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 9.2.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

do_action( 'woocommerce_before_account_orders', $has_orders ); ?>

<?php if ( $has_orders ) : ?>

	<div class="account-table-section">
	    <div class="account-table-headline">
	        <h1><?php the_title() ; ?></h1>
	    </div>
	    <div class="account-table account-table-border">
	        <div class="account-table-header account-table-grid">
	            <div class="account-table-item order-item-product">
	                <h5>Order</h5>
	            </div>
	            <div class="account-table-item order-item-price">
	                <h5>Hyresdatum</h5>
	            </div>
	            <!-- <div class="account-table-item order-item-number">
	                <h5>Status</h5>
	            </div> -->
	            <div class="account-table-item order-item-total">
	                <h5>Totalt</h5>
	            </div>
	            <div class="account-table-item order-item-remove"></div>
	        </div>

			<?php foreach ( $customer_orders->orders as $customer_order ) :
				$order      = wc_get_order( $customer_order );
				$item_count = $order->get_item_count();
				?>

				<div class="account-table-grid order-table-grid">
				    <div class="account-table-item order-item-product">
				        <p><?php echo _x( '#', 'hash before order number', 'woocommerce' ) . $order->get_order_number(); ?></p>
				    </div>
				    <div class="account-table-item order-item-price">
				        <span><?php echo get_post_meta($order->get_id(), 'cone_billing_order_date', true); ?></span>
				    </div>
				    <div class="account-table-item order-item-total">
				        <span><?php printf( _n( '%1$s for %2$s item', '%1$s for %2$s items', $item_count, 'woocommerce' ), $order->get_formatted_order_total(), $item_count ); ?></span>
				    </div>
				    <div class="account-table-item order-item-remove">
				    	<?php $days = '-'.get_field('days-delay', 'options').' days'; ?>
				        <a class="table-item-desktop" href="<?php echo esc_url( $order->get_view_order_url() ); ?>">Visa<?php if ( date('Ymd') <= date('Ymd', strtotime($days, strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true)))) ) echo ' / Redigera'; ?></a>
						<a class="table-item-mobile" href="<?php echo esc_url( $order->get_view_order_url() ); ?>"><i class="material-icons">edit</i></a>
				    </div>
				</div>
			<?php endforeach; ?>

	<?php do_action( 'woocommerce_before_account_orders_pagination' ); ?>

	<?php if ( 1 < $customer_orders->max_num_pages ) : ?>
		<div class="woocommerce-pagination woocommerce-pagination--without-numbers woocommerce-Pagination">
			<?php if ( 1 !== $current_page ) : ?>
				<a class="woocommerce-button woocommerce-button--previous woocommerce-Button woocommerce-Button--previous button" href="<?php echo esc_url( wc_get_endpoint_url( 'orders', $current_page - 1 ) ); ?>"><?php _e( 'Previous', 'woocommerce' ); ?></a>
			<?php endif; ?>

			<?php if ( intval( $customer_orders->max_num_pages ) !== $current_page ) : ?>
				<a class="woocommerce-button woocommerce-button--next woocommerce-Button woocommerce-Button--next button" href="<?php echo esc_url( wc_get_endpoint_url( 'orders', $current_page + 1 ) ); ?>"><?php _e( 'Next', 'woocommerce' ); ?></a>
			<?php endif; ?>
		</div>
	<?php endif; ?>

<?php else : ?>
	<div class="woocommerce-message woocommerce-message--info woocommerce-Message woocommerce-Message--info woocommerce-info">
		<a class="woocommerce-Button button" href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>">
			<?php _e( 'Go shop', 'woocommerce' ) ?>
		</a>
		<?php _e( 'No order has been made yet.', 'woocommerce' ); ?>
	</div>
<?php endif; ?>

<?php do_action( 'woocommerce_after_account_orders', $has_orders ); ?>
