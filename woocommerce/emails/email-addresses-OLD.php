<?php
/**
 * Email Addresses
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/email-addresses.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates/Emails
 * @version     3.2.1
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$isAdminMail = $GLOBALS['email_data']['admin'];

$text_align = is_rtl() ? 'right' : 'left';

$aladdin_drives = get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' || get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ? true : false;

$changed_fields = (get_post_meta($order->get_id(), 'cone_changed_order_info', true)) ? json_decode( get_post_meta($order->get_id(), 'cone_changed_order_info', true)) : [];

?>
<table cellpadding="0" cellspacing="0" id="addresses" width="100%" border="0">
    <tr>
        <td width="50%" style="border: 1px solid black;	margin: 0 !important; padding: 5px !important; vertical-align: top;">
            <?php if ( get_post_meta($order->get_id(), 'billing_customer_type', true) == 'Privatperson' ) : ?>
                <h2><?php _e( 'Kund', 'woocommerce' ); ?></h2>
            <?php else : ?>
                <h2><?php _e( 'Inregistrerat företagsnamn och adress', 'woocommerce' ); ?></h2>
            <?php endif ?>

            <address class="address">
                <?php echo '<strong>'. get_post_meta($order->get_id(), '_billing_first_name', true) . ' ' . get_post_meta($order->get_id(), '_billing_last_name', true) .'</strong><br/>' ; ?>
                <?php if ( get_post_meta($order->get_id(), 'billing_customer_type', true) != 'Privatperson' ) : ?>
					<?php echo get_post_meta($order->get_id(), 'billing_company', true).'<br/>' ; ?>
                <?php endif; ?>
                <?php echo get_post_meta($order->get_id(), '_billing_address_1', true).'<br/>' ; ?>
                <?php echo get_post_meta($order->get_id(), '_billing_postcode', true) . ' ' . get_post_meta($order->get_id(), '_billing_city', true) .'<br/>' ; ?>
                <?php if ( $order->get_billing_phone() ) : ?>
                    <?php echo esc_html( $order->get_billing_phone() ) . ' (alternativ tel: '.get_post_meta($order->get_id(),'_billing_other_phone', true).')<br/>'; ?>
                <?php endif; ?>
                <?php if ( $order->get_billing_email() ): ?>
                    <p><?php echo esc_html( $order->get_billing_email() ); ?></p>
                <?php endif; ?>
            </address>
        </td>
        <td style="border: 1px solid black;	margin: 0 !important; padding: 0px !important; vertical-align: top;">
            <table cellpadding="0" cellspacing="0" id="cd-first" border="0" width="100%">
                <tr>
                    <td width="25%">
                        <h2><?php _e( 'Hyresdatum', 'woocommerce' ); ?></h2>
                        <p style="font-weight: 700;"><?php echo date_i18n('l d F, Y', strtotime(get_post_meta($order->get_id(),'cone_billing_order_date', true))); ?></p>
                    </td>
                    <td width="25%">
                        <h2><?php _e( 'Bokningsnummer', 'woocommerce' ); ?></h2>
                        <p style="font-weight: 700;"><?php echo $order->get_order_number(); ?></p>
                    </td>
                </tr>
                <?php if ( get_post_meta($order->get_id(), 'billing_company_other_name', true) ) : ?>
	                <tr>
	                    <td colspan="2">
	                        <h2><?php _e( 'Om företag har annat arbetsnamn än inregistrerat namn', 'woocommerce' ); ?></h2>
	                        <p><?php echo get_post_meta($order->get_id(), 'billing_company_other_name', true); ?></p>	                        
	                    </td>
	                </tr>
            	<?php endif; ?>
            </table>
        </td>
    </tr>


    <tr id="test">
    	<td colspan="2" style="padding: 0 !important; vertical-align: top;">
    		<?php $twidth = ( get_post_meta($order->get_id(), 'billing_customer_type', true) == 'Privatperson' ) ? '33' : '25'; ?>
    		<table cellpadding="0" cellspacing="0" id="cd-second" width="100%" border="0">
    		    <tr>
    		        <td width="<?php //echo $twidth; ?>" style="vertical-align: top;">
    		            <h2><?php _e( 'Kundtyp', 'woocommerce' ); ?></h2>
    		            <p><?php echo get_post_meta($order->get_id(), 'billing_customer_type', true); ?></p>
    		        </td>
    		        <td width="<?php //echo $twidth; ?>"  style="vertical-align: top;">
    		            <h2><?php _e( 'Betalningsmetod', 'woocommerce' ); ?></h2>
    		            <p><?php echo get_post_meta($order->get_id(), '_payment_method_title', true); ?></p>
    		            <?php if ( get_post_meta($order->get_id(), '_payment_method', true) == 'cod' ) : ?>
    		                <p style="font-size: 8pt;">*Betalas vid mottagandet eller i förskott med kort / Swish senast vardagen innan leveransdagen på tel 08-664 2000</p>
    		            <?php else : ?>
    		                <p style="font-size: 8pt;">*Efter godkänd kreditprövning</p>
    		            <?php endif ?>
    		        </td>
    		        <?php if ( get_post_meta($order->get_id(), 'billing_customer_type', true) != 'Privatperson' ) : ?>
	    		        <td width="<?php //echo $twidth; ?>" style="vertical-align: top;">
	    		            <h2><?php _e( 'Organisationsnummer', 'woocommerce' ); ?></h2>
	    		            <p><?php echo get_post_meta($order->get_id(), '_billing_org_nr', true); ?></p>
	    		        </td>
    		    	<?php endif; ?>
    		        <td width="<?php //echo $twidth; ?>" style="vertical-align: top;">
    		            <h2><?php _e( 'Bokningsdatum', 'woocommerce' ); ?></h2>
    		            <p><?php echo date_i18n('d F, Y'); ?></p>
    		        </td>
    		    </tr>
    		</table>
    	</td>
    </tr>

	<tr id="test">
		<td colspan="2" style="padding: 0 !important; vertical-align: top;">
			<table cellpadding="0" cellspacing="0" id="cd-third" width="100%" border="0" style="margin-top: 30px;">
			    <tr>
			        <td width="60%" style="vertical-align: top;">
			            <h2><?php if ( in_array('cone_billing_get_products', $changed_fields) || in_array('cone_billing_order_date', $changed_fields) || in_array('cone_billing_get_products_time', $changed_fields)) echo '--->' ?><?php _e( 'Levererar', 'woocommerce' ); ?></h2>
			            <p><?php echo get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' ? 'Kund': 'Aladdin'; ?>, <?php echo date_i18n('l d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true))). ' (ca kl. ' . get_post_meta($order->get_id(), 'cone_billing_get_products_time', true).')'; ?></p>
			        </td>
			        <td width="40%" style="vertical-align: top;">
			            <h2><?php _e( 'Shipping address', 'woocommerce' ); ?></h2>
			            <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' ) : ?>
			                <address class="address">
			                    <?php echo '<strong>'. get_post_meta($order->get_id(), '_shipping_first_name', true) . ' ' . get_post_meta($order->get_id(), '_shipping_last_name', true) .'</strong><br/>' ; ?>
			                    <?php echo get_post_meta($order->get_id(), '_shipping_address_1', true).'<br/>' ; ?>
			                    <?php echo get_post_meta($order->get_id(), '_shipping_postcode', true) . ' ' . get_post_meta($order->get_id(), '_shipping_city', true) .'<br/>' ; ?>
			                </address>
			            <?php endif; ?>
			        </td>
			    </tr>
			    <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' ) : ?>
				    <tr>
				        <td width="100%" colspan="2">
				            <h2><?php _e( 'Avisering vid utkörning', 'woocommerce' ); ?></h2>
				            <p>Kund vill att vi ringer ca 30 min innan leverans: <span style="font-weight: 700; font-size: 13pt;"><?php echo ( get_post_meta($order->get_id(), 'cone_billing_call_before_delivery', true) == 'Ja' ) ? 'Ja' : 'Nej'; ?></span></p>
				        </td>
				    </tr>
				<?php endif; ?>
			    <tr>
			        <td width="60%" style="vertical-align: top;">
			            <h2><?php if ( in_array('cone_billing_return_products', $changed_fields) || in_array('cone_billing_return_date', $changed_fields) || in_array('cone_billing_return_products_time', $changed_fields)) echo '--->' ?><?php _e( 'Returnerar', 'woocommerce' ); ?></h2>
			            <p><?php echo get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ? 'Kund': 'Aladdin'; ?>,
			                <?php echo date_i18n('l d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_return_date', true))). ' (ca kl. ' . get_post_meta($order->get_id(), 'cone_billing_return_products_time', true).')'; ?></p>
			        </td>
			        <td width="40%" style="vertical-align: top;">
			            <h2><?php _e( 'Returadress', 'woocommerce' ); ?></h2>
			            <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) : ?>
			                <address class="address">
			                    <?php echo '<strong>'. get_post_meta($order->get_id(), '_shipping_first_name', true) . ' ' . get_post_meta($order->get_id(), '_shipping_last_name', true) .'</strong><br/>' ; ?>
			                    <?php echo get_post_meta($order->get_id(), '_shipping_address_1', true).'<br/>' ; ?>
			                    <?php echo get_post_meta($order->get_id(), '_shipping_postcode', true) . ' ' . get_post_meta($order->get_id(), '_shipping_city', true) .'<br/>' ; ?>
			                </address>
			            <?php endif; ?>
			        </td>
			    </tr>
			    <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) : ?>
				    <tr>
				        <td width="100%" colspan="2">
				            <h2><?php _e( 'Avisering vid returkörning', 'woocommerce' ); ?></h2>
				            <p>Kund vill att vi ringer ca 30 min innan returkörning: <span style="font-weight: 700; font-size: 13pt;"><?php echo ( get_post_meta($order->get_id(), 'cone_billing_call_before_return', true) == 'Ja' ) ? 'Ja' : 'Nej'; ?></span></p>
				        </td>
				    </tr>
				<?php endif; ?>
			    <?php if ( get_post_meta($order->get_id(), '_billing_order_code', true) ) : ?>
				    <tr>
				        <td width="100%" colspan="2">
				            <h2><?php _e( 'Märkning på beställning', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), '_billing_order_code', true); ?></p>
				        </td>
				    </tr>
				<?php endif; ?>
			</table>
		</td>
	</tr>

	<?php if ( $aladdin_drives ) : ?>
		<tr id="test">
			<td colspan="2" style="padding: 0 !important; vertical-align:top;">
				<table cellpadding="0" cellspacing="0" id="cd-fourth" width="100%" border="0" style="margin-top: 30px;">
				    <tr>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_county', $changed_fields) ) echo '--->'; ?><?php _e( 'Stadsdel', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_county', true); ?></p>
				        </td>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_venue_type', $changed_fields) ) echo '--->'; ?><?php _e( 'Lokaltyp', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_venue_type', true); ?></p>
				        </td>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_doorcode', $changed_fields) ) echo '--->'; ?><?php _e( 'Portkod', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_doorcode', true); ?></p>
				        </td>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_steps_to_elevator', $changed_fields) ) echo '--->'; ?><?php ($isAdminMail) ? _e( 'Trappsteg före entré/hiss', 'woocommerce' ) : _e( 'Antal trappsteg före entré och hiss ca', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_steps_to_elevator', true); ?></p>
				        </td>
				    </tr>
				    <tr>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_floor', $changed_fields) ) echo '--->'; ?><?php _e( 'Våningsplan', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_floor', true); ?></p>
				        </td>
				        <td width="25%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_elevator_size', $changed_fields) ) echo '--->'; ?><?php ($isAdminMail) ? _e( 'Strl Hiss/Max Pers', 'woocommerce' ) : _e( 'Storlek på hiss-max antal personer', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_elevator_size', true); ?></p>
				        </td>
				        <td colspan="2" width="50%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_carry_distance', $changed_fields) ) echo '--->'; ?><?php ($isAdminMail) ? _e( 'Bärsträcka lastbil ca', 'woocommerce' ) :  _e( 'Bärsträcka från lastbil, ca antal meter', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_carry_distance', true); ?></p>
				        </td>
				    </tr>
				    <tr>
				        <td colspan="2" width="50%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_other_contact', $changed_fields) ) echo '--->'; ?><?php ($isAdminMail) ? _e( 'Ev. annan kontakt på plats', 'woocommerce' ) : _e( 'Ev. annan kontakt på leveransplats', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_other_contact', true); ?></p>
				        </td>
				        <td colspan="2" width="50%" style="vertical-align: top;">
				            <h2><?php if ( in_array('special_other_contact_phone', $changed_fields) ) echo '--->'; ?><?php ($isAdminMail) ? _e( 'Tel.nr annan kontakt', 'woocommerce' ) : _e( 'Tel.nr till annan kontakt på leveransplats', 'woocommerce' ); ?></h2>
				            <p><?php echo get_post_meta($order->get_id(), 'special_other_contact_phone', true); ?></p>
				        </td>
				    </tr>
				</table>
			</td>
		</tr>
	<?php endif; ?>
	<tr id="test">
		<td colspan="4" style="padding: 0 !important; vertical-align:top;">
			<table cellpadding="0" cellspacing="0" id="cd-fourth" width="100%" border="0">
				<tr>
					<td colspan="4" width="100%">
					    <h2><?php _e( 'Övriga kommentarer', 'woocommerce' ); ?></h2>
					    <p><?php echo wptexturize( $order->get_customer_note() ); ?></p>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>