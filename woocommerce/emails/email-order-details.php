<?php
/**
 * Order details table shown in emails.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/email-order-details.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates/Emails
 * @version     3.7.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$text_align = is_rtl() ? 'right' : 'left';

do_action( 'woocommerce_email_before_order_table', $order, $sent_to_admin, $plain_text, $email ); ?>

<div style="margin-bottom: 40px;">
	<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; border: 1px solid black;">
		<thead>
			<tr>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Quantity', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Art.nr', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Product', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'St pris exkl 12% moms', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'St pris exkl 25% moms', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Summa denna art. exkl 12% moms', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Summa denna art. exkl 25% moms', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Summa denna art. inkl 12% moms', 'woocommerce' ); ?></th>
				<th class="" scope="col" style="border: 1px solid; text-align:<?php echo $text_align; ?>;"><?php _e( 'Summa denna art. inkl 25% moms', 'woocommerce' ); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php echo wc_get_email_order_items( $order, array(
				'show_sku'      => $sent_to_admin,
				'show_image'    => false,
				'image_size'    => array( 32, 32 ),
				'plain_text'    => $plain_text,
				'sent_to_admin' => $sent_to_admin,
			) ); ?>

</div>

<?php do_action( 'woocommerce_email_after_order_table', $order, $sent_to_admin, $plain_text, $email ); ?>
