<?php
/**
 * Email Order Items
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/email-order-items.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates/Emails
 * @version     3.2.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$text_align = is_rtl() ? 'right' : 'left';

$i12without = 0;
$i12with = 0;
$i25without = 0;
$i25with = 0; 

foreach ( $items as $item_id => $item ) :

	$product = $item->get_product();
	if ( apply_filters( 'woocommerce_order_item_visible', true, $item ) ) {
		$item_data = $item->get_data();

		$cone_class = '';
		$qty = false;
		if ( wc_get_order_item_meta($item_id, '_cone_changed_item') != '' && (wc_get_order_item_meta($item_id, '_cone_changed_item') == 'removed' || wc_get_order_item_meta($item_id, '_cone_changed_item') == 'added') ) {
		  $cone_class = wc_get_order_item_meta($item_id, '_cone_changed_item') . '-row';
		}else{
		  $qty = wc_get_order_item_meta($item_id, '_cone_changed_item');
		}

		//$cone_class = 'removed-row';
		$quantity = ( $qty ) ? '<span class="old-qty">&nbsp;'. explode(',', $qty)[1] .'&nbsp;</span><span class="new-qty bold-text"> '.explode(',', $qty)[2].'</span>' :  $item_data['quantity'];

		//var_dump($order);
		?>
		<tr class="<?php echo esc_attr( apply_filters( 'woocommerce_order_item_class', 'order_item', $item, $order ) ); echo ' '.$cone_class; ?>">
			
			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( $cone_class == 'added-row' ) echo '<div class="added-line">+</div>'; ?>
				<?php if ( $cone_class == 'removed-row' ) echo '<div class="added-line" style="color: red;">X</div>'; ?>
				<?php echo $quantity; ?>
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php echo ( wc_get_order_item_meta($item_id, '_bundled_by') ) ? '-- ' : ''; echo $product->get_sku(); ?>
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; word-wrap:break-word;">
				<?php echo apply_filters( 'woocommerce_order_item_name', $item->get_name(), $item, false ); ?>		
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] === 'reduced-rate' ) echo number_format(round( $product->get_price() , 2), 2, ',', ''); ?>
				<?php endif; ?>	
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] !== 'reduced-rate' ) echo number_format(round( $product->get_price() , 2), 2, ',', ''); ?>
				<?php endif; ?>	
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] === 'reduced-rate' ) echo number_format(round( $order->get_line_subtotal( $item ) , 2), 2, ',', ''); ?>
				<?php endif; ?>	
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] !== 'reduced-rate' ) echo number_format(round( $order->get_line_subtotal( $item ) , 2), 2, ',', ''); ?>
				<?php endif; ?>
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] === 'reduced-rate' ) echo number_format(round($order->get_line_subtotal($item, true), 2), 2, ',', '') ?>
				<?php endif; ?>		
			</td>

			<td class="td" style="text-align:<?php echo $text_align; ?>; vertical-align:middle; border: 1px solid black; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;">
				<?php if ( !wc_get_order_item_meta($item_id, '_bundled_items') ) : ?>
					<?php if ( $item_data['tax_class'] !== 'reduced-rate' ) echo number_format(round($order->get_line_subtotal($item, true), 2), 2, ',', ''); ?>
				<?php endif; ?>	
			</td>
			
		</tr>
		<?php
	}

	if ( $cone_class !== 'removed-row' ) {
		if ( $item_data['tax_class'] === 'reduced-rate' ) {
			$i12without += $order->get_line_subtotal($item);
			$i12with += $order->get_line_subtotal($item, true);
		}else {
			$i25without += $order->get_line_subtotal($item);
			$i25with += $order->get_line_subtotal($item, true);
		} 
	}
	?>

<?php endforeach; ?>

	</tbody>
</table>

<table id="order-sums" cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top; margin-bottom: 40px; padding:0;" border="0">
	<tr>
		<td style="text-align:<?php echo $text_align; ?>; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; padding:0;" valign="top" width="33%">
			<table cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top; padding:0;" border="0">
				<tr>
					<td style="border: 0; padding:0 10px;" width="55%">Summa exkl 12% moms: </td>
					<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><?php echo number_format(round( $i12without, 2), 2, ',', '') ?> kr</td>
				</tr>
				<tr>
					<td style="border: 0; padding:0 10px;" width="55%">Summa exkl 25% moms:</td>
					<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><?php echo number_format(round( $i25without, 2), 2, ',', '') ?> kr</td>
				</tr>
				<?php if ( get_post_meta($order->get_id(), '_payment_method', true) == 'cheque' ) : ?>
					<tr>
						<td style="border: 0; padding:0 10px;" width="55%"><strong>Fakturaavgift (endast företag):</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong>50,00 kr</strong></td>
					</tr>
					<tr>
						<td style="border: 0; padding:0 10px;" width="55%"><strong>Total exkl. moms:</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong><?php echo number_format(round(($i12without + $i25without + 50), 0), 2, ',', ''); ?> kr</strong></td>
					</tr>
				<?php else: ?>
					<tr>
						<td style="border: 0; padding:0 10px;" width="55%"><strong>Total exkl. moms:</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong><?php echo number_format(round(($i12without + $i25without), 0), 2, ',', ''); ?> kr</strong></td>
					</tr>
				<?php endif; ?>
			</table>
		</td>
		<td style="text-align:<?php echo $text_align; ?>; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; padding:0;" valign="top" width="33%">
			<table cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top; padding:0;" border="0">
				<tr>
					<td style="border: 0; padding:0 10px;" width="55%">Summa inkl 12% moms: </td>
					<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><?php echo number_format(round( $i12with, 2), 2, ',', '') ?> kr</td>
				</tr>
				<tr>
					<td style="border: 0; padding:0 10px;"  width="55%">Summa inkl 25% moms:</td>
					<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><?php echo number_format(round( $i25with, 2), 2, ',', '') ?> kr</td>
				</tr>
				<?php if ( get_post_meta($order->get_id(), '_payment_method', true) == 'cheque' ) : ?>
					<tr>
						<td style="border: 0; padding:0 10px; "  width="55%"><strong>Fakturaavgift (endast företag):</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong>62,50 kr</strong></td>
					</tr>
					<tr>
						<td style="border: 0; padding:0 10px; "  width="55%"><strong>Total inkl. moms:</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong><?php echo number_format(round(($i12with + $i25with + 62.5), 0), 2, ',', ''); ?> kr</strong></td>
					</tr>
				<?php else: ?>
					<tr>
						<td style="border: 0; padding:0 10px;"  width="55%"><strong>Total inkl. moms:</strong></td>
						<td style="border: 0; padding:0 8px 0 0; text-align: right;"  width="45%"><strong><?php echo number_format(round(($i12with + $i25with), 0), 2, ',', ''); ?> kr</strong></td>
					</tr>
				<?php endif; ?>
			</table>
		</td>
		<td style="text-align:<?php echo $text_align; ?>; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; padding:0; font-size: 18px;" valign="top" width="33%">
			<strong>Har du beställt transport så tillkommer transportkostnad.</strong>
		</td>
	</tr>
</table>
