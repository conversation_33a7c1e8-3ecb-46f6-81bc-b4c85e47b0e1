<?php
/**
 * Email Styles
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/email-styles.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates/Emails
 * @version 9.3.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

// Load colors
$bg              = get_option( 'woocommerce_email_background_color' );
$body            = get_option( 'woocommerce_email_body_background_color' );
$base            = get_option( 'woocommerce_email_base_color' );
$base_text       = wc_light_or_dark( $base, '#202020', '#ffffff' );
$text            = get_option( 'woocommerce_email_text_color' );

$bg_darker_10    = wc_hex_darker( $bg, 10 );
$body_darker_10  = wc_hex_darker( $body, 10 );
$base_lighter_20 = wc_hex_lighter( $base, 20 );
$base_lighter_40 = wc_hex_lighter( $base, 40 );
$text_lighter_20 = wc_hex_lighter( $text, 20 );

// !important; is a gmail hack to prevent styles being stripped if it doesn't like something.
?>
#wrapper {
	background-color: <?php echo esc_attr( $bg ); ?>;
	margin: 0;
	padding: 70px 0 70px 0;
	-webkit-text-size-adjust: none !important;
	width: 100%;
}

#template_container {
	box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important;
	background-color: <?php echo esc_attr( $body ); ?>;
	border: 1px solid <?php echo esc_attr( $bg_darker_10 ); ?>;
	border-radius: 3px !important;
}

#template_header {
	/*background-color: <?php echo esc_attr( $base ); ?>;*/
	border-radius: 3px 3px 0 0 !important;
	color: <?php echo esc_attr( $base_text ); ?>;
	border-bottom: 0;
	font-weight: bold;
	line-height: 100%;
	vertical-align: middle;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
}

#order-sums td{
	border: 1px solid black;
}

#template_header h1,
#template_header h1 a {
	color: <?php echo esc_attr( $base_text ); ?>;
}

#template_footer td {
	padding: 0;
	-webkit-border-radius: 6px;
}

#template_footer #credit {
	border:0;
	color: <?php echo esc_attr( $base_lighter_40 ); ?>;
	font-family: Arial;
	font-size:12px;
	line-height:125%;
	text-align:center;
	padding: 0 48px 48px 48px;
}

#body_content {
	background-color: <?php echo esc_attr( $body ); ?>;
}

#body_content table td {
	padding: 48px 48px 0;
}

#body_content table td td {
	padding: 12px;
}

#body_content table td th {
	padding: 12px;
}

#body_content td ul.wc-item-meta {
	font-size: small;
	margin: 1em 0 0;
	padding: 0;
	list-style: none;
}

#body_content td ul.wc-item-meta li {
	margin: 0.5em 0 0;
	padding: 0;
}

#body_content td ul.wc-item-meta li p {
	margin: 0;
}

#body_content p {
	margin: 0 0 16px;
}

#body_content_inner {
	color: black;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
	font-size: 14px;
	line-height: 150%;
	text-align: <?php echo is_rtl() ? 'right' : 'left'; ?>;
}

.td {
	color: black;
	border: 1px solid black;
}

.address {
	/*padding:12px 12px 0;*/
	color: <?php echo esc_attr( $text_lighter_20 ); ?>;
	font-style: normal;
	font-weight: 700;
	font-size: 20px;
	/*border: 1px solid <?php echo esc_attr( $body_darker_10 ); ?>;*/
}

.text {
	color: <?php echo esc_attr( $text ); ?>;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
}

.link {
	color: <?php echo esc_attr( $base ); ?>;
}

#header_wrapper {
	padding: 36px 48px;
	display: block;
}

h1 {
	color: #000 !important;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
	font-size: 30px;
	font-weight: 300;
	line-height: 150%;
	margin: 0;
	text-align: <?php echo is_rtl() ? 'right' : 'left'; ?>;
	/*text-shadow: 0 1px 0 <?php echo esc_attr( $base_lighter_20 ); ?>;*/
	-webkit-font-smoothing: antialiased;
}

h2 {
	color: <?php echo esc_attr( $base ); ?>;
	display: block;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
	font-size: 18px;
	font-weight: bold;
	line-height: 130%;
	margin: 0 0 18px;
	text-align: <?php echo is_rtl() ? 'right' : 'left'; ?>;
}

h3 {
	color: <?php echo esc_attr( $base ); ?>;
	display: block;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
	font-size: 16px;
	font-weight: bold;
	line-height: 130%;
	margin: 16px 0 8px;
	text-align: <?php echo is_rtl() ? 'right' : 'left'; ?>;
}

a {
	color: <?php echo esc_attr( $base ); ?>;
	font-weight: normal;
	text-decoration: underline;
}

img {
	border: none;
	display: inline;
	font-size: 14px;
	font-weight: bold;
	height: 88px;
	line-height: 100%;
	outline: none;
	text-decoration: none;
	text-transform: capitalize;
}

/* CONE CHANGED ORDER*/

.bold-text{
  font-weight: bold;
}

.removed-row td{
  position: relative; 
}

p{
	font-size: 20px;
}

.removed-line {
  height: 1px;
  width: 150%;
  background: red;
  margin-left: -20px;
}

strike {
    text-decoration: none;
    background-image: linear-gradient(transparent 7px,#cc1f1f 7px,#cc1f1f 9px,transparent 9px);
}

.added-row td {
  font-weight: 700;
  position: relative;
}

.added-line {
  display: inline-block;
  color: black;
  font-size: 20px;
  margin-right: 5px;
}

.old-qty{
  text-decoration: line-through;
  color: red;
  margin-right: 3px;
}

#addresses td{
	/*border: 1px solid black;
	margin: 0 !important;
	padding: 5px !important;
	vertical-align: baseline;*/
}

#addresses {
	border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: 40px;
}


#addresses table {
	border-collapse: collapse;
    border-spacing: 0;
}

#addresses h2 {
	font-size: 13.5pt;
	margin: 0;
}

#addresses p {
	font-size: 11pt;
	margin: 0;
	color: black;
}

#addresses address {
	font-size: 11pt;
	margin: 0;
	color: black;
}

#cd-first td{
	border: 1px solid black;
	margin: 0 !important;
	padding: 5px !important;
	vertical-align: baseline;
}

#cd-second td{
	border: 1px solid black;
	margin: 0 !important;
	padding: 5px !important;
	vertical-align: baseline;
}

#cd-third td{
	border: 1px solid black;
	margin: 0 !important;
	padding: 5px !important;
	vertical-align: baseline;
}

#cd-fourth td{
	border: 1px solid black;
	margin: 0 !important;
	padding: 5px !important;
	vertical-align: baseline;
}

#new-order-text p{
	color: black;
}
<?php
