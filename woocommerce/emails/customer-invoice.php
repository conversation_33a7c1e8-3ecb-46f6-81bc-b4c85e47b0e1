<?php
/**
 * Customer invoice email
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/customer-invoice.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates/Emails
 * @version     3.7.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * @hooked WC_Emails::email_header() Output the email header
 */
do_action( 'woocommerce_email_header', 'Tack för din beställning', $email ); ?>

<?php if ( $order->has_status( 'pending' ) ) : ?>
	<p><?php printf( __( 'An order has been created for you on %1$s. To pay for this order please use the following link: %2$s', 'woocommerce' ), get_bloginfo( 'name', 'display' ), '<a href="' . esc_url( $order->get_checkout_payment_url() ) . '">' . __( 'pay', 'woocommerce' ) . '</a>' ); ?></p>
<?php endif; ?>
<div>
	<?php echo get_field('email_thank_you_text', 'option'); ?>
	<p>_____________________________________________________________________________________________________________________</p>
</div>

<?php

/**
 * @hooked WC_Emails::customer_details() Shows customer details
 * @hooked WC_Emails::email_address() Shows email address
 */
do_action( 'woocommerce_email_customer_details', $order, $sent_to_admin, $plain_text, $email ); ?>

<table id="g" cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top; margin-bottom: 40px; padding:0;" border="0">
	<tr>
		<td style="text-align:<?php echo $text_align; ?>; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; border:0; padding:0;" valign="top" width="50%">
			<?php if ( get_post_meta($order->get_id(), '_billing_order_code', true) ) : ?>
				<strong>Märkning på beställning: </strong><?php echo get_post_meta($order->get_id(), '_billing_order_code', true); ?>
				<br>
			<?php endif; ?>
			<strong>Kundtyp: </strong><?php echo get_post_meta($order->get_id(), 'billing_customer_type', true); ?>
			<br>
			<strong>Levererar: </strong>
			<?php echo get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' ? 'Kund': 'Aladdin'; ?>,
            <?php echo date('d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true))). ' (kl. ' . get_post_meta($order->get_id(), 'cone_billing_get_products_time', true).')'; ?>
			<br>
			<?php if ( get_post_meta($order->get_id(), 'cone_billing_call_before_delivery', true) == 'Ja' ) : ?>
				<strong>Ring ca 30 min innan leverans: </strong>Ja
				<br>
			<?php endif; ?>
			<strong>Returnerar: </strong>
			<?php echo get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ? 'Kund': 'Aladdin'; ?>,
			<?php echo date('d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_return_date', true))). ' (kl. ' . get_post_meta($order->get_id(), 'cone_billing_return_products_time', true).')'; ?>
			<br>
			<?php if ( get_post_meta($order->get_id(), 'cone_billing_call_before_return', true) == 'Ja' ) : ?>
				<strong>Ring ca 30 min innan retur: </strong>Ja
				<br>
			<?php endif; ?>
		</td>
		<?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' || get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) : ?>
			<td style="text-align:<?php echo $text_align; ?>; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; padding:0;" valign="top" width="50%">
				<strong>Lokaltyp: </strong><?php echo get_post_meta($order->get_id(), 'special_venue_type', true); ?>
				<br>
				<strong>Stadsdel: </strong><?php echo get_post_meta($order->get_id(), 'special_county', true); ?>
				<br>
				<strong>Våningsplan: </strong><?php echo get_post_meta($order->get_id(), 'special_floor', true); ?>
				<br>
				<strong>Portkod: </strong><?php echo get_post_meta($order->get_id(), 'special_doorcode', true); ?>
				<br>
				<strong>Antal trappsteg före entré/hiss: </strong><?php echo get_post_meta($order->get_id(), 'special_steps_to_elevator', true); ?>
				<br>
				<strong>Storlek på hiss - max antal pers: </strong><?php echo get_post_meta($order->get_id(), 'special_elevator_size', true); ?>
				<br>
				<strong>Bärsträcka(meter): </strong><?php echo get_post_meta($order->get_id(), 'special_carry_distance', true); ?>
				<br>
				<strong>Ev annan kontakt: </strong><?php echo get_post_meta($order->get_id(), 'special_other_contact', true); ?>
				<br>
				<strong>Ev annan kontakt tel: </strong><?php echo get_post_meta($order->get_id(), 'special_other_contact_phone', true); ?>
				<br>
				<?php if ( get_post_meta($order->get_id(), 'invoice_email', true) == true ) : ?>
					<strong>Faktura e-post: </strong><?php echo get_post_meta($order->get_id(), 'invoice_email_address', true); ?>
					<br>
				<?php endif; ?>
				<?php if ( $order->get_customer_note() ) : ?>
					<strong>Övriga kommentarer: </strong><?php echo wptexturize( $order->get_customer_note() ); ?>
					<br>
				<?php endif; ?>
			</td>
		<?php endif; ?>
	</tr>
</table>
<?php
/**
 * @hooked WC_Emails::order_details() Shows the order details table.
 * @hooked WC_Structured_Data::generate_order_data() Generates structured data.
 * @hooked WC_Structured_Data::output_structured_data() Outputs structured data.
 * @since 2.5.0
 */
do_action( 'woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email );

/**
 * @hooked WC_Emails::order_meta() Shows order meta data.
 */
do_action( 'woocommerce_email_order_meta', $order, $sent_to_admin, $plain_text, $email );

/**
 * @hooked WC_Emails::email_footer() Output the email footer
 */
do_action( 'woocommerce_email_footer', $email );
