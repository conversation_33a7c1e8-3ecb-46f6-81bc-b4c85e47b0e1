<?php

/**
 * Order details
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/order/order-details.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 9.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! $order = wc_get_order( $order_id ) ) {
	return;
}

$show_purchase_note    = $order->has_status( apply_filters( 'woocommerce_purchase_note_order_statuses', array( 'completed', 'processing' ) ) );
//$show_customer_details = is_user_logged_in() && $order->get_user_id() === get_current_user_id();

if ( $_SERVER['QUERY_STRING'] ) {
	if (strpos($_SERVER['QUERY_STRING'], 'json') !== false) {
	    $json = $_SERVER['QUERY_STRING'];
	    $a = urldecode(explode('json=',$json)[1]);
	    $add_to_order_var = $a;

	}else {
		$query_strings = explode('&', $_SERVER['QUERY_STRING']);
		$added_product_id = explode('=', $query_strings[0])[1];
		$added_qty = explode('=', $query_strings[1])[1];
		$add_to_order_var = $added_product_id;
	}
	
	//var_dump($add_to_order_var);
}

?>

<div class="account-table-section">
    <div class="account-table-headline">
        <h1><?php echo 'Order #' . $order->get_order_number(); ?></h1>
        <?php 
        	$days = '-'.get_field('days-delay', 'options').' days';
        	if ( date('Ymd') <= date('Ymd', strtotime($days, strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true)))) ) : ?>
			<div class="table-search-content">
				<div id="hidden-add-product-container" style="display: none;">
					<form role="search" method="get" class="search-form cd-quick-add" action="<?php echo home_url( '/' ); ?>">
					    <i class="material-icons">search</i>
					    <label>
					        <input type="search" class="search-field"
					               placeholder="<?php echo esc_attr_x( 'Lägg till produkt', 'placeholder' ) ?>"
					               value="<?php echo get_search_query() ?>" name="s"
					               title="<?php echo esc_attr_x( 'Search for:', 'label' ) ?>" />
					    </label>
					</form>
				</div>
				<a href="" id="edit-existing-order">Ändra order</a>
	   		</div>
   		<?php endif; ?>
    </div>

    <div class="account-table account-table-border">
        <div class="account-table-header account-table-grid">
            <div class="account-table-item account-item-img"></div>
            <div class="account-table-item account-item-product">
                <h5>Produkt</h5>
            </div>
            <div class="account-table-item account-item-price">
                <h5>Pris</h5>
            </div>
            <div class="account-table-item account-item-number">
                <h5>Antal</h5>
            </div>
            <div class="account-table-item account-item-total">
                <h5>Totalt</h5>
            </div>
            <div class="account-table-item account-item-remove"></div>
        </div>

		<?php
			$bundle_count = 0;
			foreach ( $order->get_items() as $item_id => $item ) {
				$product = apply_filters( 'woocommerce_order_item_product', $item->get_product(), $item );
				//var_dump($product->get_tax_class());
				if ( $product->is_type( 'yith_bundle' ) ) {
				    //var_dump($product);
				    $bundle_count++;
				    $belongsto = $product->get_id();
				}

				wc_get_template( 'order/order-details-item.php', array(
					'order'			     => $order,
					'item_id'		     => $item_id,
					'item'			     => $item,
					'show_purchase_note' => $show_purchase_note,
					'purchase_note'	     => $product ? $product->get_purchase_note() : '',
					'product'	         => $product,
					'bundle_count'		 => $bundle_count,
					'belongsto'			 => isset($belongsto) ? $belongsto : null
				) );
			}
		?>
		<?php do_action( 'woocommerce_order_items_table', $order ); ?>

		<div class="account-table-summation">
		    <div class="checkout-sidebar-info">
			<?php
			//var_dump($order->get_order_item_totals());
				foreach ( $order->get_order_item_totals() as $key => $total ) {
					?>
				        <div class="<?php echo $key; ?>">
				            <strong><?php echo $total['label']; ?></strong>
				            <strong><?php echo $total['value']; ?></strong>
				        </div>
					<?php
				}
			?>
			</div>
		</div>
	</div>
	<div id="account-info-inputs" class="account-info">

		<?php if ( get_post_meta($order->get_id(), 'billing_customer_type', true) == 'Privatperson' ) {
			include get_template_directory() . '/inc/edit-order/privat-form.php';
		}
		else{
			include get_template_directory() . '/inc/edit-order/company-form.php';
		} ?>
		<div class="two-inputs">
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_order_date">Hyrdatum</label>
				<input type="text" id="edit-order-pick-date" data-value='<?php echo get_option( 'cone_blocked_dates' ); ?>' value="<?php echo get_post_meta($order->get_id(), 'cone_billing_order_date', true); ?>"  autocomplete="off" disabled />
				<input type="hidden" id="cone_billing_order_date" class="edit-required" name="cone_billing_order_date" value="<?php echo get_post_meta($order->get_id(), 'cone_billing_order_date', true); ?>" />
			</div>
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_return_date">Returdatum</label>
				<input type="text" id="edit-order-return-date" name="" value="<?php echo get_post_meta($order->get_id(), 'cone_billing_return_date', true); ?>" autocomplete="off" disabled />
				<input type="hidden" id="cone_billing_return_date" name="cone_billing_return_date" class="edit-required" value="<?php echo get_post_meta($order->get_id(), 'cone_billing_return_date', true); ?>" />
			</div>
		</div>
		<?php
			$hide_style = ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' || get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) ? '': 'display: none;'; 
		?>
		<div class="cd-conditional-info" style="<?php  echo $hide_style ?>">
			<strong><?php echo get_field('choose-time-info', 'option'); ?></strong>
			<strong><?php echo get_field('transport-fee', 'option'); ?></strong>
		</div>
		<div class="two-inputs">
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_get_products">Hämtar varor</label>
				<select id="cone_billing_get_products" class="customer-type-select clone-select" name="cone_billing_get_products" disabled>
					<option <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' ) echo 'selected="selected"';  ?> value="customer">Kund</option>
					<option <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' ) echo 'selected="selected"';  ?> value="aladdin">Aladdin</option>
				</select>
			</div>
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_get_products_time">kl:</label>
				<select id="cone_billing_get_products_time" class="clone-select" name="cone_billing_get_products_time" disabled>
					<?php if( have_rows('customer-pick-up', 'options') ): ?>
						<?php while( have_rows('customer-pick-up', 'options') ) : the_row(); ?>
							<?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' && get_sub_field('customer-pick-up-time', 'options') == get_post_meta($order->get_id(), 'cone_billing_get_products_time', true) ) {
								$selected = true;
							} else { 
								$selected = false;
							} ?>
							<option 
							class="customer-time-option <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' ) echo 'hidden-time' ?>"
							<?php if ($selected) echo 'selected="selected"'; ?> value="<?php the_sub_field('customer-pick-up-time', 'options') ; ?>"
							>
								<?php the_sub_field('customer-pick-up-time', 'options') ; ?>
							</option>
						<?php endwhile; ?>
					<?php endif; ?>

					<?php if( have_rows('aladdin-deliver', 'options') ): ?>
						<?php while( have_rows('aladdin-deliver', 'options') ) : the_row(); ?>
							<?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'aladdin' && get_sub_field('aladdin-deliver-time', 'options') == get_post_meta($order->get_id(), 'cone_billing_get_products_time', true) ) { 
								$selected = true;
							} else{ 
								$selected = false;
							} ?>
							<option 
							class="aladdin-time-option <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' ) echo 'hidden-time' ?>"
							<?php if ($selected) echo 'selected="selected"'; ?>
							value="<?php the_sub_field('aladdin-deliver-time', 'options') ; ?>"
							>
								<?php the_sub_field('aladdin-deliver-time', 'options') ; ?>
							</option>
						<?php endwhile; ?>
					<?php endif; ?>
				</select>
			</div>
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_return_products">Lämnar varor</label>
				<select id="cone_billing_return_products" class="customer-type-select clone-select" name="cone_billing_return_products" disabled="">
					<option <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ) echo 'selected="selected"';  ?> value="customer">Kund</option>
					<option <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) echo 'selected="selected"';  ?> value="aladdin">Aladdin</option>
				</select>
			</div>
			<div class="checkout-input checkout-content checkout-content-first" >
				<label for="cone_billing_return_products_time">kl:</label>
				<select id="cone_billing_return_products_time" class="clone-select" name="cone_billing_return_products_time" disabled>
					<?php if( have_rows('customer-deliver', 'options') ): ?>
						<?php while( have_rows('customer-deliver', 'options') ) : the_row(); ?>
							<?php ( get_sub_field('customer-deliver-time', 'options') == get_post_meta($order->get_id(), 'cone_billing_return_products_time', true) ) ? $selected = true : $selected = false; ?>
							<option
							class="customer-time-option <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'aladdin' ) echo 'hidden-time' ?>"
							<?php if ($selected) echo 'selected="selected"'; ?>
							value="<?php the_sub_field('customer-deliver-time', 'options') ; ?>"
							>
								<?php the_sub_field('customer-deliver-time', 'options') ; ?>
							</option>
						<?php endwhile; ?>
					<?php endif; ?>

					<?php if( have_rows('aladdin-pick-up', 'options') ): ?>
						<?php while( have_rows('aladdin-pick-up', 'options') ) : the_row(); ?>
							<?php ( get_sub_field('aladdin-pick-up-time', 'options') == get_post_meta($order->get_id(), 'cone_billing_return_products_time', true) ) ? $selected = true : $selected = false; ?>
							<option 
							class="aladdin-time-option <?php if ( get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ) echo 'hidden-time' ?>"
							<?php if ($selected) echo 'selected="selected"'; ?>
							value="<?php the_sub_field('aladdin-pick-up-time', 'options') ; ?>"
							>
								<?php the_sub_field('aladdin-pick-up-time', 'options') ; ?>
							</option>
						<?php endwhile; ?>
					<?php endif; ?>
				</select>
			</div>
		</div>
		<!-- Special Form -->
		<?php include get_template_directory() . '/inc/edit-order/special-fields.php'; ?>
		<!-- Special form end -->
	</div>

	<!-- Cone -->

	<?php if ( date('Ymd') <= date('Ymd', strtotime($days, strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true)))) ) { ?>
		<form id="cone-update-order-form" class="cone-display-none" method="POST" action="">
			<input type="hidden" name="action" value="cone_update_order" />
			<input type="hidden" id="cone_orderID" name="cone_orderID" value="<?php echo $order->get_id() ?>" />
			<div class="order-info-container" style="display: none;"></div>
			<button class="cone-update-order" name="change_submit">Skicka ändringar</button>
	    </form>
		


	<?php } ?>

	<?php 
	if( $_SERVER['QUERY_STRING'] == 'order=updated' ) {
	    echo '<script>
	    		alert("Ordern har uppdaterats");
	    		var url = window.location.href;
	    		var a = url.indexOf("?");
	    		var b =  url.substring(a);
	    		var c = url.replace(b,"");
	    		history.pushState("", "", c);
	    	</script>';
	}

	if ( isset($add_to_order_var) ){
		?>
		<script>jQuery(document).ready( function($) {
				$("#edit-existing-order").trigger("click");
			    $.ajax ({
					url: "",
					type:"POST",
					data: {action: 'cone_quick_add_item_to_order', order_id: <?php echo $order_id; ?>, product_id: <?php echo $add_to_order_var; ?>},
					success:function(results) {
						//console.log(results);
						if ( typeof results == "object" ) {
							alert("Produkten finns redan i orden. Öka antal istället.");
						}else {
							$(".account-table-summation").before(results);
							if ( $(document).find('.existing-products-list').length > 0 ) {
								var message = '';
								$(document).find('.existing-products-list').each(function () {
									message += 'Produkten '+ $(this).text() +' finns redan i ordern. Var god öka antalet istället.\n';
								});
								$(document).find('.existing-products-list').remove();
								alert(message);
							}
							alert('Scrolla ner och tryck på "Skicka ändringar" för att spara ordern.');
						}
					},
					error: function(data){
						console.log(data.responseText);
					}
				});
			});
		</script>
		<?php
	}

	?>

	<!-- End Cone -->



	<?php do_action( 'woocommerce_order_details_after_order_table', $order ); ?>