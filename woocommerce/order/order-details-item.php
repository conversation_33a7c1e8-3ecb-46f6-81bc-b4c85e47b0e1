<?php
/**
 * Order Item Details
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/order/order-details-item.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 5.2.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! apply_filters( 'woocommerce_order_item_visible', true, $item ) ) {
	return;
}
$days = '+' . get_option( 'options_close_order' ) . ' days';
$isBundle = $product->is_type( 'yith_bundle' );
$isBundleChild = wc_get_order_item_meta( $item_id, '_bundled_by' );
//var_dump($isBundleChild);
//apply_filters( 'woocommerce_order_item_class', 'woocommerce-table__line-item order_item', $item, $order ) == 'woocommerce-table__line-item order_item yith-wcpb-child-of-bundle-table-item' ? true : false;
//var_dump($product->price_per_item_tot);
$bundle_qty = '';
if ( $isBundle ){
    $bundle_qty = "data-bundleqty='[";
    foreach ($product->bundle_data as $key => $bp) {
        //var_dump($bp['product_id'] . ' - ' . $product->id);
        $bundle_qty .= '{"id":'. intval($bp["product_id"]) .',"qty":'. intval($bp["bp_min_qty"]) .'},';
    }
    $bundle_qty = rtrim($bundle_qty,", ");
    $bundle_qty .= "]'";
}
?>
<div class="account-table-grid <?php if ( $isBundleChild ) echo 'cone-bundle-id-'.$belongsto.'-'.$product->get_id().' cone-is-bundle-child-'.$bundle_count; if ( $isBundle ) echo 'cone-is-bundle cone-is-bundle-'.$product->get_id(); ?>" <?php if ( $isBundleChild ) echo 'data-belongsto="'. $belongsto .'"'; ?> data-price="<?php echo $isBundle ? ( wc_get_order_item_meta( $item_id, '_yith_bundle_totals' )["line_subtotal"] / $item->get_quantity()) : $product->get_price(); ?>" data-tax="<?php echo $item['tax_class']; ?>" <?php if ( $isBundle ) echo  'data-bundle="'.$bundle_count.'"'; ?> <?php echo $bundle_qty; ?>>
    <div class="account-table-item account-item-img">
    	<?php  echo $product->get_image() ; ?>
    </div>
    <div class="account-table-item account-item-product">
    <?php
    	$is_visible        = $product && $product->is_visible();
    	$product_permalink = apply_filters( 'woocommerce_order_item_permalink', $is_visible ? $product->get_permalink( $item ) : '', $item, $order );
        $item_name = $isBundleChild ? '<a href="%s">&nbsp;&nbsp;&nbsp;-&nbsp;%s</a>' : '<a href="%s">%s</a>';

    	echo apply_filters( 'woocommerce_order_item_name', $product_permalink ? sprintf( $item_name, $product_permalink, $item->get_name() ) : $item->get_name(), $item, $is_visible ); ?>
    </div>
    <div class="account-table-item account-item-price">
        <?php if ( !$isBundleChild ) : ?>
            <?php if ( $isBundle ) : ?>
                <span><?php echo wc_price( wc_get_order_item_meta( $item_id, '_yith_bundle_totals' )["line_subtotal"] / $item->get_quantity() ); ?></span>
            <?php else : ?>
                <span><?php echo wc_price( $product->get_price() ); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    <div class="account-table-item account-item-number" <?php if ( $isBundle ) echo ' data-bpid="'. $product->get_id() .'"'; ?>>
    	<?php
    	if ( date('Y-m-d', strtotime($days)) < date('Y-m-d', strtotime( get_post_meta($order->get_id(), 'cone_billing_order_date', true) )) ) {
            if ( $isBundleChild ) {
                echo '<span>'.$item->get_quantity().'</span>';
            }else{
                woocommerce_quantity_input( array( 'input_name' => 'cone_quantity', 'disabled' => 'disabled', 'input_value' => $item->get_quantity(), 'min_value' => 1, 'max_value' => $product->backorders_allowed() ? '' : $product->get_stock_quantity() ), $product );
            }

    	}else{
    		woocommerce_quantity_input( array( 'input_name' => 'cone_quantity', 'disabled' => 'disabled', 'input_value' => $item->get_quantity(), 'min_value' => $item->get_quantity(), 'max_value' => $product->backorders_allowed() ? '' : $product->get_stock_quantity() ) );
    	}
    	?>
    </div>
    <div class="account-table-item account-item-total" data-total="<?php echo $item->get_quantity() * $product->get_price(); ?>">
        <?php if ( $isBundle ) : ?>
            <h6><?php echo wc_price( wc_get_order_item_meta( $item_id, '_yith_bundle_totals' )["line_subtotal"] ); ?></h6>
        <?php else : ?>
            <h6><?php echo $order->get_formatted_line_subtotal( $item ); ?></h6>
        <?php endif; ?>
    </div>
    <div class="account-table-item account-item-remove cone-hidden">
    	<?php //if (  ) : ?>
    		<i class="cone-remove-product material-icons <?php if ( $isBundleChild || date('Y-m-d', strtotime($days)) >= date('Y-m-d', strtotime( get_post_meta($order->get_id(), 'cone_billing_order_date', true) )) ) echo 'cone-display-none'; ?>" data-id="<?php echo $item_id; ?>" <?php if ( $isBundle ) echo  'data-bundle="'.$bundle_count.'"'; ?>>remove_circle_outline</i>
    	<?php //endif; ?>
    </div>
</div>

<?php if ( $show_purchase_note && $purchase_note ) : ?>

<tr class="woocommerce-table__product-purchase-note product-purchase-note">

	<td colspan="3"><?php echo wpautop( do_shortcode( wp_kses_post( $purchase_note ) ) ); ?></td>

</tr>

<?php endif; ?>
