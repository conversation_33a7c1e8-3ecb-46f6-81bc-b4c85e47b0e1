<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/archive-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     8.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

get_header( 'shop' );

$term = get_queried_object();

$children = get_terms( $term->taxonomy, array(
    'parent'    => $term->term_id,
    'hide_empty' => false
) );

?>
<section class="shop-section">
    <div class="max-width">
        <?php woocommerce_breadcrumb(); ?>
    </div>
    <div class="woocommerce-products-header">
        <div class="max-width">
            <?php if ( apply_filters( 'woocommerce_show_page_title', true ) ) : ?>

				<h1 class="woocommerce-products-header__title page-title">
                    <?php
						$current_cat = strtolower( woocommerce_page_title(false) );
                        $parent_cat = strtolower( get_term( get_queried_object()->parent )->name );
                        $grand_parent_cat = strtolower( get_term( get_term( get_queried_object()->parent )->parent )->name );
						if ($children) {
                        ?>
                        <span><?php if ( $current_cat != 'engångsartiklar' && $parent_cat != 'engångsartiklar' && $grand_parent_cat != 'engångsartiklar' ) echo 'Hyr '; ?></span>
                        <?php
                        woocommerce_page_title();
                    }else{
                        ?>
                        <span> <?php if ( $current_cat != 'engångsartiklar' && $parent_cat != 'engångsartiklar' && $grand_parent_cat != 'engångsartiklar' ) echo 'Hyr '; ?></span>
                        <?php
                        echo get_term( get_queried_object()->parent )->name;
                    }?>
                </h1>

            <?php endif; ?>

            <?php
            /**
             * woocommerce_archive_description hook.
             *
             * @hooked woocommerce_taxonomy_archive_description - 10
             * @hooked woocommerce_product_archive_description - 10
             */
            do_action( 'woocommerce_archive_description' );


            if ($children) {
                $category_id = $term->term_id;
            }else{
                $category_id = $term->parent;
            }
            $args = array(
                'hierarchical' => 1,
                'show_option_none' => '',
                'hide_empty' => 0,
                'parent' => $category_id,
                'taxonomy' => 'product_cat'
            );
            $subcats = get_categories($args);
            ?>
            <div class="shop-cat-wrapper">
                <div class="shop-cat-section">
                    <div class="swiper-container shop-cat-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($subcats as $key => $subcat) :
                                $thumbnail_id = get_term_meta( $subcat->term_id, 'thumbnail_id', true );
                                $image = wp_get_attachment_url( $thumbnail_id );
                                $s_children = get_terms( $subcat->taxonomy, array(
                                    'parent'    => $subcat->term_id,
                                    'hide_empty' => false
                                ) );
                                ?>
                                <div class="swiper-slide top-category <?php if ( $subcat->term_id == $term->term_id ) echo 'swiper-slide-active top-category-active'; ?>">
                                    <a href="<?php echo get_term_link( $subcat->term_id ); ?>" data-id="<?php echo $subcat->term_id; ?>" class="absolute-link <?php if ( ! $s_children ) echo 'sno-children'; ?>"></a>
                                    <div class="top-category-img-wrapper">
                                        <img src="<?php echo $image; ?>">
                                    </div>
                                    <p><?php echo $subcat->name; ?></p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="top-cat-next">
                    <i class="material-icons nav-right-cat">keyboard_arrow_right</i>
                </div>
                <div class="top-cat-prev">
                    <i class="material-icons nav-right-cat">keyboard_arrow_left</i>
                </div>
            </div>
        </div>

    </div>
    <div class="max-width">
        <div class="shop-category-text">
            <div class="cd-shop-category-img">
                <?php
                $term_id = get_queried_object()->term_id;
                $post_id = 'product_cat_'.$term_id;
                $custom_field_img = get_field('category-extra-img', $post_id); // My Advanced Custom Field Variable
                ?>
                <img src="<?php echo $custom_field_img; ?>" />
                <div>
                    <h1><?php if ( single_cat_title('', false) ) { single_cat_title(); }else { echo 'Sortiment'; } ?></h1>
                    <div class="shop-category-text-p"><?php echo category_description(); ?></div>
                </div>
            </div>
      
            <div class="shop-grid-filter">
                <?php //wc_get_template( 'loop/result-count.php' ); ?>
                    <?php
                    /**
                     * woocommerce_before_shop_loop hook.
                     *
                     * @hooked wc_print_notices - 10
                     * @hooked woocommerce_result_count - 20
                     * @hooked woocommerce_catalog_ordering - 30
                     */
                    do_action( 'woocommerce_before_shop_loop' );
                    ?>

            </div>
        </div>
        <?php
        /**
         * woocommerce_before_main_content hook.
         *
         * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
         * @hooked woocommerce_breadcrumb - 20
         * @hooked WC_Structured_Data::generate_website_data() - 30
         */
        do_action( 'woocommerce_before_main_content' );
        ?>
        <?php if ( have_posts() ) : ?>
            <div class="the-shop">
                <div class="shop-sidebar">

                <?php
                $taxonomy     = 'product_cat';
                $orderby      = 'name';
                $show_count   = 0;      // 1 for yes, 0 for no
                $pad_counts   = 0;      // 1 for yes, 0 for no
                $hierarchical = 1;      // 1 for yes, 0 for no
                $title        = '';
                $empty        = 0;

                $args = array(
                    'taxonomy'     => $taxonomy,
                    // 'orderby'      => $orderby,
                    'show_count'   => $show_count,
                    'pad_counts'   => $pad_counts,
                    'hierarchical' => $hierarchical,
                    'title_li'     => $title,
                    'hide_empty'   => $empty,
                    //'parent'       => 0,
                );

                //$all_categories = get_categories( $args );

                $terms = get_categories( $args );
                $sorted_menu_cats = array();
                $mjau = sort_terms_hierarchically( $terms, $sorted_menu_cats );

                $parents = get_ancestors( get_queried_object()->term_id, 'product_cat'); ?>
                
                <?php 
                foreach ($sorted_menu_cats as $key1 => $menu_cat) : 
                if ($menu_cat->slug === 'uncategorized' || $menu_cat->slug === 'okategoriserad') {
                    continue;
                }
                ?>
                    <div class="shop-top-cats <?php if($menu_cat->slug == get_queried_object()->slug || in_array($menu_cat->term_id, $parents) ) echo 'shop-top-cats-active' ; ?>">
                        <a class="shop-top-cat" href="<?php echo get_term_link($menu_cat->slug, 'product_cat'); ?>"><?php echo $menu_cat->name; ?><?php if ( ! empty($menu_cat->children) ) : ?><i class="material-icons shop-first-i">keyboard_arrow_right</i><?php endif ; ?></a>
                        <?php foreach ($menu_cat->children as $key2 => $menu_subcat) : ?>
                            <div class="shop-second-cats <?php if($menu_subcat->slug == get_queried_object()->slug || in_array($menu_subcat->term_id, $parents)) echo 'shop-second-cat-active' ; ?>">
                                <?php if ( get_field('category-alias', 'product_cat_'.$menu_subcat->term_id) ) : ?>
                                    <a class="shop-second-cat-a" href="<?php echo get_term_link(get_field('copy-cat', 'product_cat_'.$menu_subcat->term_id)[0]->slug, 'product_cat'); ?>"><?php echo $menu_subcat->name; ?><?php if ( ! empty($menu_subcat->children) ) : ?><i class="material-icons shop-second-i">keyboard_arrow_right</i><?php endif ; ?></a>
                                <?php else : ?>
                                    <a class="shop-second-cat-a" href="<?php echo get_term_link($menu_subcat->slug, 'product_cat'); ?>"><?php echo $menu_subcat->name; ?><?php if ( ! empty($menu_subcat->children) ) : ?><i class="material-icons shop-second-i">keyboard_arrow_right</i><?php endif ; ?></a>
                                <?php endif; ?>
                                <?php foreach ($menu_subcat->children as $key3 => $menu_subsub) : ?>
                                    <div class="shop-third-cats <?php if($menu_subsub->slug == get_queried_object()->slug || in_array($menu_subsub->term_id, $parents ) ) echo 'shop-third-cat-active' ; ?>">
                                        <?php if ( get_field('category-alias', 'product_cat_'.$menu_subsub->term_id) ) : ?>
                                            <a class="shop-third-cat" href="<?php echo get_term_link(get_field('copy-cat', 'product_cat_'.$menu_subsub->term_id)[0]->slug, 'product_cat'); ?>"><?php echo $menu_subsub->name; ?><?php if ( ! empty($menu_subsub->children) ) : ?><i class="material-icons shop-third-i">keyboard_arrow_right</i><?php endif ; ?></a>
                                        <?php else : ?>
                                            <a class="shop-third-cat" href="<?php echo get_term_link($menu_subsub->slug, 'product_cat'); ?>"><?php echo $menu_subsub->name; ?><?php if ( ! empty($menu_subsub->children) ) : ?><i class="material-icons shop-third-i">keyboard_arrow_right</i><?php endif ; ?></a>
                                        <?php endif; ?>
                                        <div class="shop-fourth-cats">
                                            <?php foreach ($menu_subsub->children as $key3 => $menu_inception) : ?>
                                                <a class="shop-fourth-cat <?php if($menu_inception->slug == get_queried_object()->slug || in_array($menu_inception->term_id, $parents ) ) echo 'shop-fourth-cat-active' ; ?>" href="<?php echo get_term_link($menu_inception->slug, 'product_cat'); ?>"><?php echo $menu_inception->name; ?></a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>

                </div>
                <div class="shop-grid">
                    <div class="shop-categories">
                        <?php $globalChildren = true; ?>
                        <?php foreach ($subcats as $key => $subcat) :
                            if ($subcat->slug === 'uncategorized' || $subcat->slug === 'okategoriserad') {
                                continue;
                            }
                            $thumbnail_id = get_term_meta( $subcat->term_id, 'thumbnail_id', true );
                            $image = wp_get_attachment_url( $thumbnail_id );
                            // $children = get_terms( $subcat->taxonomy, array(
                            //     'parent'    => $subcat->term_id,
                            //     'hide_empty' => false
                            // ) );
                            ?>
                            <?php if ( $children ) : ?>
                                <?php $url = ( get_field('category-alias', 'product_cat_'.$subcat->term_id) ) ? get_term_link( get_field('copy-cat', 'product_cat_'.$subcat->term_id)[0]->slug, 'product_cat') : get_term_link( $subcat->term_id ); ?>
                                <div class="shop-category">
                                    <a href="<?php echo $url; ?>" data-id="<?php echo $subcat->term_id; ?>" class="absolute-link"></a>
                                    <img src="<?php echo $image; ?>">
                                    <p><?php echo $subcat->name; ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                    <?php if (! $children ) : ?>

                        <?php woocommerce_product_loop_start(); ?>

                        <?php while ( have_posts() ) : the_post(); ?>

                            <?php
                            /**
                             * woocommerce_shop_loop hook.
                             *
                             * @hooked WC_Structured_Data::generate_product_data() - 10
                             */
                            do_action( 'woocommerce_shop_loop' );
                            ?>

                            <?php wc_get_template_part( 'content', 'product' ); ?>

                        <?php endwhile; // end of the loop. ?>

                        <?php woocommerce_product_loop_end(); ?>

                    

                        <?php
                        /**
                         * woocommerce_after_shop_loop hook.
                         *
                         * @hooked woocommerce_pagination - 10
                         */
                        do_action( 'woocommerce_after_shop_loop' );
                        ?>
                    <?php endif; ?>
                    <div class="shop-category-seo-text">
                        <?php

                        $term_id = get_queried_object()->term_id;
                        $post_id = 'product_cat_'.$term_id;
                        $custom_field = get_field('category-text-shop', $post_id); // My Advanced Custom Field Variable

                        echo $custom_field;
                        ?>
                    </div>
                </div>
            </div>

        <?php elseif ( ! woocommerce_product_subcategories( array( 'before' => woocommerce_product_loop_start( false ), 'after' => woocommerce_product_loop_end( false ) ) ) ) : ?>

            <?php
            /**
             * woocommerce_no_products_found hook.
             *
             * @hooked wc_no_products_found - 10
             */
            do_action( 'woocommerce_no_products_found' );
            ?>

        <?php endif; ?>

        <?php
        /**
         * woocommerce_after_main_content hook.
         *
         * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
         */
        do_action( 'woocommerce_after_main_content' );
        ?>
    </div>
</section>

<?php get_footer( 'shop' ); ?>
