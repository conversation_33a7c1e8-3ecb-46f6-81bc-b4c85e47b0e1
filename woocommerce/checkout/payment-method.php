<?php
/**
 * Output a single payment method
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/payment-method.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     3.5.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Check if any payment method is currently chosen
$any_chosen = false;
$cod_chosen = false;
$cheque_chosen = false;

// Get all available gateways to check which one is chosen
$available_gateways = WC()->payment_gateways->get_available_payment_gateways();
foreach ($available_gateways as $gateway_id => $gateway_obj) {
    if ($gateway_obj->chosen) {
        $any_chosen = true;
        if ($gateway_id === 'cod') $cod_chosen = true;
        if ($gateway_id === 'cheque') $cheque_chosen = true;
    }
}
?>
<div class="type-check-content cd-payment-method <?php echo esc_attr( $gateway->id ); ?> <?php if( $gateway->chosen == 1) echo 'type-check-active'; ?>">
    <span><?php echo $gateway->get_title(); ?></span>
    <input id="payment_method_<?php echo $gateway->id; ?>" type="radio" style="display: none;" class="input-radio" name="payment_method" value="<?php echo esc_attr( $gateway->id ); ?>" <?php checked( $gateway->chosen, true ); ?> data-order_button_text="<?php echo esc_attr( $gateway->order_button_text ); ?>" />
</div>
<?php if ( esc_attr( $gateway->id ) == 'cod') : ?>
	<div class="cash-text" style="<?php echo ($cod_chosen || !$any_chosen) ? '' : 'display: none;'; ?>">
		<?php echo get_field('cod_text', 'option'); ?>
	</div>
<?php endif; ?>
<?php if ( esc_attr( $gateway->id ) == 'cheque') : ?>	
	<div class="cheque-text" style="<?php echo $cheque_chosen ? '' : 'display: none;'; ?>">
		<div style="margin-top: 15px;">
			<?php echo get_field('cheque_text', 'option'); ?>
			<p class="form-row">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input type="checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" name="invoice_email" id="invoice_email" value="1"> <span><?php _e( 'Önskar mottaga fakturan via e-post', 'woocommerce' ); ?></span>
				</label>
			</p>
			<div class="checkout-input input-invoice-email-address" style="display: none; margin-bottom: 0;">
				<input type="email" name="invoice_email_address" id="invoice_email_address" placeholder="E-postadress">
			</div>
		</div>
	</div>
<?php endif; ?>
