<?php
/**
 * Checkout Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     2.3.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

wc_print_notices();

//do_action( 'woocommerce_before_checkout_form', $checkout );

// If checkout registration is disabled and not logged in, the user cannot checkout
if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() ) {
	echo apply_filters( 'woocommerce_checkout_must_be_logged_in_message', __( 'You must be logged in to checkout.', 'woocommerce' ) );
	return;
}

//$fields = $checkout->get_checkout_fields( 'billing' );

//var_dump($fields);

// foreach ( $fields as $key => $field ) {
// 	if ( isset( $field['country_field'], $fields[ $field['country_field'] ] ) ) {
// 		$field['country'] = $checkout->get_value( $field['country_field'] );
// 	}
// 	woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
// }

?>

<div class="checkout-header">
    <div class="max-width checkout-header-content">
        <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/aladdins-uthyrning.jpg' ) ); ?>">
        <div class="checkout-header-stages">
            <div class="checkout-header-stage active-checkout-stage checkout-step-1">
                <div class="checkout-header-number">1</div>
                <span>Faktura<br/>adress</span>
            </div>
            <div class="checkout-header-stage  checkout-step-2">
                <div class="checkout-header-number ">2</div>
                <span>Leverans<br/>metod</span>
            </div>
            <div class="checkout-header-stage  checkout-step-3">
                <div class="checkout-header-number">3</div>
                <span>Order<br/>bekräftelse</span>
            </div>
        </div>
        <h5>Trygga leveranser sedan 1986</h5>
    </div>
</div>
<section class="checkout-wrapper">
    <div class="checkout-section max-width">
        <div class="checkout-left">
            <form id="checkout-form" name="checkout1" method="post" action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">
        	<!--FIRST STEP -->
        	
                <div id="step1">
            	    <h2>Faktura adress</h2>
            	    <div class="type-check type-of-service">
            	        <div class="type-check-content" data-name="privat">
            	            <span>Privatperson</span>
            	        </div>
            	        <div class="type-check-content" data-name="company">
            	            <span>Företag</span>
            	        </div>
            	        <div class="type-check-content" data-name="catering">
            	            <span>Catering</span>
            	        </div>
            	    </div>
            	    <div id="billing-form-info" style="display: none;">
            	        <div class="two-inputs">
            	            <div class="checkout-input">
            	                <label for="billing-first-name">Förnamn*</label>
            	                <input type="text" class="cone-required" name="billing_first_name" id="billing-first-name" value="<?php echo $checkout->get_value( 'billing_first_name' ); ?>" />
            	            </div>
            	            <div class="checkout-input">
            	                <label for="billing-last-name">Efternamn*</label>
            	                <input type="text" class="cone-required" name="billing_last_name" id="billing-last-name" value="<?php echo $checkout->get_value( 'billing_last_name' ); ?>" />
            	            </div>
            	        </div>
            	        <div class="two-inputs company-inputs">
            	            <div class="checkout-input">
            	                <label for="billing-company">Företagsnamn*</label>
            	                <input type="text" class="cone-required" name="billing_company" id="billing-company" value="<?php echo $checkout->get_value( 'billing_company' ); ?>" />
            	            </div>
            	            <div class="checkout-input">
            	            	<label for="companynumber">Organisationsnummer*</label>
            	            	<input type="text" class="cone-required" name="companynumber" id="companynumber" />
            	            </div>
            	        </div>
            	        <div class="checkout-input">
            	            <label for="billing-address-1">Gatuadress*</label>
            	            <input type="text" class="cone-required" name="billing_address_1" id="billing-address-1" value="<?php echo $checkout->get_value( 'billing_address_1' ); ?>" />
            	        </div>
            	        <div class="checkout-input">
            	            <label for="billing-address-2">Boxadress (Viktigt om det finns)</label>
            	            <input type="text" name="billing_address_2" id="billing-address-2" value="<?php echo $checkout->get_value( 'billing_address_2' ); ?>" />
            	        </div>
            	        <div class="two-inputs">
            	            <div class="checkout-input">
            	                <label for="billing-postcode">Postnummer*</label>
            	                <input type="text" class="cone-required" name="billing_postcode" id="billing-postcode" value="<?php echo $checkout->get_value( 'billing_postcode' ); ?>" />
            	            </div>
            	            <div class="checkout-input">
            	                <label for="billing-city">Postadress*</label>
            	                <input type="text" class="cone-required" name="billing_city" id="billing-city" value="<?php echo $checkout->get_value( 'billing_city' ); ?>" />
            	            </div>
            	        </div>
            	        <div class="checkout-input">
            	            <label for="billing-phone">Telefonnummer*</label>
            	            <input type="text" class="cone-required" name="billing_phone" id="billing-phone" value="<?php echo $checkout->get_value( 'billing_phone' ); ?>" />
            	        </div>
            	        <div class="checkout-input checkout-checkbox">
            	            <h4>Aladdins nyhetsbrev</h4>
            	            <label><input type="checkbox" name="checkout_prem" value="prem">Jag vill gärna få aladdins nyhetsbrev med tips till festen, erbjudanden m.m</label>
            	        </div>
            	         <div class="checkout-continue checkout-continue-right checkout-continue-first">
            	           <a href="#">Fortsätt</a>
            	        </div>
            	    </div>
            	</div>

                <!-- Second step -->
    	        <div id="step2" style="display: none;">
                    <!-- Delivery date seciton -->
    	            <h2>Leverans</h2>
                    <div class="checkout-input checkout-content">
                        <label for="c-l-name">Hyrdatum</label>
                        <input name="pick_date" id="pick-date" />
                    </div>
                    <!-- Pick up or get delivered section -->
                    <div class="checkout-input checkout-content checkout-receiving checkout-pickup-return" style="display: none;">
                        <label>Mottagande</label>
                        <div class="checkout-pick-section">
                            <div class="checkout-pick" data-type="customer">
                                <i class="fa fa-home" aria-hidden="true"></i>
                                <div class="checkout-pick-content">
                                    <h4>Hämta upp i butik</h4>
                                    <p>Välj en tid i nästa steg när du vill hämta dina produkter</p>
                                    <span>Gratis</span>
                                </div>
                            </div>
                            <div class="checkout-pick" data-type="aladdin">
                                <i class="fa fa-truck" aria-hidden="true"></i>
                                <div class="checkout-pick-content">
                                    <h4>Aladdin levererar</h4>
                                    <p>Välj en tid i nästa steg när du vill att dina produkter ska lämnas</p>
                                    <span>Från 599 sek</span>
                                </div>
                            </div>
                        </div>
                        <div class="checkout-type-check delivery-time-customer" style="display: none;">
                            <label>Välj tid*</label>
                            <div class="type-check checkout-time-section delivery-time">
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 17:00</span>
                                </div>
                            </div>
                        </div>
                        <div class="checkout-type-check delivery-time-aladdin" style="display: none;">
                            <label>Välj tid*</label>
                            <div class="type-check checkout-time-section delivery-time">
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 11:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 10:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 17:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 15:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 15:00 - 17:00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Return, aladdin returns section -->
                    <div class="checkout-input checkout-content checkout-content-border checkout-return checkout-pickup-return" style="display: none;">
                        <label>Retur</label>
                        <div class="checkout-pick-section">
                            <div class="checkout-pick" data-type="customer">
                                <i class="fa fa-home" aria-hidden="true"></i>
                                <div class="checkout-pick-content">
                                    <h4>Leverera tillbaka själv</h4>
                                    <p>Välj en tid i nästa steg när du vill lämna dina produkter</p>
                                    <span>Gratis</span>
                                </div>
                            </div>
                            <div class="checkout-pick" data-type="aladdin">
                                <i class="fa fa-truck" aria-hidden="true"></i>
                                <div class="checkout-pick-content">
                                    <h4>Aladdin hämtar</h4>
                                    <p>Välj en tid i nästa steg när du vill att dina produkter ska hämtas</p>
                                    <span>Från 599 sek</span>
                                </div>
                            </div>
                        </div>
                        <div class="checkout-type-check return-time-customer pickup-return-time" style="display: none;">
                            <label>Välj tid*</label>
                            <div class="type-check checkout-time-section return-time">
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 17:00</span>
                                </div>
                            </div>
                        </div>
                        <div class="checkout-type-check return-time-aladdin pickup-return-time" style="display: none;">
                            <label>Välj tid*</label>
                            <div class="type-check checkout-time-section return-time">
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 09:00 - 11:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 10:00 - 12:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 17:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 13:00 - 15:00</span>
                                </div>
                                <div class="type-check-content">
                                    <span>kl. 15:00 - 17:00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping and special fields -->
                    <div class="checkout-input checkout-content last-checkout-content checkout-delivery" style="display: none;">
                        <label style="font-size: 18px; font-weight: normal;">Uppgifter för leverans</label>
                        <div class="type-check different-from-billing">
                            <div class="type-check-content">
                                <span>Annan än Faktura adress</span>
                            </div>
                        </div>
                        <!-- JS gets billing info -->
                        <div class="same-as-billing">

                        	
                        </div>

                        <div name="shipping-form" id="shipping-form" style="display: none;">
    	                    <div class="checkout-input">
    	                        <label for="shipping-first-name">Förnamn*</label>
    	                        <input type="text" class="cone-required" name="shipping_first_name" id="shipping-first-name" value="" />
    	                    </div>
    	                    <div class="checkout-input">
    	                        <label for="shipping-last-name">Efternamn*</label>
    	                        <input type="text" class="cone-required" name="shipping_last_name" id="shipping-last-name" value="" />
    	                    </div>
    	                    <div class="two-inputs company-inputs">
    	                        <div class="checkout-input">
    	                            <label for="shipping-company">Företagsnamn*</label>
    	                            <input type="text" class="cone-required" name="shipping_company" id="shipping-company" value="" />
    	                        </div>
    	                        <div class="checkout-input">
    		                        <label for="companynumber">Organisationsnummer*</label>
    		                        <input type="text" class="cone-required" name="shipping_companynumber" id="shipping-companynumber" />
    	                        </div>
    	                    </div>
    	                    <div class="checkout-input">
    	                        <label for="billing-address-1">Gatuadress*</label>
    	                        <input type="text" class="cone-required" name="shipping_address_1" id="shipping-address-1" value="" />
    	                    </div>
    	                    <div class="checkout-input">
    	                        <label for="shipping-address-2">Boxadress (Viktigt om det finns)</label>
    	                        <input type="text" name="shipping_address_2" id="shipping-address-2" value="" />
    	                    </div>
    	                    <div class="two-inputs">
    	                        <div class="checkout-input">
    	                            <label for="shipping-postcode">Postnummer*</label>
    	                            <input type="text" class="cone-required" name="shipping_postcode" id="shipping-postcode" value="" />
    	                        </div>
    	                        <div class="checkout-input">
    	                            <label for="shipping-city">Postadress*</label>
    	                            <input type="text" class="cone-required" name="shipping_city" id="shipping-city" value="" />
    	                        </div>
    	                    </div>
    	                    <div class="checkout-input">
    	                        <label for="shipping-phone">Telefonnummer*</label>
    	                        <input type="text" class="cone-required" name="shipping_phone" id="shipping-phone" value="" />
    	                    </div>
                        </div>
                        <div id="aladdin-special-form" class="aladdin-delivery-special" style="display: none;">
                            <div class="">
                            	<div class="checkout-type-check">
                            	    <label>Typ av lokal*</label>
                            	    <div class="type-check">
                            	        <div class="type-check-content">
                            	            <span>Villa</span>
                            	        </div>
                            	        <div class="type-check-content">
                            	            <span>Flerfamiljshus</span>
                            	        </div>
                            	        <div class="type-check-content">
                            	            <span>Lokal</span>
                            	        </div>
                            	        <div class="type-check-content">
                            	            <span>Annat</span>
                            	        </div>
                            	    </div>
                            	    <div class="checkout-input other-facility" style="display: none;">
                            	        <label for="special-facility">Specificera "Annat"*</label>
                            	        <input type="text" class="cone-required" name="special_facility" id="special-facility" />
                            	    </div>
                            	</div>
                            	<div class="two-inputs">
        		                    <div class="checkout-input">
        		                        <label for="special-city">Stadsdel*</label>
        		                        <input type="text" class="cone-required" name="special_city" id="special-city" />
        		                    </div>
        		                    <div class="checkout-input">
        		                        <label for="special-floor">Våningsplan*</label>
        		                        <input type="text" class="cone-required" name="special_floor" id="special-floor" />
        		                    </div>
        	                    </div>
        	                    <div class="two-inputs">
        	                        <div class="checkout-input">
        	                            <label for="special-doorcode">Portkod*</label>
        	                            <input type="text" class="cone-required" name="special_doorcode" id="special-doorcode" />
        	                        </div>
        	                        <div class="checkout-input">
        		                        <label for="special-steps">Antal trappsteg före hiss/entré*</label>
        		                        <input type="text" class="cone-required" name="special_steps" id="special-steps" placeholder="Antal trappsteg till närmsta användbara hiss." />
        	                        </div>
        	                    </div>
        	                    <div class="two-inputs">
        		                    <div class="checkout-input">
        		                        <label for="special-elevatorsize">Storlek på ev. hiss*</label>
        		                        <input type="text" class="cone-required" name="special_elevatorsize" id="special-elevatorsize" placeholder="Ange antal personer som rymms i eventuell hiss." />
        		                    </div>
        		                    <div class="checkout-input">
        		                        <label for="special-carry">Bärsträcka</label>
        		                        <input type="text" name="special_carry" id="special-carry" placeholder="Fastighet eller tomt över 10 meter. Ange i meter." />
        		                    </div>
        	                    </div>
        	                    <div class="two-inputs">
        	                        <div class="checkout-input">
        	                            <label for="special-othercontact">Ev. annan kontaktperson på leveransplats</label>
        	                            <input type="text" class="cone-required" name="special_othercontact" id="special-othercontact" />
        	                        </div>
        	                        <div class="checkout-input">
        	                            <label for="special-contactphone">Kontaktpersonens mobilnr</label>
        	                            <input type="text" class="cone-required" name="special_contactphone" id="special-contactphone" />
        	                        </div>
        	                    </div>
                            </div>
                        </div>
                    </div>
                    <div class="" style="display: none;">
                        <input type="submit" class="button alt" name="" id="place_order" value="Fortsätt/Beställ" data-value="Place order" />
                        <?php wp_nonce_field( 'woocommerce-process_checkout' ); ?>
                    </div>
                </div>
            </form>
        </div>
        <div class="checkout-sidebar">
            <div class="checkout-sidebar-headline">
                <i class="material-icons checkout-sidebar-toggle">close</i>
                <h4>Ordersummering</h4>
                <a href="#">Redigera</a>
            </div>
            <div class="checkout-sidebar-show">
                <div class="checkout-sidebar-products">
                    <div class="checkout-sidebar-product">
                        <img src="http://www.aladdinsuthyrning.se/sites/default/files/imagecache/product_full/product-images/m1170.jpg">
                        <div class="checkout-sidebar-content">
                            <p>Eldkorg - större, i smide för dekorativ vedbrasa utomhus</p>
                            <div class="checkout-sidebar-product-info">
                                <span>antal: 25</span>
                                <h6>242.51 kr</h6>
                            </div>
                        </div>
                    </div>
                    <div class="checkout-sidebar-product">
                        <img src="http://www.aladdinsuthyrning.se/sites/default/files/imagecache/product_full/product-images/m1170.jpg">
                        <div class="checkout-sidebar-content">
                            <p>Parasoll 3 meters diameter - vit</p>
                            <div class="checkout-sidebar-product-info">
                                <span>antal: 25</span>
                                <h6>242.51 kr</h6>
                            </div>
                        </div>
                    </div>
                    <div class="checkout-sidebar-product">
                        <img src="http://www.aladdinsuthyrning.se/sites/default/files/imagecache/product_full/product-images/m1170.jpg">
                        <div class="checkout-sidebar-content">
                            <p>Parasoll 3 meters diameter - vit</p>
                            <div class="checkout-sidebar-product-info">
                                <span>antal: 25</span>
                                <h6>242.51 kr</h6>
                            </div>
                        </div>
                    </div>
                    <div class="checkout-sidebar-product">
                        <img src="http://www.aladdinsuthyrning.se/sites/default/files/imagecache/product_full/product-images/m1170.jpg">
                        <div class="checkout-sidebar-content">
                            <p>Parasoll 3 meters diameter - vit</p>
                            <div class="checkout-sidebar-product-info">
                                <span>antal: 25</span>
                                <h6>242.51 kr</h6>
                            </div>
                        </div>
                    </div>
                    <div class="checkout-sidebar-product">
                        <img src="http://www.aladdinsuthyrning.se/sites/default/files/imagecache/product_full/product-images/m1170.jpg">
                        <div class="checkout-sidebar-content">
                            <p>Parasoll 3 meters diameter - vit</p>
                            <div class="checkout-sidebar-product-info">
                                <span>antal: 25</span>
                                <h6>242.51 kr</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="checkout-sidebar-info">
                    <div>
                        <strong>Subtotalt:</strong>
                        <strong>460 kr</strong>
                    </div>
                    <div>
                        <p>Uppskattad fraktkostnad:</p>
                        <p>...</p>
                    </div>
                    <div>
                        <p>Moms:</p>
                        <p>40 kr</p>
                    </div>
                    <div>
                        <strong>Totalt:</strong>
                        <strong>460 kr</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php get_footer() ; ?>
