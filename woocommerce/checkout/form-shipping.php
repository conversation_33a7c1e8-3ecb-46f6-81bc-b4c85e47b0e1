<?php
/**
 * Checkout shipping information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-shipping.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 3.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
} ?>
<h2>Leverans</h2>
<div>
    <div class="two-inputs">
        <div class="checkout-input checkout-content checkout-content-first" >
            <label for="c-l-name">Hyrdatum</label>
            <input name="pick_date" id="pick-date" placeholder="Välj hyrdatum här" autocomplete="off" data-value='<?php echo get_option( 'cone_blocked_dates' ); ?>'/>
        </div>
        <div class="checkout-input checkout-content checkout-content-first" >
            <label for="c-l-name">Returdatum</label>
            <input placeholder="Välj returdatum här" autocomplete="off" name="return_date" id="return-date" />
        </div>
    </div>
    <p class="checkout-content-explain"><?php echo get_field('info-about-delivery', 'option') ; ?> <span class="delivery-private-text" style="display: none;">Vi återkommer på orderbekräftelsen med transportpris baserat på den info du lämnar om leveransplats, transport kan avbokas senast dagen innan leverans.<span></p>
</div>

<!-- Pick up or get delivered section -->
<div class="checkout-input checkout-content checkout-receiving checkout-pickup-return" style="display: none;">
    <label>Mottagande</label>
    <div class="checkout-pick-section">
        <div class="checkout-pick" data-type="customer">
            <i class="fa fa-home" aria-hidden="true"></i>
            <div class="checkout-pick-content">
                <h4>Hämta upp i butik</h4>
                <p>Välj en tid i nästa steg när du vill hämta dina produkter</p>
                <span></span>
            </div>
        </div>
        <div class="checkout-pick" data-type="aladdin">
            <i class="fa fa-truck" aria-hidden="true"></i>
            <div class="checkout-pick-content">
                <h4>Aladdin levererar</h4>
                <p>Välj en tid i nästa steg när du vill att dina produkter ska lämnas</p>
                <span></span>
            </div>
        </div>
    </div>
    <div class="checkout-type-check delivery-time-customer" style="display: none;">
        <label>Välj tid*</label>
        <div class="type-check checkout-time-section delivery-time">
            <?php if( have_rows('customer-pick-up', 'options') ): ?>
                <?php while( have_rows('customer-pick-up', 'options') ) : the_row(); ?>

                    <div class="type-check-content">
                        kl. <span><?php the_sub_field('customer-pick-up-time', 'options') ; ?></span>
                    </div>
                <?php endwhile; ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="checkout-type-check delivery-time-aladdin" style="display: none;">
        <label>Välj tid*</label>
        <p><strong><?php echo get_field('choose-time-info', 'option'); ?></strong></p>
        <div class="type-check checkout-time-section delivery-time">
            <?php if( have_rows('aladdin-deliver', 'options') ): ?>
                <?php while( have_rows('aladdin-deliver', 'options') ) : the_row(); ?>
                    <?php
                        $time = get_sub_field('aladdin-deliver-time', 'options');
                        $start = intval(trim(explode(':',(explode('-', $time)[0]))[0]));
                        $end = intval(trim(explode(':',(explode('-', $time)[1]))[0]));
                        $extra_class = (($end - $start) < 3) ? 'extra-charge' : '';

                    ?>
                    <div class="type-check-content <?php echo ($extra_class); ?>">
                        kl. <span><?php the_sub_field('aladdin-deliver-time', 'options') ; ?></span>
                    </div>

                <?php endwhile; ?>
            <?php endif; ?>
            <div class="extra-charge-text" style="display: none;">
                <p><strong><?php echo get_field('transport-fee', 'option'); ?></strong></p>
            </div>
        </div>
        <div class="type-check">
            <input type="checkbox" id="call-before-delivery-input" name="call_before_delivery" class="call-before-input">
            <label>Vill du att vi ringer dig ca 30 min innan leverans?</label>
        </div>
    </div>
</div>

<div class="checkout-input checkout-content checkout-content-border checkout-return checkout-pickup-return" style="display: none;">
    <label>Retur</label>
    <div class="checkout-pick-section">
        <div class="checkout-pick" data-type="customer">
            <i class="fa fa-home" aria-hidden="true"></i>
            <div class="checkout-pick-content">
                <h4>Leverera tillbaka själv</h4>
                <p>Välj en tid i nästa steg när du vill lämna dina produkter</p>
                <span></span>
            </div>
        </div>
        <div class="checkout-pick" data-type="aladdin">
            <i class="fa fa-truck" aria-hidden="true"></i>
            <div class="checkout-pick-content">
                <h4>Aladdin hämtar</h4>
                <p>Välj en tid i nästa steg när du vill att dina produkter ska hämtas</p>
                <span></span>
            </div>
        </div>
    </div>
    <div class="checkout-type-check return-time-customer pickup-return-time" style="display: none;">
        <label>Välj tid*</label>
        <div class="type-check checkout-time-section return-time">
            <?php if( have_rows('customer-deliver', 'options') ): ?>
                <?php while( have_rows('customer-deliver', 'options') ) : the_row(); ?>
                    <div class="type-check-content">
                        kl. <span><?php the_sub_field('customer-deliver-time', 'options') ; ?></span>
                    </div>
                <?php endwhile; ?>
            <?php endif; ?>
        </div>
    </div>
    <div class="checkout-type-check return-time-aladdin pickup-return-time" style="display: none;">
        <label>Välj tid*</label>
        <p><strong><?php echo get_field('choose-time-info', 'option'); ?></strong></p>
        <div class="type-check checkout-time-section return-time">
            <?php if( have_rows('aladdin-pick-up', 'options') ): ?>
                <?php while( have_rows('aladdin-pick-up', 'options') ) : the_row(); ?>
                    <?php
                        $time = get_sub_field('aladdin-pick-up-time', 'options');
                        $start = intval(trim(explode(':',(explode('-', $time)[0]))[0]));
                        $end = intval(trim(explode(':',(explode('-', $time)[1]))[0]));
                        $extra_class = (($end - $start) < 3) ? 'extra-charge' : '';
                    ?>
                    <div class="type-check-content <?php echo ($extra_class); ?>">
                        kl. <span><?php the_sub_field('aladdin-pick-up-time', 'options') ; ?></span>
                    </div>
                <?php endwhile; ?>
            <?php endif; ?>
            <div class="extra-charge-text" style="display: none;">
                <p><strong><?php echo get_field('transport-fee', 'option'); ?></strong></p>
            </div>
        </div>
        <div class="type-check">
            <input type="checkbox" id="call-before-return-input" name="call_before_return" class="call-before-input">
            <label>Vill du att vi ringer dig ca 30 min innan hämtning?</label>
        </div>
    </div>
</div>

<div class="checkout-input checkout-content last-checkout-content checkout-delivery" style="display: none;">
    <label style="font-size: 18px; font-weight: normal;">Uppgifter för leverans</label>
    <div class="type-check different-from-billing">
        <div class="type-check-content other-delivery-address">
            <span>Annan än fakturaadress</span>
            <input id="ship-to-different-address-checkbox" class="" type="hidden" name="ship_to_different_address" value="1" />
        </div>
    </div>
    <!-- JS gets billing info -->
    <div class="same-as-billing">

    	
    </div>
	<?php //if ( true === WC()->cart->needs_shipping_address() ) : ?>

<!-- 		<h3 id="ship-to-different-address">
			<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
				<input id="ship-to-different-address-checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" <?php checked( apply_filters( 'woocommerce_ship_to_different_address_checked', 'shipping' === get_option( 'woocommerce_ship_to_destination' ) ? 1 : 0 ), 1 ); ?> type="checkbox" name="ship_to_different_address" value="1" /> <span><?php _e( 'Ship to a different address?', 'woocommerce' ); ?></span>
			</label>
		</h3> -->


			<?php do_action( 'woocommerce_before_checkout_shipping_form', $checkout ); ?>

			<div name="shipping-form" id="shipping-form" style="display: none;">
				<?php
					$fields = $checkout->get_checkout_fields( 'shipping' );

					foreach ( $fields as $key => $field ) {
						if ( isset( $field['country_field'], $fields[ $field['country_field'] ] ) ) {
							$field['country'] = $checkout->get_value( $field['country_field'] );
						}

						$cone_hidden = ( substr($key, 0, 4) == 'cone' ) ? true : false;

						if ( $key == 'shipping_first_name' || $key == 'shipping_company' || $key == 'shipping_postcode' ) {
						?>
							<div class="two-inputs <?php if ( $key == 'shipping_company' ) echo 'company-inputs'; ?>">
						<?php
						}
						?>

						<div class="checkout-input" <?php if ( $key == 'shipping_customer_type' ) echo 'style="display: none;"'; ?>>
						    <label for="<?php echo $key; ?>"><?php echo $field['label']; if ( $field['required'] ) echo ' *'; ?></label>
						    <input type="text" class="<?php if( $field['required'] && ! $cone_hidden ) echo 'cone-required '; if ( $cone_hidden ) echo 'cone-required-2' ?> " name="<?php echo $key; ?>" id="<?php echo $key; ?>" value="<?php echo $checkout->get_value( $key ); ?>" />
						</div>
						<?php

						if ( $key == 'shipping_last_name' || $key == 'shipping_org_nr' || $key == 'shipping_city' ) {
						?>
							</div>
						<?php
						}
						
						//woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
					}
				?>
			</div>

			<?php do_action( 'woocommerce_after_checkout_shipping_form', $checkout ); ?>

			<div id="aladdin-special-form" class="aladdin-delivery-special" style="display: none;">
				<?php do_action( 'woocommerce_before_order_notes', $checkout ); ?>

			    <div class="">

			    	<?php $even = 0; ?>
			    	<?php foreach ( $checkout->get_checkout_fields( 'order' ) as $key => $field ) : ?>

			    		<?php if ( $even == 0 ) : ?>
			    			<div class="checkout-type-check">
			    			    <label>Typ av lokal*</label>
			    			    <div class="type-check type-of-facility-block">
			    			        <div class="type-check-content">
			    			            <span>Villa</span>
			    			        </div>
			    			        <div class="type-check-content">
			    			            <span>Flerfamiljshus</span>
			    			        </div>
			    			        <div class="type-check-content">
			    			            <span>Lokal</span>
			    			        </div>
                                    <div class="type-check-content">
                                        <span>Företagshus</span>
                                    </div>
			    			        <div class="type-check-content">
			    			            <span>Annat</span>
			    			        </div>
			    			    </div>
			    			    <div class="checkout-input type-of-facility" style="display: none;">
			    			        <label for="<?php echo $key; ?>"><?php echo $field['label']; ?></label>
			    			        <input type="text" class="cone-required" name="<?php echo $key; ?>" id="<?php echo $key; ?>" />
			    			    </div>
			    		<?php endif; ?>

			    		<?php if ( $even == 1 ) : ?>
				    			<div class="checkout-input other-facility" style="display: none;">
				    			    <label for="<?php echo $key; ?>"><?php echo $field['label']; ?></label>
				    			    <input type="text" class="" name="<?php echo $key; ?>" id="<?php echo $key; ?>" />
				    			</div>
			    			</div>
			    		<?php endif; ?>

			    		<?php if ( $even % 2 == 0  && $even >= 2 && $even < 10 ) : ?>
			    			<div class="two-inputs">
			    		<?php endif; ?>


			    		<?php if ( $even != 0 && $even != 1 && $even != 10) : ?>
				    		<div class="checkout-input">
				    		    <label for="<?php echo $key; ?>"><?php echo $field['label']; ?></label>
				    		    <input type="text" class="<?php if ( $key != 'special_other_contact' && $key != 'special_other_contact_phone' ) echo'cone-required'; ?>" name="<?php echo $key; ?>" id="<?php echo $key; ?>" />
				    		</div>
			    		<?php endif; ?>

			    		<?php if ( $even % 2 != 0 && $even > 1 ) : ?>
			    			</div>
			    		<?php endif; ?>

			    		<?php $even++; ?>

			    	<?php endforeach; ?>

			    	<?php do_action( 'woocommerce_after_order_notes', $checkout ); ?>

			    </div>
			</div>

            <div class="checkout-input">
                <label for="order_comments">Ditt meddelande till Aladdin</label>
                <textarea  class="" name="order_comments" id="order_comments"></textarea>
            </div>

	<?php //endif; ?>
</div>




