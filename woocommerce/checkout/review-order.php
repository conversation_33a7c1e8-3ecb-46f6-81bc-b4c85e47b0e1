<?php
/**
 * Review order table
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/review-order.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     5.2.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>



<div class="checkout-sidebar-products">

    <?php
    	do_action( 'woocommerce_review_order_before_cart_contents' );

    	foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
    		$_product     = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );

    		if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_checkout_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
    			?>

    			<div class="checkout-sidebar-product">
    			    <img src="<?php echo wp_get_attachment_image_src( get_post_thumbnail_id( $_product->get_id() ) )[0]; ?>">
    			    <div class="checkout-sidebar-content">
    			        <p><?php echo apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) . '&nbsp;'; ?></p>
    			        <div class="checkout-sidebar-product-info">
    			            <span>antal: <?php echo $cart_item['quantity']; ?></span>
    			            <h6><?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?></h6>
    			        </div>
    			    </div>
    			</div>

    			<?php
    		}
    	}

    	do_action( 'woocommerce_review_order_after_cart_contents' );
    ?>

</div>
<div class="checkout-sidebar-info">
    <div>
        <strong>Subtotalt:</strong>
        <strong><?php wc_cart_totals_subtotal_html(); ?></strong>
    </div>
    <?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
    	<div class="cart-discount coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
    		<p><?php wc_cart_totals_coupon_label( $coupon ); ?>:</p>
    		<p><?php wc_cart_totals_coupon_html( $coupon ); ?></p>
    	</div>
    <?php endforeach; ?>
    <?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
    	<div class="fee">
    		<p><?php echo esc_html( $fee->name ); ?>:</p>
    		<p><?php wc_cart_totals_fee_html( $fee ); ?></p>
    	</div>
    <?php endforeach; ?>
    <?php if ( wc_tax_enabled() && 'excl' === WC()->cart->get_tax_price_display_mode() ) : ?>
    	<?php if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) : ?>
    		<?php foreach ( WC()->cart->get_tax_totals() as $code => $tax ) : ?>
    			<div class="tax-rate tax-rate-<?php echo sanitize_title( $code ); ?>">
    				<p><?php echo esc_html( $tax->label ); ?>:</p>
    				<p><?php echo wp_kses_post( $tax->formatted_amount ); ?></p>
    			</div>
    		<?php endforeach; ?>
    	<?php else : ?>
    		<div class="tax-total">
    			<p><?php echo esc_html( WC()->countries->tax_or_vat() ); ?>:</p>
    			<p><?php wc_cart_totals_taxes_total_html(); ?></p>
    		</div>
    	<?php endif; ?>
    <?php endif; ?>
    <?php do_action( 'woocommerce_review_order_before_order_total' ); ?>

    <div class="order-total">
    	<strong><?php _e( 'Total', 'woocommerce' ); ?>:</strong>
    	<strong><?php wc_cart_totals_order_total_html(); ?></strong>
    </div>

    <?php do_action( 'woocommerce_review_order_after_order_total' ); ?>
</div>