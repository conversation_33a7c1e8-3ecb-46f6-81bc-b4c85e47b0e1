<?php
/**
 * Checkout billing information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-billing.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 3.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/** @global WC_Checkout $checkout */

?>
	<?php if ( wc_ship_to_billing_address_only() && WC()->cart->needs_shipping() ) : ?>

		<!-- <h3><?php _e( 'Billing &amp; Shipping', 'woocommerce' ); ?></h3> -->

	<?php else : ?>

<!-- 		<h3><?php _e( 'Billing details', 'woocommerce' ); ?></h3>
 -->
	<?php endif; ?>

	<?php do_action( 'woocommerce_before_checkout_billing_form', $checkout ); ?>

<!-- 	<div class="woocommerce-billing-fields__field-wrapper"> -->
	<h2>Adress</h2>
	<div class="type-check type-of-service">
	    <div class="type-check-content" data-name="privat">
	        <span>Privatperson</span>
	    </div>
	    <div class="type-check-content" data-name="company">
	        <span>Företag</span>
	    </div>
	    <div class="type-check-content" data-name="catering">
	        <span>Catering</span>
	    </div>
	</div>
	<div id="billing-form-info" style="display: none;">
		<div style="margin-bottom:  50px;">
		    <label for="cone-is-offer" style="margin-right: 10px; cursor: pointer;">Endast Offert</label>
			<input type="checkbox" id="cone-is-offer" style="cursor: pointer;" />
		    <p><?php echo get_field('is-offer-info', 'option'); ?></p>
		</div>
		<?php
			$fields = $checkout->get_checkout_fields( 'billing' );

			foreach ( $fields as $key => $field ) {
				if ( isset( $field['country_field'], $fields[ $field['country_field'] ] ) ) {
					$field['country'] = $checkout->get_value( $field['country_field'] );
				}

				$cone_hidden = ( substr($key, 0, 4) == 'cone' ) ? true : false;

				if ( $key == 'billing_first_name' || $key == 'billing_company' || $key == 'billing_postcode' || $key == 'billing_address_2' || $key == 'billing_company_other_name' || $key == 'billing_order_code' ) {
				?>
					<div class="<?php if ($key != 'billing_address_2' || $key != 'billing_company_other_name' || $key != 'billing_order_code') echo 'two-inputs';?> <?php if ( $key == 'billing_company' || $key == 'billing_address_2' || $key == 'billing_company_other_name' || $key == 'billing_order_code') echo 'company-inputs'; ?>">
				<?php
				}
				?>

				<div class="checkout-input" <?php if ( $key == 'billing_customer_type' || $cone_hidden ) echo 'style="display: none;"'; ?>>
				    <label for="<?php echo $key; ?>"><?php echo $field['label']; if ( $field['required'] ) echo ' *'; ?></label>
				    <input type="text" class="<?php if( $field['required'] && ! $cone_hidden ) echo 'cone-required '; if ( $cone_hidden ) echo 'cone-required-2' ?> " name="<?php echo $key; ?>" id="<?php echo $key; ?>" value="<?php echo $checkout->get_value( $key ); ?>" />
				</div>
				<?php

				if ( $key == 'billing_last_name' || $key == 'billing_org_nr' || $key == 'billing_city' || $key == 'billing_address_2' || $key == 'billing_company_other_name' || $key == 'billing_order_code') {
				?>
					</div>
				<?php
				}

				//var_dump($field);
				//woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
			}
		?>

		<?php do_action( 'woocommerce_after_checkout_billing_form', $checkout ); ?>
		 <div class="checkout-continue checkout-continue-right checkout-continue-first">
		   <a href="#">Fortsätt</a>
		</div>
	</div>

<?php if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) : ?>
	<div class="woocommerce-account-fields">
		<?php if ( ! $checkout->is_registration_required() ) : ?>

			<p class="form-row form-row-wide create-account">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="createaccount" <?php checked( ( true === $checkout->get_value( 'createaccount' ) || ( true === apply_filters( 'woocommerce_create_account_default_checked', false ) ) ), true ) ?> type="checkbox" name="createaccount" value="1" /> <span><?php _e( 'Create an account?', 'woocommerce' ); ?></span>
				</label>
			</p>

		<?php endif; ?>

		<?php do_action( 'woocommerce_before_checkout_registration_form', $checkout ); ?>

		<?php if ( $checkout->get_checkout_fields( 'account' ) ) : ?>

			<div class="create-account">
				<?php foreach ( $checkout->get_checkout_fields( 'account' ) as $key => $field ) : ?>
					<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			</div>

		<?php endif; ?>

		<?php do_action( 'woocommerce_after_checkout_registration_form', $checkout ); ?>
	</div>
<?php endif; ?>
