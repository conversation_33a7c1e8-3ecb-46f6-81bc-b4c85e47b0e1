<?php
/**
 * Thankyou page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/thankyou.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     8.1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
//die( var_dump($order->meta_data) );
?>

<div class="checkout-header">
    <div class="max-width checkout-header-content">
        <a href="<?php echo esc_url(home_url()); ?>">
            <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/aladdins-uthyrning.jpg' ) ); ?>">
        </a>
        <div class="checkout-header-stages">
            <div class="checkout-header-stage checkout-step-1">
                <div class="checkout-header-number">1</div>
                <span>Faktura<br/>adress</span>
            </div>
            <div class="checkout-header-stage  checkout-step-2">
                <div class="checkout-header-number ">2</div>
                <span>Leverans<br/>metod</span>
            </div>
            <div class="checkout-header-stage active-checkout-stage checkout-step-3">
                <div class="checkout-header-number">3</div>
                <span><?php if ( get_post_meta($order->get_id(), 'cone_is_offer', true) === 'false' ) : ?>Order<br/>bekräftelse<?php else: ?>Offert<br/>mottagen</span><?php endif; ?>
            </div>
        </div>
        <h5>Trygga leveranser sedan 1986</h5>
    </div>
</div>


	<?php if ( $order ) : ?>

		<?php if ( $order->has_status( 'failed' ) ) : ?>

			<p class="woocommerce-notice woocommerce-notice--error woocommerce-thankyou-order-failed"><?php _e( 'Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please attempt your purchase again.', 'woocommerce' ); ?></p>

			<p class="woocommerce-notice woocommerce-notice--error woocommerce-thankyou-order-failed-actions">
				<a href="<?php echo esc_url( $order->get_checkout_payment_url() ); ?>" class="button pay"><?php _e( 'Pay', 'woocommerce' ) ?></a>
				<?php if ( is_user_logged_in() ) : ?>
					<a href="<?php echo esc_url( wc_get_page_permalink( 'myaccount' ) ); ?>" class="button pay"><?php _e( 'My account', 'woocommerce' ); ?></a>
				<?php endif; ?>
			</p>

		<?php else : 
			$count = 0;
			foreach ($order->get_items() as $key => $item) {
				$count += $item['quantity'];
			}
			$days = '-' . get_option( 'options_close_order' ) . ' days';
		?>

			<?php //var_dump($order->meta_data); ?>
			
			<section class="checkout-wrapper">
			    <div class="checkout-section confirm-section max-width">
				    <div class="checkout-left confirm-left">
				        <h2><?php echo ( get_post_meta($order->get_id(), 'cone_is_offer', true) === 'false' ) ? 'Order #' : 'Offertförfrågan #'; ?><?php echo $order->get_order_number(); ?> mottagen</h2>
				        <div class="confirm-content">
				            <div class="confirm-icon">
				                <i class="material-icons">check</i>
				            </div>
				            <?php if ( get_post_meta($order->get_id(), 'cone_is_offer', true) === 'false' ) : ?>
								<?php echo get_field('thank_you_text', 'option') ; ?>
								<p>Du har även möjlighet att ändra din order fram tills <?php echo get_option( 'options_close_order' ); ?> arbetsdagar innnan hyresdagen genom att granska ordern på ditt konto</p>
					            <a href="<?php echo esc_url( $order->get_view_order_url() ); ?>">Mitt konto</a>
					        <?php else: ?>
					        	<p>Vi skickar inom kort en offert till dig</p>
					       <?php endif; ?>
				        </div>
				    </div>
				    <div class="confirm-sidebar">
				        <h4>Order summering</h4>
				        <h5><?php echo $count; ?><?php echo ($count == 1) ? ' artikel' : ' artiklar';  ?></h5>
				        <div class="confirm-grid">
				            <p>Leveransdatum</p>
				            <p><?php echo date('d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_order_date', true))). ' (kl. ' . get_post_meta($order->get_id(), 'cone_billing_get_products_time', true).')'; ?></p>
				        </div>
				        <div class="confirm-grid">
				            <p>Returdatum</p>
				            <p><?php echo date('d.m.Y', strtotime(get_post_meta($order->get_id(), 'cone_billing_return_date', true))). ' (kl. ' . get_post_meta($order->get_id(), 'cone_billing_return_products_time', true).')'; ?></p>
				        </div>
				        <div class="confirm-grid">
				            <p>Leverans</p>
				            <p><?php echo ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) == 'customer' ) ? 'Kund hämtar': 'Aladdin levererar'; ?></p>
				        </div>
				        <div class="confirm-grid">
				            <p>Retur</p>
				            <p><?php echo get_post_meta($order->get_id(), 'cone_billing_return_products', true) == 'customer' ? 'Kund returnerar': 'Aladdin hämtar'; ?></p>
				        </div>
				        <div class="confirm-grid">
				            <p>Betalning</p>
				            <p><?php echo $order->get_payment_method_title(); ?> <?php if ( get_post_meta($order->get_id(), 'invoice_email', true) == true ) : ?> (e-post till <?php echo get_post_meta($order->get_id(), 'invoice_email_address', true); ?>)<?php endif; ?></p>
				        </div>
				        <div class="confirm-grid">
				            <p>Order totalt inkl. moms</p>
				            <strong><?php echo $order->get_formatted_order_total(); ?></strong>
				        </div>
				        <div class="confirm-grid">
				            <strong>Kostnad ovan är för en hyresperiod, om långhyra är bokad (flera hyresperioder) - kommer hyreskostnaden framgå av orderbekräftelse som skickas
separat till dig efter att vi behandlat denna order.
							<?php if ( get_post_meta($order->get_id(), 'billing_customer_type', true) == 'Företag' ) :  ?>
								Transportkostnad tillkommer om transport bokats.</strong>
							<?php else : ?>
								Om transport bokats, kommer den kostnaden också redovisas på orderbekräftelsen.</strong>
							<?php endif; ?>
				        </div>
				        <div class="confirm-grid-content">
				            <strong>Din information</strong>
				            <p>Email: <?php echo $order->get_billing_email(); ?></p>
				            <p>Telefon: <?php echo $order->get_billing_phone(); ?></p>
				        </div>
				        <?php if ( get_post_meta($order->get_id(), 'cone_billing_get_products', true) != 'customer' || get_post_meta($order->get_id(), 'cone_billing_return_products', true) != 'customer' ) : ?>
					        <div class="confirm-grid-content">
					            <strong>Leverans adress</strong>
					            <p><?php echo $order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name(); ?></p>
					            <p><?php echo $order->get_shipping_address_1(); ?></p>
					            <p><?php echo $order->get_shipping_postcode() . ' ' . $order->get_shipping_city(); ?></p>
					        </div>
				    	<?php endif; ?>
				        <div class="confirm-grid-content">
				            <strong>Support hjälp?</strong>
				            <a href="mailto:<?php echo get_field('aladdin-mail', 'option') ; ?>"><?php echo get_field('aladdin-mail', 'option') ; ?></a>
				            <p><?php echo get_field('aladdin-phone', 'option') ; ?></p>
				        </div>
				    </div>
			    </div>
			</section>

		<?php endif; ?>

		<?php //do_action( 'woocommerce_thankyou_' . $order->get_payment_method(), $order->get_id() ); ?>
		<?php //do_action( 'woocommerce_thankyou', $order->get_id() ); ?>

	<?php else : ?>

		<p class="woocommerce-notice woocommerce-notice--success woocommerce-thankyou-order-received"><?php echo apply_filters( 'woocommerce_thankyou_order_received_text', __( 'Thank you. Your order has been received.', 'woocommerce' ), null ); ?></p>

	<?php endif; ?>

	<section class="big-icon-section max-width">
	    <h2>Behöver du hjälp? Vi finns här för dig!</h2>
	    <div class="big-icons">
	        <div class="big-icons-content">
	            <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Card.png' ) ); ?>">
	            <span>Aladdins Uthyrning till undsättning! Här är lite olika sätt att <a href="<?php echo esc_url(home_url( '/kontakt' ) ); ?>">kontakta oss</a> på</span>
	        </div>
	        <div class="big-icons-content">
	            <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Call.png' ) ); ?>">
	            <span>Tala med oss direkt (Telefontid helgfri: mån-fre 09-12 samt 13-20.) <br/> på <?php echo get_field('aladdin-phone', 'option') ; ?></span>
	        </div>
	        <div class="big-icons-content">
	            <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/Contact.png' ) ); ?>">
	            <span>Vi älskar emails! <a href="mailto:<?php echo get_field('aladdin-mail', 'option') ; ?>">Maila oss</a> vad du än har för frågor angående vad som helst</span>
	        </div>
	    </div>
	</section>