<?php
/**
 * Checkout Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see 	    https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     9.4.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

?>
<div class="checkout-header">
    <div class="max-width checkout-header-content">
        <a href="<?php echo esc_url(home_url()); ?>">
            <img src="<?php echo esc_url(home_url( '/wp-content/themes/aladdin/assets/images/aladdins-uthyrning.jpg' ) ); ?>">
        </a>
        <div class="checkout-header-stages" data-days="<?php echo get_field('days-delay', 'options'); ?>">
            <div class="checkout-header-stage active-checkout-stage checkout-step-1">
                <div class="checkout-header-number">1</div>
                <span>Faktura<br/>adress</span>
            </div>
            <div class="checkout-header-stage  checkout-step-2">
                <div class="checkout-header-number ">2</div>
                <span>Leverans<br/>metod</span>
            </div>
            <div class="checkout-header-stage  checkout-step-3">
                <div class="checkout-header-number">3</div>
                <span>Order<br/>bekräftelse</span>
            </div>
        </div>
        <h5>Trygga leveranser sedan 1986</h5>
    </div>
</div>

<?php

wc_print_notices();

do_action( 'woocommerce_before_checkout_form', $checkout );

// If checkout registration is disabled and not logged in, the user cannot checkout
if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() ) {
	echo apply_filters( 'woocommerce_checkout_must_be_logged_in_message', __( 'You must be logged in to checkout.', 'woocommerce' ) );
	return;
}

?>

<section class="checkout-wrapper">
    <div class="checkout-section max-width">
        <div class="checkout-left">
			<form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">

				<?php if ( $checkout->get_checkout_fields() ) : ?>

					<?php do_action( 'woocommerce_checkout_before_customer_details' ); ?>

					<div id="step1">
						<?php do_action( 'woocommerce_checkout_billing' ); ?>
					</div>
					<div id="step2" style="display: none;">
						<?php do_action( 'woocommerce_checkout_shipping' ); ?>
					</div>
	
					<?php do_action( 'woocommerce_checkout_after_customer_details' ); ?>

				<?php endif; ?>

			</form>

			<?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>

		</div>
		<div class="checkout-sidebar">

			<div class="checkout-sidebar-headline">
			    <i class="material-icons checkout-sidebar-toggle">close</i>
			    <h4>Ordersummering</h4>
			    <a href="<?php echo wc_get_cart_url(); ?>">Redigera</a>
			</div>
			<?php do_action( 'woocommerce_checkout_before_order_review' ); ?>
			<div class="checkout-sidebar-show">
				<?php do_action( 'woocommerce_checkout_order_review' ); ?>
			</div>

			<?php do_action( 'woocommerce_checkout_after_order_review' ); ?>

		</div>
	</div>
</section>
