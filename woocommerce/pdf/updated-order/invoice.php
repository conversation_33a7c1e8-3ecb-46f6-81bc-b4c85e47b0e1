<?php do_action( 'wpo_wcpdf_before_document', $this->type, $this->order ); ?>

<?php
	$i12without = 0;
	$i12with = 0;
	$i25without = 0;
	$i25with = 0; 
?>
<style>
	.order-details th{
		color: black !important;
		border: 1px solid;
		width: 100%;
	}
	.order-details-body td{
		border: 1px solid;
		width: 100%;
	}

	.order-details tfoot{
		color: black !important;
		border: 1px solid;
		width: 100%;
	}
	.order-details table{
		border-collapse: collapse;
	}

	.order-details td{
		/*position: relative;*/
	}
 
	.order-details-footer td{
		color: black !important;
		border: 1px solid;
		width: 100%;
	}
	.no-borders td{
		border: none !important;
	}

	.removed-row td{
		position: relative;	
	}
	
	.removed-row td:before {
		content: '';
		position: absolute;
		left: -5px;
		top: 14px;
		height: 1px;
		width: 142%;
		background: red;
	}

	.added-row {
		position: relative;
	}


	.added-row td {
		font-weight: 700;
		position: relative;
	}

	.added-row td:first-child:before {
		content: '+';
		position: absolute;
		left: -15px;
		top: 6px;
		color: black;
		font-size: 18px;
	}

	.original-qty{
		position: relative;
	}


	.original-qty:before{
		position: absolute;
		content: '';
		width: 10px;
		top: 10px;
		left:-1px;
		height: 1px;
		background: black;
	}

	.new-qty{
		font-weight: bold;
	}

	.order-data th {
		font-weight: bold !important;
	}

</style>

<table class="head container">
	<tr>
		<td class="header">
		<?php
		if( $this->has_header_logo() ) {
			$this->header_logo();
		} else {
			echo apply_filters( 'wpo_wcpdf_invoice_title', __( 'Bokning', 'woocommerce-pdf-invoices-packing-slips' ) );
		}
		?>
		</td>
		<td class="shop-info">
			<img height="50" src="<?php echo home_url( '/wp-content/themes/aladdin/assets/images/aladdins-uthyrning.jpg' ); ?>" />
		</td>
	</tr>
</table>

<h1 class="document-type-label">
<?php if( $this->has_header_logo() ) echo apply_filters( 'wpo_wcpdf_invoice_title', __( 'Invoice', 'woocommerce-pdf-invoices-packing-slips' ) ); ?>
</h1>

<?php do_action( 'wpo_wcpdf_after_document_label', $this->type, $this->order ); ?>

<table class="order-data-addresses">
	<tr>
		<td class="address billing-address">
			<!-- <h3><?php _e( 'Billing Address:', 'woocommerce-pdf-invoices-packing-slips' ); ?></h3> -->
			<?php $this->billing_address(); ?>
			<?php if ( isset($this->settings['display_email']) ) { ?>
			<div class="billing-email"><?php $this->billing_email(); ?></div>
			<?php } ?>
			<?php if ( isset($this->settings['display_phone']) ) { ?>
			<div class="billing-phone"><?php $this->billing_phone(); ?></div>
			<?php } ?>
		</td>
		<td class="address shipping-address">
			<?php if ( isset($this->settings['display_shipping_address']) && $this->ships_to_different_address()) { ?>
			<h3><?php _e( 'Ship To:', 'woocommerce-pdf-invoices-packing-slips' ); ?></h3>
			<?php $this->shipping_address(); ?>
			<?php } ?>
		</td>
		<td class="order-data">
			<table>
				<?php do_action( 'wpo_wcpdf_before_order_data', $this->type, $this->order ); ?>
				<tr class="order-date">
					<th><?php _e( 'Hyresdatum:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php echo date('F d, Y',strtotime($this->get_custom_field('cone_billing_order_date'))); ?></td>
				</tr>
				<?php if ( isset($this->settings['display_number']) ) { ?>
				<tr class="order-number">
					<th><?php _e( 'Bokningsnummer:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->order_number(); ?></td>
				</tr>
				<?php } ?>
				<?php if ( isset($this->settings['display_date']) ) { ?>
				<tr class="invoice-date">
					<th><?php _e( 'Bokningsdatum:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->invoice_date(); ?></td>
				</tr>
				<?php } ?>
				<tr class="payment-method">
					<th><?php _e( 'Betalningsmetod:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->payment_method(); ?></td>
				</tr>
				<?php do_action( 'wpo_wcpdf_after_order_data', $this->type, $this->order ); ?>
			</table>			
		</td>
	</tr>
</table>

<!--  TEST MIKAEL T.A. BÅNG  -->

<table class="order-data-addresses">
	<tr>
		<td class="order-data">
			<table>
				<tr>
					<th><?php _e( 'Kundtyp:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('billing_customer_type'); ?></td>
				</tr>
				<?php if ( $this->get_custom_field('billing_customer_type') !== 'Privatperson' ) : ?>
					<tr>
						<th><?php _e( 'Företagsnamn:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
						<td><?php $this->custom_field('billing_company'); ?></td>
					</tr>
					<tr>
						<th><?php _e( 'Org nr:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
						<td><?php $this->custom_field('billing_org_nr'); ?></td>
					</tr>
					<tr>
						<th><?php _e( 'Annat företagsnamn :', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
						<td><?php $this->custom_field('billing_company_other_name'); ?></td>
					</tr>
					<tr>
						<th><?php _e( 'Märkning på beställningen:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
						<td><?php $this->custom_field('billing_order_code'); ?></td>
					</tr>
				<?php endif; ?>
				<tr>
					<th><?php _e( 'Levererar:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php echo $this->get_custom_field('cone_billing_get_products') === 'customer' ? 'Kund' : 'Aladdin' ; ?>, <?php echo date('dmY',strtotime($this->get_custom_field('cone_billing_order_date'))); ?> <?php $this->custom_field('cone_billing_get_products_time'); ?>. </td>
				</tr>
				<tr>
					<th><?php _e( 'Ring ca 30 min innan leverans:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php echo $this->get_custom_field('cone_billing_call_before_delivery') == '' ? 'Nej' : 'Ja'; ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Returnerar:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php echo $this->get_custom_field('cone_billing_return_products') === 'customer' ? 'Kund' : 'Aladdin'; ?>, <?php echo date('dmY',strtotime($this->get_custom_field('cone_billing_return_date'))); ?> <?php $this->custom_field('cone_billing_return_products_time'); ?>. </td>
				</tr>
				<tr>
					<th><?php _e( 'Ring ca 30 min innan returnering:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php echo $this->get_custom_field('cone_billing_call_before_return') == '' ? 'Nej' : 'Ja'; ?></td>
				</tr>
				<tr>
			</table>
		</td>
		<td class="order-data">
			<table>
					<th><?php _e( 'Lokaltyp:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_venue_type'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Stadsdel:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_county'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Våningsplan:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_floor'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Portkod:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_doorcode'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Antal trappsteg före entré/hiss:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_steps_to_elevator'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Storlek på hiss - max antal pers:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_elevator_size'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Bärsträcka(meter):', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_carry_distance'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Ev annan kontakt:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_other_contact'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Ev annan kontakt tel:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_other_contact_phone'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Övriga kommentarer:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->shipping_notes(); ?></td>
				</tr>
			</table>
		</td>
	</tr>
</table>

<!-- Slut test -->

<?php do_action( 'wpo_wcpdf_before_order_details', $this->type, $this->order ); ?>

<table class="order-details">
	<thead style="background: white !important; border-bottom: 2px solid">
		<tr>
			<th class="quantity"><?php _e('Antal', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
			<th class="quantity"><?php _e('Art.nr', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
			<th class="product"><?php _e('Produkt', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
			<th class="price">St pris exkl 12% moms</th>
			<th class="price">St pris exkl 25% moms</th>
			<th class="price">Summa denna art. exkl 12% moms</th>
			<th class="price">Summa denna art. exkl 25% moms</th>
			<th class="price">Summa denna art. inkl 12% moms</th>
			<th class="price">Summa denna art. inkl 25% moms</th>
		</tr>
	</thead>
	<tbody class="order-details-body">
		<?php $items = $this->get_order_items(); if( sizeof( $items ) > 0 ) : foreach( $items as $item_id => $item ) :
			//die(var_dump($item));
			$cone_class = '';
			$qty = false;
			if ( wc_get_order_item_meta($item_id, '_cone_changed_item') != '' && (wc_get_order_item_meta($item_id, '_cone_changed_item') == 'removed' || wc_get_order_item_meta($item_id, '_cone_changed_item') == 'added') ) {
				$cone_class = wc_get_order_item_meta($item_id, '_cone_changed_item') . '-row';
			}else{
				$qty = wc_get_order_item_meta($item_id, '_cone_changed_item');
			}

			$quantity = ( $qty ) ? '<p class="changed-qty"><span class="original-qty">'. explode(',', $qty)[1] .'</span> <span class="new-qty"> '.explode(',', $qty)[2].'</span></p>' :  $item['quantity'];
			//$quantity = $qty;

		?>
		<tr class="<?php echo apply_filters( 'wpo_wcpdf_item_row_class', $item_id, $this->type, $this->order, $item_id ); ?> <?php echo $cone_class; ?>">
			<td class="quantity"><?php echo $quantity; ?></td>
			<td class="quantity"><?php if( !empty( $item['sku'] ) ) : ?><p><?php echo $item['sku']; ?></p><?php endif; ?></td>
			<td class="product">
				<?php $description_label = __( 'Description', 'woocommerce-pdf-invoices-packing-slips' ); // registering alternate label translation ?>
				<span class="item-name"><?php echo $item['name']; ?></span>
				<?php do_action( 'wpo_wcpdf_before_item_meta', $this->type, $item, $this->order  ); ?>
				<span class="item-meta"><?php echo $item['meta']; ?></span>
				<dl class="meta">
					<?php $description_label = __( 'SKU', 'woocommerce-pdf-invoices-packing-slips' ); // registering alternate label translation ?>
					<?php if( !empty( $item['weight'] ) ) : ?><dt class="weight"><?php _e( 'Weight:', 'woocommerce-pdf-invoices-packing-slips' ); ?></dt><dd class="weight"><?php echo $item['weight']; ?><?php echo get_option('woocommerce_weight_unit'); ?></dd><?php endif; ?>
				</dl>
				<?php do_action( 'wpo_wcpdf_after_item_meta', $this->type, $item, $this->order  ); ?>
			</td>
			<td class="exkl 12% price"><?php if ( $item['tax_rates'] == '12 %' )  echo $item['single_line_total']; ?></td>
			<td class="exkl 25% price"><?php if ( $item['tax_rates'] == '25 %' ) echo $item['single_line_total']; ?></td>
			<td class="summa med quantity 12% exkl price"><?php if ( $item['tax_rates'] == '12 %' ) echo $item['line_subtotal']; ?></td>
			<td class="summa med quantity 25% exkl price"><?php if ( $item['tax_rates'] == '25 %' ) echo $item['line_subtotal']; ?></td>
			<td class="summa med quantity 12% inkl price"><?php if ( $item['tax_rates'] == '12 %' ) echo $item['price']; ?></td>
			<td class="summa med quantity 25% inkl price"><?php if ( $item['tax_rates'] == '25 %' ) echo $item['price']; ?></td>
		</tr>
		<?php if ( $item['tax_rates'] == '12 %' ) {
			$i12without += wc_get_order_item_meta($item_id, '_line_total');
			$i12with += wc_get_order_item_meta($item_id, '_line_total') + wc_get_order_item_meta($item_id, '_line_tax');
		}else {
			$i25without += wc_get_order_item_meta($item_id, '_line_total');
			$i25with += wc_get_order_item_meta($item_id, '_line_total') + wc_get_order_item_meta($item_id, '_line_tax');
		} 
		?>
		<?php endforeach; endif; ?>
	</tbody>
	<tfoot class="order-details-footer">
		<tr>
			<td colspan="3">
				<table>
					<tr class="no-borders"><td>Summa exkl 12 % moms: <?php echo round($i12without, 0); ?></td></tr>
					<tr class="no-borders"><td>Summa exkl 25 % moms: <?php echo round($i25without, 0); ?></td></tr>
					<tr class="no-borders"><td>Fakturaavgift (endast företag):</td></tr>
					<tr class="no-borders"><td>Total exkl moms: <?php echo round($i12without + $i25without, 0); ?></td></tr>
				</table>
			</td>
			<td colspan="3">
				<table>
				<tr class="no-borders"><td>Summa inkl 12 % moms: <?php echo round($i12with, 0); ?></td></tr>
				<tr class="no-borders"><td>Summa inkl 25 % moms: <?php echo round($i25with, 0); ?></td></tr>
				<tr class="no-borders"><td>Fakturaavgift inkl 25% moms :</td></tr>
				<tr class="no-borders"><td>Total inkl moms: <?php echo round($i12with+$i25with, 0); ?></td></tr>
				</table>
			</td>
			<td colspan="3">
				<p class="no-borders" style="font-size: 16px"><b>Har du beställt transport så tillkommer transportkostnad.</b></p>
			</td>
		</tr>
	</tfoot>
</table>

<div>
	<p>Bokningen måste bekräftas av oss i ett separat mail innan den räknas som bekräftad, vilket normalt sker inom 24 timmar.</p>
	<p>Kontakta oss gärna på tel 08-664 20 00 om det tar längre tid.</p>
</div>

<?php do_action( 'wpo_wcpdf_after_order_details', $this->type, $this->order ); ?>

<?php if ( $this->get_footer() ): ?>
<div id="footer">
	<?php $this->footer(); ?>
</div><!-- #letter-footer -->
<?php endif; ?>
<?php do_action( 'wpo_wcpdf_after_document', $this->type, $this->order ); ?>
