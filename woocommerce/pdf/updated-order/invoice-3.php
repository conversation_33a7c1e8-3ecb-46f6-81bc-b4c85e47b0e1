<?php do_action( 'wpo_wcpdf_before_document', $this->type, $this->order ); ?>

<table class="head container">
	<tr>
		<td class="header">
		<?php
		if( $this->has_header_logo() ) {
			$this->header_logo();
		} else {
			echo apply_filters( 'wpo_wcpdf_invoice_title', __( 'Invoice', 'woocommerce-pdf-invoices-packing-slips' ) );
		}
		?>
		</td>
		<td class="shop-info">
			<div class="shop-name"><h3><?php $this->shop_name(); ?></h3></div>
			<div class="shop-address"><?php $this->shop_address(); ?></div>
		</td>
	</tr>
</table>

<h1 class="document-type-label">
<?php if( $this->has_header_logo() ) echo apply_filters( 'wpo_wcpdf_invoice_title', __( 'Invoice', 'woocommerce-pdf-invoices-packing-slips' ) ); ?>
</h1>

<?php do_action( 'wpo_wcpdf_after_document_label', $this->type, $this->order ); ?>

<table class="order-data-addresses">
	<tr>
		<td class="address billing-address">
			<!-- <h3><?php _e( 'Billing Address:', 'woocommerce-pdf-invoices-packing-slips' ); ?></h3> -->
			<?php $this->billing_address(); ?>
			<?php if ( isset($this->settings['display_email']) ) { ?>
			<div class="billing-email"><?php $this->billing_email(); ?></div>
			<?php } ?>
			<?php if ( isset($this->settings['display_phone']) ) { ?>
			<div class="billing-phone"><?php $this->billing_phone(); ?></div>
			<?php } ?>
		</td>
		<td class="address shipping-address">
			<?php if ( isset($this->settings['display_shipping_address']) && $this->ships_to_different_address()) { ?>
			<h3><?php _e( 'Ship To:', 'woocommerce-pdf-invoices-packing-slips' ); ?></h3>
			<?php $this->shipping_address(); ?>
			<?php } ?>
		</td>
		<td class="order-data">
			<table>
				<?php do_action( 'wpo_wcpdf_before_order_data', $this->type, $this->order ); ?>
				<?php if ( isset($this->settings['display_number']) ) { ?>
				<tr class="invoice-number">
					<th><?php _e( 'Invoice Number:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->invoice_number(); ?></td>
				</tr>
				<?php } ?>
				<?php if ( isset($this->settings['display_date']) ) { ?>
				<tr class="invoice-date">
					<th><?php _e( 'Invoice Date:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->invoice_date(); ?></td>
				</tr>
				<?php } ?>
				<tr class="order-number">
					<th><?php _e( 'Order Number:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->order_number(); ?></td>
				</tr>
				<tr class="order-date">
					<th><?php _e( 'Order Date:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->order_date(); ?></td>
				</tr>
				<tr class="payment-method">
					<th><?php _e( 'Payment Method:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->payment_method(); ?></td>
				</tr>
				<?php do_action( 'wpo_wcpdf_after_order_data', $this->type, $this->order ); ?>
			</table>			
		</td>
	</tr>
</table>

<!--  TEST MIKAEL T.A. BÅNG  -->

<table class="order-data-addresses">
	<tr>
		<td class="order-data">
			<table>
				<tr>
					<th><?php _e( 'Kundtyp:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('billing_customer_type'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Levererar:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('cone_billing_get_products'); ?>, den <?php $this->custom_field('cone_billing_order_date'); ?> kl <?php $this->custom_field('cone_billing_get_products_time'); ?>. </td>
				</tr>
				<tr>
					<th><?php _e( 'Returnerar:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('cone_billing_return_products'); ?>, den <?php $this->custom_field('cone_billing_return_date'); ?> kl <?php $this->custom_field('cone_billing_return_products_time'); ?>. </td>
				</tr>
				<tr>
					<th><?php _e( 'Lokaltyp:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_venue_type'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Stadsdel:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_county'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Våningsplan:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_floor'); ?></td>
				</tr>
			</table>
		</td>
		<td class="order-data">
			<table>
				<tr>
					<th><?php _e( 'Portkod:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_doorcode'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Steg till hiss:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_steps_to_elevator'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Storlek hiss:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_elevator_size'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Bärsträcka:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_carry_distance'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Ev annan kontakt:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_other_contact'); ?></td>
				</tr>
				<tr>
					<th><?php _e( 'Ev annan kontakt tel:', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
					<td><?php $this->custom_field('special_other_contact_phone'); ?></td>
				</tr>
			</table>
		</td>
	</tr>
</table>

<!-- Slut test -->

<?php do_action( 'wpo_wcpdf_before_order_details', $this->type, $this->order ); ?>

<table class="order-details">
	<thead>
		<tr>
			<th class="product"><?php _e('Product', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
			<th class="quantity"><?php _e('Quantity', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
			<th class="price"><?php _e('Price', 'woocommerce-pdf-invoices-packing-slips' ); ?></th>
		</tr>
	</thead>
	<tbody>
		<?php $items = $this->get_order_items(); if( sizeof( $items ) > 0 ) : foreach( $items as $item_id => $item ) : ?>
		<tr class="<?php echo apply_filters( 'wpo_wcpdf_item_row_class', $item_id, $this->type, $this->order, $item_id ); ?>">
			<td class="product">
				<?php $description_label = __( 'Description', 'woocommerce-pdf-invoices-packing-slips' ); // registering alternate label translation ?>
				<span class="item-name"><?php echo $item['name']; ?></span>
				<?php do_action( 'wpo_wcpdf_before_item_meta', $this->type, $item, $this->order  ); ?>
				<span class="item-meta"><?php echo $item['meta']; ?></span>
				<dl class="meta">
					<?php $description_label = __( 'SKU', 'woocommerce-pdf-invoices-packing-slips' ); // registering alternate label translation ?>
					<?php if( !empty( $item['sku'] ) ) : ?><dt class="sku"><?php _e( 'SKU:', 'woocommerce-pdf-invoices-packing-slips' ); ?></dt><dd class="sku"><?php echo $item['sku']; ?></dd><?php endif; ?>
					<?php if( !empty( $item['weight'] ) ) : ?><dt class="weight"><?php _e( 'Weight:', 'woocommerce-pdf-invoices-packing-slips' ); ?></dt><dd class="weight"><?php echo $item['weight']; ?><?php echo get_option('woocommerce_weight_unit'); ?></dd><?php endif; ?>
				</dl>
				<?php do_action( 'wpo_wcpdf_after_item_meta', $this->type, $item, $this->order  ); ?>
			</td>
			<td class="quantity"><?php echo $item['quantity']; ?></td>
			<td class="price"><?php echo $item['order_price']; ?></td>
		</tr>
		<?php endforeach; endif; ?>
	</tbody>
	<tfoot>
		<tr class="no-borders">
			<td class="no-borders">
				<div class="customer-notes">
					<?php do_action( 'wpo_wcpdf_before_customer_notes', $this->type, $this->order ); ?>
					<?php if ( $this->get_shipping_notes() ) : ?>
						<h3><?php _e( 'Customer Notes', 'woocommerce-pdf-invoices-packing-slips' ); ?></h3>
						<?php $this->shipping_notes(); ?>
					<?php endif; ?>
					<?php do_action( 'wpo_wcpdf_after_customer_notes', $this->type, $this->order ); ?>
				</div>				
			</td>
			<td class="no-borders" colspan="2">
				<table class="totals">
					<tfoot>
						<?php foreach( $this->get_woocommerce_totals() as $key => $total ) : ?>
						<tr class="<?php echo $key; ?>">
							<td class="no-borders"></td>
							<th class="description"><?php echo $total['label']; ?></th>
							<td class="price"><span class="totals-price"><?php echo $total['value']; ?></span></td>
						</tr>
						<?php endforeach; ?>
					</tfoot>
				</table>
			</td>
		</tr>
	</tfoot>
</table>

<?php do_action( 'wpo_wcpdf_after_order_details', $this->type, $this->order ); ?>

<?php if ( $this->get_footer() ): ?>
<div id="footer">
	<?php $this->footer(); ?>
</div><!-- #letter-footer -->
<?php endif; ?>
<?php do_action( 'wpo_wcpdf_after_document', $this->type, $this->order ); ?>
