<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $product;

// Ensure visibility
if ( empty( $product ) || ! $product->is_visible() ) {
	return;
}
?>

<div class="product-card swiper-slide" <?php post_class(); ?>>
	<div class="product-card-top">
		<a href="<?php echo get_permalink( get_the_ID() ); ?>" class="absolute-link"></a>
<!--		<div class="product-card-icons">-->
<!--			<i class="material-icons">favorite_border</i>-->
<!--		</div>-->
		<?php  echo $product->get_image('full') ; ?>
		<div class="product-card-excerpt">
			<p><?php echo $product->get_short_description() ; ?></p>
			<span class="cd-product-card-excerpt-link">Klicka för viktig info</span>
		</div>
	</div>
	<div class="product-card-content">
		<a href="<?php echo get_permalink( get_the_ID() ) ?>" class="absolute-link"></a>
		<p><?php the_title() ; ?></p>
		<?php if($product->get_sku()) : ?>
			<?php if ( !$product->is_type( 'variable' ) ) : ?>
				<span class="product-card-sku">SKU: <?php echo $product->get_sku(); ?></span>
			<?php endif; ?>
		<?php endif ; ?>
		<span class="cd-product-span"><?php echo $product->get_price_html(); ?></span>
	</div>
	<div class="product-card-add">
		<p class="<?php if ( $product->is_type( 'variable' ) || get_field('prompt') ) echo 'cd-variable-product'; ?>" data-link="<?php echo get_permalink( get_the_ID() ); ?>">Lägg till i varukorg</p>
		<?php if ( ! $product->is_type( 'variable' ) ) : ?>
			<div class="add-remove-card" style="display: none;">
			    <i class="material-icons">remove</i>
			    <?php woocommerce_quantity_input(array('input_value' => isset( $quantity ) ? $quantity : 1)) ; ?>
			    <i class="material-icons">add</i>
			</div>
			<a rel="nofollow" href="<?php echo esc_url( $product->add_to_cart_url() ); ?>" data-quantity="<?php echo esc_attr( isset( $quantity ) ? $quantity : 1 ); ?>" data-product_id="<?php echo esc_attr( $product->get_id() ); ?>" data-product_sku="<?php echo esc_attr( $product->get_sku() ); ?>" class="<?php echo esc_attr( isset( $class ) ? $class : 'bitt' ); ?> product_type_simple add_to_cart_button ajax_add_to_cart" style="display: none;">Lägg till</a>
		<?php endif; ?>
	</div>
</div>